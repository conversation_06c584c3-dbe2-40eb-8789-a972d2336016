package com.datascope.app.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ResourceBO implements Serializable {
    private String resourceCode;
    private String resourceName;
    private String majorCode;
    private String categoryCode;
    private String resourceStatus;
    private String systemCode;
}

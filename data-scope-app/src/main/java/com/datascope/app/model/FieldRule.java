package com.datascope.app.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 字段规则定义类
 */
@Setter
@Getter
@NoArgsConstructor
public class FieldRule {
    // SQL关键字列表，避免将这些误识别为表名或别名
    private static final Set<String> SQL_KEYWORDS = new HashSet<>(Arrays.asList(
        "select", "from", "where", "join", "inner", "outer", "left", "right", "on",
        "and", "or", "not", "in", "between", "like", "is", "null", "group", "order",
        "by", "having", "limit", "offset", "union", "except", "intersect"
    ));

    // Getters and Setters
    private String fieldName;        // 字段名称
    private String formType;         // 表单类型：date, date-range, text等

    public void setFuzzyMatch(Boolean fuzzyMatch) {
        isFuzzyMatch = Objects.isNull(fuzzyMatch) || fuzzyMatch;
    }

    public void setMultiSelect(Boolean multiSelect) {
        isMultiSelect = Objects.nonNull(multiSelect) && multiSelect;
    }

    private Boolean isFuzzyMatch;    // 是否支持模糊查询
    private boolean isRequired;      // 是否必填
    private Boolean isMultiSelect;   // 是否支持多选
    private String tableName;        // 表名，对于多表查询时使用
    private String tableAlias;       // 表别名，对于多表查询时使用

    public FieldRule(String fieldName, String formType, boolean isFuzzyMatch, boolean isRequired, boolean isMultiSelect) {
        this.fieldName = fieldName;
        this.formType = formType;
        this.isFuzzyMatch = isFuzzyMatch;
        this.isRequired = isRequired;
        this.isMultiSelect = isMultiSelect;
    }

    public FieldRule(String fieldName, String formType, boolean isFuzzyMatch, boolean isRequired, boolean isMultiSelect, String tableAlias) {
        this(fieldName, formType, isFuzzyMatch, isRequired, isMultiSelect);
        this.tableAlias = tableAlias;
    }

    public FieldRule(String fieldName, String formType, boolean isFuzzyMatch, boolean isRequired, boolean isMultiSelect, String tableName, String tableAlias) {
        this(fieldName, formType, isFuzzyMatch, isRequired, isMultiSelect);
        this.tableName = tableName;
        this.tableAlias = tableAlias;
    }

    /**
     * 获取完整的字段名称（包括表别名或表名，如果有）
     * @return 完整字段名称
     */
    public String getFullFieldName() {
        if (tableAlias != null && !tableAlias.isEmpty() && !SQL_KEYWORDS.contains(tableAlias.toLowerCase())) {
            return tableAlias + "." + fieldName;
        } else if (tableName != null && !tableName.isEmpty() && !SQL_KEYWORDS.contains(tableName.toLowerCase())) {
            // 如果没有表别名但有表名，且表名不是SQL关键字，直接使用表名
            return tableName + "." + fieldName;
        }
        return fieldName;
    }
}

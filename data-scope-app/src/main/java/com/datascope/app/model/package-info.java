/**
 * Domain Models Package
 *
 * Contains the domain entities and value objects that represent the core business concepts:
 * - DataSource: Represents external database connections and their metadata
 * - Schema/Table/Column: Represents database structure metadata
 * - Query: Represents user queries and their execution context
 * - RelationshipInfo: Represents inferred or defined relationships between tables
 *
 * This package follows the DDD principles, ensuring that domain objects encapsulate
 * both data and behavior, maintaining invariants and business rules.
 */
package com.datascope.app.model;

package com.datascope.app.mapper.typehandler;

import com.datascope.app.security.EncryptionServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis TypeHandler for automatically encrypting and decrypting password fields.
 * Uses AesGcmEncryptionService for the crypto operations.
 */
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(String.class)
public class EncryptedPasswordTypeHandler extends BaseTypeHandler<String> {

    // Use static field + setter injection to make the service available
    // as <PERSON><PERSON><PERSON><PERSON> creates TypeHandler instances itself, not via Spring context directly.

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        // Encrypt the password before setting it in the PreparedStatement
        log.warn("Encrypting password for column: {}", parameter);

        ps.setString(i, EncryptionServiceFactory.get("aes").encrypt(parameter));
        //ps.setString(i, parameter);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String encryptedValue = rs.getString(columnName);
        if (rs.wasNull() || encryptedValue == null || encryptedValue.isEmpty()) {
            return null;
        }
        return getString(encryptedValue);
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String encryptedValue = rs.getString(columnIndex);
        if (rs.wasNull() || encryptedValue == null || encryptedValue.isEmpty()) {
            return null;
        }
        return getString(encryptedValue);
    }

    private String getString(String encryptedValue) throws SQLException {
        try {
            return EncryptionServiceFactory.get("aes").decrypt(encryptedValue);
        } catch (Exception e) {
            log.warn("Failed to decrypt value for column. Returning original value. Error: {}", e.getMessage());
            return encryptedValue;
        }
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String encryptedValue = cs.getString(columnIndex);
        if (cs.wasNull() || encryptedValue == null || encryptedValue.isEmpty()) {
            return null;
        }
        return getString(encryptedValue);
    }
}

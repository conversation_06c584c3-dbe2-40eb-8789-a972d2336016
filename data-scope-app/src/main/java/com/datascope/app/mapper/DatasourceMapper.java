package com.datascope.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datascope.app.entity.Datasource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 数据源Mapper接口
 */
@Mapper
public interface DatasourceMapper extends BaseMapper<Datasource> {

    /**
     * Selects all datasources without applying the TypeHandler for the password field.
     * This is primarily used for the password migration tool.
     * Note: This retrieves the raw value stored in the database.
     *
     * @return A list of Datasource objects with potentially unencrypted passwords.
     */
    @Select("SELECT * FROM datasources")
    List<Datasource> selectAllWithoutTypeHandler();

    /**
     * Updates the password field directly without applying the TypeHandler.
     * This is used by the migration tool to store the newly encrypted password,
     * avoiding double encryption that would occur if the standard update methods were used.
     *
     * @param id       The ID of the datasource to update.
     * @param password The encrypted password string to store directly.
     * @return The number of rows affected.
     */
    @Update("UPDATE datasources SET password = #{password, typeHandler=com.datascope.app.mapper.typehandler.EncryptedPasswordTypeHandler} WHERE id = #{id}")
    int updateWithoutTypeHandler(@Param("id") String id, @Param("password") String password);
}

package com.datascope.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.entity.Integration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 集成管理Mapper接口
 */
@Mapper
public interface IntegrationMapper extends BaseMapper<Integration> {
    
    /**
     * 分页查询集成列表
     *
     * @param page 分页参数
     * @param name 名称过滤
     * @param type 类型过滤
     * @param status 状态过滤
     * @return 分页结果
     */
    IPage<Integration> selectIntegrationPage(Page<Integration> page, 
                                           @Param("name") String name,
                                           @Param("type") String type,
                                           @Param("status") String status);
} 
/**
 * Domain Repository Interfaces Package
 *
 * Contains repository interfaces that define the persistence contracts for domain models.
 * These interfaces are implemented by the infrastructure layer but are defined here to
 * maintain the domain layer's independence from implementation details.
 *
 * Key repositories:
 * - DataSourceRepository: Manages data source configurations and metadata
 * - SchemaRepository: Handles database schema metadata
 * - TableRepository: Manages table metadata and relationships
 * - QueryRepository: Handles query history and favorites
 *
 * Following the Repository Pattern, these interfaces abstract away the details of how
 * domain objects are persisted and retrieved, allowing the domain layer to focus on
 * business logic.
 */
package com.datascope.app.mapper;

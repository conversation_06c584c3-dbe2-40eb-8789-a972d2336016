package com.datascope.app.common.enums;

import lombok.Getter;

/**
 * 同步状态枚举
 */
@Getter
public enum SyncStatusEnum {
    
    /**
     * 未同步
     */
    NOT_SYNCED("NOT_SYNCED", "未同步"),
    
    /**
     * 同步中
     */
    SYNCING("SYNCING", "同步中"),
    
    /**
     * 同步成功
     */
    SUCCESS("SUCCESS", "同步成功"),
    
    /**
     * 同步失败
     */
    FAILED("FAILED", "同步失败"),
    
    /**
     * 部分同步成功
     */
    PARTIAL("PARTIAL", "部分同步成功"),
    
    /**
     * 同步已取消
     */
    CANCELLED("CANCELLED", "同步已取消");
    
    private final String code;
    private final String desc;
    
    SyncStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
} 
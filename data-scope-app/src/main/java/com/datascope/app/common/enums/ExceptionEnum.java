package com.datascope.app.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/07/20
 * @Description 错误码
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ExceptionEnum {

    //全局信息
    ERROR(-1, "系统异常,请稍后重试"),
    SUCCESS(200, "请求成功"),
    BUSINESS_ERROR(10000, "业务异常"),
    PARAM_ERROR(10001, "请求参数不正确:%s"),
    INSUFFICIENT_PERMISSIONS(10002, "权限不足"),
    BAD_REQUEST_TYPE(10003, "请求方式不正确"),
    FIELD_IS_NULL(10004, "字段不存在"),
    //业务信息
    OPERATION_FREQUENCY(20002, "操作频繁"),
    LOGIN(401, "未登录"),

    FORBIDDEN(403, "该用户无权限查询该页面"),

    PAGE_META_NOT_FOUND(10005, "页面元数据不存在"),


    ;
































    private Integer code;
    private String msg;

}

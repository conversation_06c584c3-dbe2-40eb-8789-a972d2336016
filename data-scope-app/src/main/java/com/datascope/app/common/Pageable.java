package com.datascope.app.common;

/**
 * 分页请求接口
 * 用于封装分页查询参数
 */
public interface Pageable {

    /**
     * 创建分页请求
     *
     * @param pageNumber 页码（从0开始）
     * @param pageSize   每页大小
     * @return 分页请求
     */
    static Pageable of(int pageNumber, int pageSize) {
        return PageRequest.of(pageNumber, pageSize);
    }

    /**
     * 创建第一页分页请求
     *
     * @param pageSize 每页大小
     * @return 分页请求
     */
    static Pageable ofSize(int pageSize) {
        return PageRequest.ofSize(pageSize);
    }

    /**
     * 创建无分页请求
     *
     * @return 无分页请求
     */
    static Pageable unpaged() {
        return PageRequest.unpaged();
    }

    /**
     * 获取页码
     *
     * @return 页码（从0开始）
     */
    int getPageNumber();

    /**
     * 获取每页大小
     *
     * @return 每页大小
     */
    int getPageSize();

    /**
     * 获取偏移量
     *
     * @return 偏移量
     */
    default long getOffset() {
        return (long) getPageNumber() * getPageSize();
    }
}

package com.datascope.app.common.enums;

import lombok.Getter;

/**
 * 授权类型
 *
 * <AUTHOR>
 */
@Getter
public enum AuthTypeEnum {

    /**
     * table
     */
    TABLE("TABLE", "TABLE"),

    /**
     * database
     */
    DATASOURCE("DATASOURCE", "DATASOURCE"),

    /**
     * SCHEMA
     */
    SCHEMA("SCHEMA", "SCHEMA"),

    /**
     * COLUMN
     */
    COLUMN("COLUMN", "FIELD");

    private final String code;
    private final String desc;

    AuthTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

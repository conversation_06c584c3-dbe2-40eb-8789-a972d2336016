package com.datascope.app.common.enums;

import lombok.Getter;

/**
 * 数据源类型枚举
 */
@Getter
public enum DataSourceTypeEnum {
    
    /**
     * MySQL数据库
     */
    MYSQL("MYSQL", "MySQL数据库"),
    
    /**
     * DB2数据库
     */
    DB2("DB2", "DB2数据库");
    
    private final String code;
    private final String desc;
    
    DataSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
} 
package com.datascope.app.common.enums;

import lombok.Getter;

/**
 * 数据源状态枚举
 */
@Getter
public enum DataSourceStatusEnum {
    
    /**
     * 在线
     */
    ONLINE("ONLINE", "在线"),
    
    /**
     * 离线
     */
    OFFLINE("OFFLINE", "离线"),
    
    /**
     * 错误
     */
    ERROR("ERROR", "错误");
    
    private final String code;
    private final String desc;
    
    DataSourceStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
} 
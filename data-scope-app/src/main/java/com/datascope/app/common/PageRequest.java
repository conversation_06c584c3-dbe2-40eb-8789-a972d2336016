package com.datascope.app.common;

/**
 * 分页请求实现类
 * 用于封装分页查询参数
 */
public class PageRequest implements Pageable {

    /**
     * 无分页请求实例
     */
    private static final PageRequest UNPAGED = new PageRequest(-1, Integer.MAX_VALUE, false);
    /**
     * 页码（从0开始）
     */
    private final int pageNumber;
    /**
     * 每页大小
     */
    private final int pageSize;
    /**
     * 是否分页
     */
    private final boolean paged;

    /**
     * 构造函数
     *
     * @param pageNumber 页码（从0开始）
     * @param pageSize   每页大小
     * @param paged      是否分页
     */
    private PageRequest(int pageNumber, int pageSize, boolean paged) {
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.paged = paged;
    }

    /**
     * 创建分页请求
     *
     * @param pageNumber 页码（从0开始）
     * @param pageSize   每页大小
     * @return 分页请求
     */
    public static PageRequest of(int pageNumber, int pageSize) {
        return new PageRequest(pageNumber, pageSize, true);
    }

    /**
     * 创建第一页分页请求
     *
     * @param pageSize 每页大小
     * @return 分页请求
     */
    public static PageRequest ofSize(int pageSize) {
        return new PageRequest(0, pageSize, true);
    }

    /**
     * 创建无分页请求
     *
     * @return 无分页请求
     */
    public static PageRequest unpaged() {
        return UNPAGED;
    }

    /**
     * 获取页码
     *
     * @return 页码（从0开始）
     */
    @Override
    public int getPageNumber() {
        return pageNumber;
    }

    /**
     * 获取每页大小
     *
     * @return 每页大小
     */
    @Override
    public int getPageSize() {
        return pageSize;
    }

    /**
     * 是否分页
     *
     * @return 是否分页
     */
    public boolean isPaged() {
        return paged;
    }

    /**
     * 是否不分页
     *
     * @return 是否不分页
     */
    public boolean isUnpaged() {
        return !paged;
    }

    /**
     * 获取下一页
     *
     * @return 下一页
     */
    public PageRequest next() {
        return new PageRequest(getPageNumber() + 1, getPageSize(), true);
    }

    /**
     * 获取上一页
     *
     * @return 上一页
     */
    public PageRequest previous() {
        return getPageNumber() == 0 ? this : new PageRequest(getPageNumber() - 1, getPageSize(), true);
    }

    /**
     * 获取第一页
     *
     * @return 第一页
     */
    public PageRequest first() {
        return new PageRequest(0, getPageSize(), true);
    }

    /**
     * 获取指定页码的分页请求
     *
     * @param pageNumber 页码（从0开始）
     * @return 分页请求
     */
    public PageRequest withPage(int pageNumber) {
        return new PageRequest(pageNumber, getPageSize(), true);
    }

    /**
     * 获取指定每页大小的分页请求
     *
     * @param pageSize 每页大小
     * @return 分页请求
     */
    public PageRequest withSize(int pageSize) {
        return new PageRequest(getPageNumber(), pageSize, true);
    }

    @Override
    public String toString() {
        return String.format("Page request [number: %d, size: %d, paged: %s]", pageNumber, pageSize, paged);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        PageRequest other = (PageRequest) obj;
        return pageNumber == other.pageNumber && pageSize == other.pageSize && paged == other.paged;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + pageNumber;
        result = prime * result + pageSize;
        result = prime * result + (paged ? 1231 : 1237);
        return result;
    }
}

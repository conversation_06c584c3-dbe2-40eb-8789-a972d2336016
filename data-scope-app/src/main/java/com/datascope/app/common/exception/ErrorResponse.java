package com.datascope.app.common.exception;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一错误响应
 */
@Data
public class ErrorResponse {
    private boolean sussecc = false;

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 错误详情
     */
    private Map<String, Object> details;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 构造错误响应
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public ErrorResponse(String code, String message) {
        this.code = code;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造错误响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param details 错误详情
     */
    public ErrorResponse(String code, String message, Map<String, Object> details) {
        this.code = code;
        this.message = message;
        this.details = details;
        this.timestamp = LocalDateTime.now();
    }
}

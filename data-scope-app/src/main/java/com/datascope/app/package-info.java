/**
 * Application Layer Package
 *
 * Contains the application services, controllers, and DTOs that implement use cases
 * by coordinating the domain layer. This layer provides the API interface and handles
 * request/response transformation.
 *
 * Key components:
 * - REST Controllers: Handle HTTP requests and responses
 * - DTOs: Data transfer objects for API input/output
 * - Request/Response models: Structured API contracts
 * - Application Services: Coordinate domain services for use cases
 * - Exception handlers: Global API error handling
 *
 * The application layer depends on both domain and infrastructure layers, orchestrating
 * them to fulfill business use cases while maintaining a clean separation of concerns.
 */
package com.datascope.app;

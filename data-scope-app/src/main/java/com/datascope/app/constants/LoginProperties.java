package com.datascope.app.constants;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025-04-14 15:46
 */
@Configuration
@Getter
@Slf4j
public class LoginProperties implements InitializingBean  {

    @Value("${uiaservice.url:http://yuia.service.boss.yp:30422/yuia-service-boss}")
    private String uiaserviceUrl;
    @Value("${uiaservice.inner.url:http://yuia.service.boss.yp:30422/yuia-service-boss}")
    private String uiaserviceInnerUrl;
    @Value("${callback.url:http://localhost:8082/BusinessSys}")
    private String callbackUrl;

    private static String TOKENURL = "http://yuia.service.boss.yp:30422/yuia-service-boss/auth/principal";
    public static  String LOGINURL = "http://yuia.service.boss.yp:30422/yuia-service-boss//sso/logout?callback=http://localhost:8082/BusinessSys";
    public static String UIASERVICE_MENU = "";


    @Override
    public void afterPropertiesSet() {
        LOGINURL = uiaserviceUrl + "/sso/login.htm";
        TOKENURL = uiaserviceInnerUrl + "/auth/principal";

        UIASERVICE_MENU = uiaserviceInnerUrl +"/sso/logout";
        log.info("统一身份认证 初始化完毕，TOKENURL[{}] LOGINURL[{}]", TOKENURL,LOGINURL);
    }

    public static String getTOKENURL() {
        return TOKENURL;
    }

    public static String getLOGINURL() {
        return LOGINURL;
    }

    public static String getUiaserviceMenu() {
        return UIASERVICE_MENU;
    }
}

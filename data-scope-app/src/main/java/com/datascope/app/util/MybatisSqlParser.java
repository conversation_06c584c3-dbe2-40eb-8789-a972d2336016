package com.datascope.app.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MyBatis SQL 解析工具类
 * 支持解析 MyBatis 风格的 if 标签、where 标签和 foreach 标签
 */
public class MybatisSqlParser {

    private static final Pattern IF_PATTERN = Pattern.compile("<if\\s+test=\"([^\"]+)\">\\s*([^<]+)\\s*</if>");
    private static final Pattern WHERE_PATTERN = Pattern.compile("<where>\\s*(.*?)\\s*</where>", Pattern.DOTALL);
    private static final Pattern FOREACH_PATTERN = Pattern.compile("<foreach\\s+collection=\"([^\"]+)\"\\s+item=\"([^\"]+)\"\\s+open=\"([^\"]+)\"\\s+separator=\"([^\"]+)\"\\s+close=\"([^\"]+)\">\\s*(.*?)\\s*</foreach>", Pattern.DOTALL);

    /**
     * 解析 MyBatis 风格的 SQL 语句
     *
     * @param sqlTemplate SQL 模板
     * @param params      参数Map
     * @return 解析后的 SQL 语句
     */
    public static String parseSql(String sqlTemplate, Map<String, Object> params) {
        if (sqlTemplate == null || sqlTemplate.isEmpty()) {
            return "";
        }

        // 先处理 where 标签
        sqlTemplate = processWhereTag(sqlTemplate, params);

        // 处理 foreach 标签
        sqlTemplate = processForeachTag(sqlTemplate, params);

        // 处理 if 标签
        StringBuilder result = new StringBuilder();
        Matcher matcher = IF_PATTERN.matcher(sqlTemplate);
        int lastEnd = 0;

        while (matcher.find()) {
            // 添加 if 标签之前的内容
            result.append(sqlTemplate.substring(lastEnd, matcher.start()));

            String condition = matcher.group(1);
            String content = matcher.group(2).trim();

            // 检查条件是否满足
            if (evaluateCondition(condition, params)) {
                result.append(content);
            }

            lastEnd = matcher.end();
        }

        // 添加最后一个 if 标签之后的内容
        if (lastEnd < sqlTemplate.length()) {
            result.append(sqlTemplate.substring(lastEnd));
        }

        return result.toString();
    }

    /**
     * 处理 where 标签
     * where 标签会自动去除第一个多余的 AND 或 OR
     */
    private static String processWhereTag(String sqlTemplate, Map<String, Object> params) {
        Matcher matcher = WHERE_PATTERN.matcher(sqlTemplate);
        if (matcher.find()) {
            String whereContent = matcher.group(1);
            // 处理 where 标签内的 if 标签
            whereContent = parseSql(whereContent, params);

            // 去除所有多余的空格
            whereContent = whereContent.trim();

            // 如果内容为空，则不添加 WHERE 关键字
            if (whereContent.isEmpty()) {
                return sqlTemplate.replace(matcher.group(0), "");
            }

            // 处理开头的 AND 或 OR（不区分大小写）
            whereContent = whereContent.replaceAll("(?i)^\\s*(and|or)\\s+", "");

            // 处理中间的重复 AND 或 OR（不区分大小写）
            whereContent = whereContent.replaceAll("(?i)\\s+(and|or)\\s+(and|or)\\s+", " AND ");

            // 如果处理后内容为空，则不添加 WHERE 关键字
            if (whereContent.trim().isEmpty()) {
                return sqlTemplate.replace(matcher.group(0), "");
            }

            return sqlTemplate.replace(matcher.group(0), "WHERE " + whereContent);
        }
        return sqlTemplate;
    }

    /**
     * 处理 foreach 标签
     * 支持 collection、item、open、separator、close 属性
     */
    private static String processForeachTag(String sqlTemplate, Map<String, Object> params) {
        Matcher matcher = FOREACH_PATTERN.matcher(sqlTemplate);
        if (matcher.find()) {
            String collection = matcher.group(1);
            String item = matcher.group(2);
            String open = matcher.group(3);
            String separator = matcher.group(4);
            String close = matcher.group(5);
            String content = matcher.group(6);

            // 获取集合对象
            Object collectionObj = params.get(collection);
            if (collectionObj == null || !(collectionObj instanceof List || collectionObj.getClass().isArray())) {
                return sqlTemplate.replace(matcher.group(0), "");
            }

            List<?> list;
            if (collectionObj instanceof List) {
                list = (List<?>) collectionObj;
            } else {
                List<Object> tempList = new ArrayList<>();
                Object[] array = (Object[]) collectionObj;
                for (Object obj : array) {
                    tempList.add(obj);
                }
                list = tempList;
            }

            if (list.isEmpty()) {
                return sqlTemplate.replace(matcher.group(0), "");
            }

            StringBuilder result = new StringBuilder();
            result.append(open);

            for (int i = 0; i < list.size(); i++) {
                Object itemObj = list.get(i);
                // 创建临时参数Map，包含当前项
                Map<String, Object> tempParams = new HashMap<>();
                tempParams.put(item, itemObj);

                // 处理内容中的 if 标签
                String itemContent = parseSql(content, tempParams);
                result.append(itemContent);

                if (i < list.size() - 1) {
                    result.append(separator);
                }
            }

            result.append(close);

            return sqlTemplate.replace(matcher.group(0), result.toString());
        }
        return sqlTemplate;
    }

    /**
     * 评估条件表达式
     * 支持的条件类型：
     * 1. != null - 参数不为空
     * 2. == null - 参数为空
     * 3. == 值 - 参数等于指定值
     * 4. != 值 - 参数不等于指定值
     * 5. > 值 - 参数大于指定值
     * 6. < 值 - 参数小于指定值
     * 7. >= 值 - 参数大于等于指定值
     * 8. <= 值 - 参数小于等于指定值
     * 9. and - 逻辑与
     * 10. or - 逻辑或
     */
    private static boolean evaluateCondition(String condition, Map<String, Object> params) {
        if (params == null) {
            return false;
        }

        // 处理 != null 条件
        if (condition.endsWith("!= null")) {
            String paramName = condition.substring(0, condition.indexOf("!")).trim();
            return params.containsKey(paramName) && params.get(paramName) != null;
        }

        // 处理 == null 条件
        if (condition.endsWith("== null")) {
            String paramName = condition.substring(0, condition.indexOf("=")).trim();
            return params.containsKey(paramName) && params.get(paramName) == null;
        }

        // 处理 == 值 条件
        if (condition.contains("==")) {
            String[] parts = condition.split("==");
            if (parts.length == 2) {
                String paramName = parts[0].trim();
                String expectedValue = parts[1].trim().replaceAll("'", "");

                if (!params.containsKey(paramName)) {
                    return false;
                }

                Object paramValue = params.get(paramName);
                if (paramValue == null) {
                    return false;
                }

                return paramValue.toString().equals(expectedValue);
            }
        }

        // 处理 != 值 条件
        if (condition.contains("!=")) {
            String[] parts = condition.split("!=");
            if (parts.length == 2) {
                String paramName = parts[0].trim();
                String expectedValue = parts[1].trim().replaceAll("'", "");

                if (!params.containsKey(paramName)) {
                    return false;
                }

                Object paramValue = params.get(paramName);
                if (paramValue == null) {
                    return false;
                }

                return !paramValue.toString().equals(expectedValue);
            }
        }

        // 处理 > 值 条件
        if (condition.contains(">")) {
            String[] parts = condition.split(">");
            if (parts.length == 2) {
                String paramName = parts[0].trim();
                String expectedValue = parts[1].trim().replaceAll("'", "");

                if (!params.containsKey(paramName)) {
                    return false;
                }

                Object paramValue = params.get(paramName);
                if (paramValue == null) {
                    return false;
                }

                try {
                    double value = Double.parseDouble(paramValue.toString());
                    double expected = Double.parseDouble(expectedValue);
                    return value > expected;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        // 处理 < 值 条件
        if (condition.contains("<")) {
            String[] parts = condition.split("<");
            if (parts.length == 2) {
                String paramName = parts[0].trim();
                String expectedValue = parts[1].trim().replaceAll("'", "");

                if (!params.containsKey(paramName)) {
                    return false;
                }

                Object paramValue = params.get(paramName);
                if (paramValue == null) {
                    return false;
                }

                try {
                    double value = Double.parseDouble(paramValue.toString());
                    double expected = Double.parseDouble(expectedValue);
                    return value < expected;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        // 处理 >= 值 条件
        if (condition.contains(">=")) {
            String[] parts = condition.split(">=");
            if (parts.length == 2) {
                String paramName = parts[0].trim();
                String expectedValue = parts[1].trim().replaceAll("'", "");

                if (!params.containsKey(paramName)) {
                    return false;
                }

                Object paramValue = params.get(paramName);
                if (paramValue == null) {
                    return false;
                }

                try {
                    double value = Double.parseDouble(paramValue.toString());
                    double expected = Double.parseDouble(expectedValue);
                    return value >= expected;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        // 处理 <= 值 条件
        if (condition.contains("<=")) {
            String[] parts = condition.split("<=");
            if (parts.length == 2) {
                String paramName = parts[0].trim();
                String expectedValue = parts[1].trim().replaceAll("'", "");

                if (!params.containsKey(paramName)) {
                    return false;
                }

                Object paramValue = params.get(paramName);
                if (paramValue == null) {
                    return false;
                }

                try {
                    double value = Double.parseDouble(paramValue.toString());
                    double expected = Double.parseDouble(expectedValue);
                    return value <= expected;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        // 处理 and 条件
        if (condition.contains(" and ")) {
            String[] parts = condition.split(" and ");
            boolean result = true;

            for (String part : parts) {
                if (!evaluateCondition(part.trim(), params)) {
                    result = false;
                    break;
                }
            }

            return result;
        }

        // 处理 or 条件
        if (condition.contains(" or ")) {
            String[] parts = condition.split(" or ");
            boolean result = false;

            for (String part : parts) {
                if (evaluateCondition(part.trim(), params)) {
                    result = true;
                    break;
                }
            }

            return result;
        }

        return false;
    }
}

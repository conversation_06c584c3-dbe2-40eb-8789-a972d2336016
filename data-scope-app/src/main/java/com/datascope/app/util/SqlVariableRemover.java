package com.datascope.app.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL变量移除工具类
 * 用于识别和移除SQL中的变量，保留常量部分，确保生成的SQL语法正确
 */
public class SqlVariableRemover {

    // 匹配 #{变量名} 格式的变量
    private static final Pattern MYBATIS_VARIABLE_PATTERN = Pattern.compile("#\\{([^{}]+)\\}");

    // 匹配 :变量名 格式的变量
    private static final Pattern COLON_VARIABLE_PATTERN = Pattern.compile(":(\\w+)");

    // 匹配 MyBatis if 标签
    private static final Pattern IF_TAG_PATTERN = Pattern.compile("<if\\s+test=\"([^\"]+)\">\\s*(.*?)\\s*</if>", Pattern.DOTALL);

    // 匹配 MyBatis where 标签
    private static final Pattern WHERE_TAG_PATTERN = Pattern.compile("<where>\\s*(.*?)\\s*</where>", Pattern.DOTALL);

    // 匹配条件表达式中的变量部分 (例如: column = #{value})
    private static final Pattern CONDITION_PATTERN = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s*(?:=|!=|<>|>|<|>=|<=|LIKE|like|IN|in|NOT IN|not in)\\s*(?:#\\{[^{}]+\\}|:[\\w]+)");

    // 匹配括号中的条件 (例如: (#{userId} IS NULL OR user_id LIKE CONCAT('%', #{userId}, '%')))
    private static final Pattern PARENTHESIS_CONDITION_PATTERN = Pattern.compile("\\(\\s*(?:#\\{[^{}]+\\}|:[\\w]+)\\s+IS\\s+NULL\\s+OR\\s+[^)]+\\)", Pattern.CASE_INSENSITIVE);

    // 匹配更复杂的括号条件模式，支持 (#{var} IS NULL OR column operation #{var}) 格式
    private static final Pattern COMPLEX_PARENTHESIS_PATTERN = Pattern.compile("\\(\\s*(?:#\\{[^{}]+\\}|:[\\w]+)\\s+IS\\s+NULL\\s+OR\\s+[^)]+\\)", Pattern.CASE_INSENSITIVE);

    // 匹配 BETWEEN 条件
    private static final Pattern BETWEEN_CONDITION_PATTERN = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s+BETWEEN\\s+(?:#\\{[^{}]+\\}|:[\\w]+)\\s+AND\\s+(?:#\\{[^{}]+\\}|:[\\w]+)", Pattern.CASE_INSENSITIVE);

    // 匹配函数中的变量 (例如: CONCAT('%', #{value}, '%'))
    private static final Pattern FUNCTION_VARIABLE_PATTERN = Pattern.compile("\\b\\w+\\s*\\([^()]*(?:#\\{[^{}]+\\}|:[\\w]+)[^()]*\\)");

    // 匹配 IN 条件中的变量
    private static final Pattern IN_CONDITION_PATTERN = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s+IN\\s+\\([^)]*(?:#\\{[^{}]+\\}|:[\\w]+)[^)]*\\)", Pattern.CASE_INSENSITIVE);

    // 匹配子查询中包含变量的 IN 条件 - 支持嵌套子查询
    private static final Pattern SUBQUERY_IN_PATTERN = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s+IN\\s+\\(SELECT[^()]*(?:#\\{[^{}]+\\}|:[\\w]+)[^()]*\\)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    // 匹配常量条件表达式 (例如: column = 'value')
    private static final Pattern CONSTANT_CONDITION_PATTERN = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s*(?:=|!=|<>|>|<|>=|<=|LIKE|like|IN|in|NOT IN|not in)\\s*(?:'[^']*'|\\d+(?:\\.\\d+)?|NULL|null|TRUE|true|FALSE|false)");

    /**
     * 检查SQL是否包含变量
     *
     * @param sql SQL语句
     * @return 如果包含变量返回true，否则返回false
     */
    public static boolean containsVariables(String sql) {
        if (sql == null || sql.isEmpty()) {
            return false;
        }

        // 字符串 like '%#{xxx}%' 中的变量应被视为变量
        Pattern variableInStringPattern = Pattern.compile("'[^']*(?:#\\{[^{}]+\\}|:[\\w]+)[^']*'");
        Matcher stringMatcher = variableInStringPattern.matcher(sql);
        if (stringMatcher.find()) {
            return true;
        }

        // 检查 #{变量名} 格式的变量
        Matcher mybatisMatcher = MYBATIS_VARIABLE_PATTERN.matcher(sql);
        if (mybatisMatcher.find()) {
            return true;
        }

        // 检查 :变量名 格式的变量
        Matcher colonMatcher = COLON_VARIABLE_PATTERN.matcher(sql);
        return colonMatcher.find();
    }

    /**
     * 移除字符串字面量中的内容
     * 将单引号或双引号中的内容替换为占位符，避免误判字符串中的变量语法
     *
     * @param sql SQL语句
     * @return 移除字符串字面量后的SQL
     */
    private static String removeStringLiterals(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 替换单引号字符串
        String result = sql;
        Pattern singleQuotePattern = Pattern.compile("'[^']*'");
        Matcher singleQuoteMatcher = singleQuotePattern.matcher(result);
        StringBuffer sb = new StringBuffer();

        while (singleQuoteMatcher.find()) {
            singleQuoteMatcher.appendReplacement(sb, "'__STRING_LITERAL__'");
        }
        singleQuoteMatcher.appendTail(sb);
        result = sb.toString();

        // 替换双引号字符串
        Pattern doubleQuotePattern = Pattern.compile("\"[^\"]*\"");
        Matcher doubleQuoteMatcher = doubleQuotePattern.matcher(result);
        sb = new StringBuffer();

        while (doubleQuoteMatcher.find()) {
            doubleQuoteMatcher.appendReplacement(sb, "\"__STRING_LITERAL__\"");
        }
        doubleQuoteMatcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 移除SQL中的变量，保留常量部分
     *
     * @param sql SQL语句
     * @return 移除变量后的SQL语句
     */
    public static String removeVariables(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 如果不包含变量，直接返回原SQL
        if (!containsVariables(sql)) {
            return sql;
        }

        // 处理字符串字面量中的假变量
        sql = processStringLiteralVariables(sql);

        // 处理 MyBatis 标签
        String processedSql = processMybatisTags(sql);

        // 处理括号中的条件 (#{userId} IS NULL OR user_id LIKE CONCAT('%', #{userId}, '%'))
        processedSql = processParenthesisConditions(processedSql);

        // 处理 BETWEEN 条件
        processedSql = processBetweenConditions(processedSql);

        // 处理函数中的变量
        processedSql = processFunctionVariables(processedSql);

        // 处理子查询中的 IN 条件
        processedSql = processSubqueryInConditions(processedSql);

        // 处理 IN 条件中的变量
        processedSql = processInConditions(processedSql);

        // 处理普通条件表达式
        processedSql = processConditions(processedSql);

        // 清理SQL，移除多余的AND、OR、WHERE等
        processedSql = cleanupSql(processedSql);

        return processedSql;
    }

    /**
     * 处理字符串字面量中的假变量
     * 识别并保留字符串中的 #{xxx} 或 :xxx 形式的内容
     *
     * @param sql SQL语句
     * @return 处理后的SQL
     */
    private static String processStringLiteralVariables(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 寻找单引号中的内容
        Pattern stringPattern = Pattern.compile("'([^']*?(?:#\\{[^{}]+\\}|:[\\w]+)[^']*?)'");
        Matcher matcher = stringPattern.matcher(sql);

        // 如果没有找到单引号中包含变量的情况，则直接返回
        if (!matcher.find()) {
            return sql;
        }

        // 重置匹配器并处理所有匹配
        matcher.reset();
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            // 完整匹配，包括引号
            String matchedString = matcher.group(0);
            // 不做任何替换，保留原样
            matcher.appendReplacement(sb, Matcher.quoteReplacement(matchedString));
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 处理MyBatis标签
     */
    private static String processMybatisTags(String sql) {
        // 处理 where 标签
        String processedSql = processWhereTag(sql);

        // 处理 if 标签
        processedSql = processIfTag(processedSql);

        return processedSql;
    }

    /**
     * 处理 where 标签
     */
    private static String processWhereTag(String sql) {
        Matcher matcher = WHERE_TAG_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String whereContent = matcher.group(1);

            // 处理 where 标签内的内容，但不保留 AND 连接词
            String processedContent = processWhereContent(whereContent);

            // 如果处理后的内容不为空，则保留 where 子句
            if (!processedContent.trim().isEmpty()) {
                matcher.appendReplacement(result, " WHERE " + processedContent);
            } else {
                // 如果处理后的内容为空，则移除整个 where 子句
                matcher.appendReplacement(result, "");
            }
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 处理 where 标签内的内容，但不保留 AND 连接词
     */
    private static String processWhereContent(String whereContent) {
        // 处理条件表达式
        Matcher conditionMatcher = CONDITION_PATTERN.matcher(whereContent);
        List<String> conditionsToRemove = new ArrayList<>();

        while (conditionMatcher.find()) {
            conditionsToRemove.add(conditionMatcher.group(0));
        }

        // 移除包含变量的条件
        String processedContent = whereContent;
        for (String condition : conditionsToRemove) {
            // 检查条件前后是否有 AND 或 OR
            Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern conditionAndPattern = Pattern.compile(Pattern.quote(condition) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
            Pattern conditionOrPattern = Pattern.compile(Pattern.quote(condition) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

            // 移除条件及其前面的 AND 或 OR
            processedContent = andPattern.matcher(processedContent).replaceAll(" ");
            processedContent = orPattern.matcher(processedContent).replaceAll(" ");

            // 移除条件及其后面的 AND 或 OR
            processedContent = conditionAndPattern.matcher(processedContent).replaceAll("");
            processedContent = conditionOrPattern.matcher(processedContent).replaceAll("");

            // 移除单独的条件
            processedContent = processedContent.replace(condition, "");
        }

        return processedContent.trim();
    }

    /**
     * 处理 if 标签
     */
    private static String processIfTag(String sql) {
        Matcher matcher = IF_TAG_PATTERN.matcher(sql);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            // 获取if标签内容
            String content = matcher.group(2);

            // 检查内容是否包含变量
            if (containsVariables(content)) {
                // 如果包含变量，则移除整个if标签及其内容
                matcher.appendReplacement(result, "");
            } else {
                // 如果不包含变量，则保留内容部分
                matcher.appendReplacement(result, content);
            }
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 处理括号中的条件
     */
    private static String processParenthesisConditions(String sql) {
        // 使用增强的括号条件处理方法
        return processComplexParenthesisConditions(sql);
    }

    /**
     * 增强版的括号条件处理方法，可以处理多个括号条件
     * 特别是处理 (#{var} IS NULL OR column operation #{var}) 格式的条件
     */
    private static String processComplexParenthesisConditions(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 更强大的括号条件匹配模式，可以处理多层括号和复杂条件
        Pattern pattern = Pattern.compile("\\([^()]*(?:#\\{[^{}]+\\}|:[\\w]+)[^()]*\\)", Pattern.CASE_INSENSITIVE);

        String processedSql = sql;
        boolean found = true;

        // 循环处理直到没有可以匹配的括号条件为止
        while (found) {
            Matcher matcher = pattern.matcher(processedSql);
            found = false;

            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                found = true;
                String condition = matcher.group(0);

                // 如果条件中包含变量，则完全移除
                if (containsVariables(condition)) {
                    matcher.appendReplacement(sb, "");
                } else {
                    matcher.appendReplacement(sb, condition);
                }
            }
            matcher.appendTail(sb);
            processedSql = sb.toString();

            // 清理和条件连接词
            processedSql = processedSql.replaceAll("\\s+AND\\s+AND\\s+", " AND ");
            processedSql = processedSql.replaceAll("\\s+OR\\s+OR\\s+", " OR ");
            processedSql = processedSql.replaceAll("\\s+AND\\s+OR\\s+", " AND ");
            processedSql = processedSql.replaceAll("\\s+OR\\s+AND\\s+", " OR ");

            // 清理 WHERE 后面直接跟连接词的情况
            processedSql = processedSql.replaceAll("(?i)WHERE\\s+AND", "WHERE");
            processedSql = processedSql.replaceAll("(?i)WHERE\\s+OR", "WHERE");

            // 处理连接词前后直接跟括号的情况
            processedSql = processedSql.replaceAll("(?i)AND\\s*$", "");
            processedSql = processedSql.replaceAll("(?i)OR\\s*$", "");
            processedSql = processedSql.replaceAll("(?i)^\\s*AND", "");
            processedSql = processedSql.replaceAll("(?i)^\\s*OR", "");
        }

        // 处理非括号条件表达式
        processedSql = processNonParenthesisConditions(processedSql);

        // 处理 WHERE 后没有条件的情况
        processedSql = processedSql.replaceAll("(?i)\\s*WHERE\\s*$", " ");

        // 特殊情况：在处理过程中，"WHERE" 和 "ORDER BY" 可能会被错误地合并为 "WHEREDER BY"
        processedSql = processedSql.replaceAll("(?i)WHEREDER BY", "ORDER BY");
        processedSql = processedSql.replaceAll("(?i)WHEREORDER BY", "ORDER BY");
        processedSql = processedSql.replaceAll("(?i)WHEREGROUP BY", "GROUP BY");
        processedSql = processedSql.replaceAll("(?i)WHEREHAVING", "HAVING");
        processedSql = processedSql.replaceAll("(?i)WHERELIMIT", "LIMIT");

        // 修复 WHERE 和 ORDER BY 之间的关系，确保 WHERE 后面直接跟 ORDER BY 的情况被正确处理
        processedSql = processedSql.replaceAll("(?i)\\s+WHERE\\s+ORDER", " ORDER");
        processedSql = processedSql.replaceAll("(?i)\\s+WHERE\\s+GROUP", " GROUP");
        processedSql = processedSql.replaceAll("(?i)\\s+WHERE\\s+HAVING", " HAVING");
        processedSql = processedSql.replaceAll("(?i)\\s+WHERE\\s+LIMIT", " LIMIT");

        // 处理可能出现的错误合并
        processedSql = processedSql.replaceAll("(?i)WHEREDER", "ORDER");
        processedSql = processedSql.replaceAll("(?i)WHEREGROUP", "GROUP");
        processedSql = processedSql.replaceAll("(?i)WHEREHAVING", "HAVING");
        processedSql = processedSql.replaceAll("(?i)WHERELIMIT", "LIMIT");

        return processedSql.trim();
    }

    /**
     * 处理非括号的条件表达式，如 column = #{value}
     */
    private static String processNonParenthesisConditions(String sql) {
        // 匹配非括号内的变量条件表达式
        Pattern pattern = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s*(?:=|!=|<>|>|<|>=|<=|LIKE|like|IN|in|NOT IN|not in|BETWEEN|between)\\s*(?:#\\{[^{}]+\\}|:[\\w]+)(?:\\s+AND\\s+(?:#\\{[^{}]+\\}|:[\\w]+))?", Pattern.CASE_INSENSITIVE);

        String processedSql = sql;
        Matcher matcher = pattern.matcher(processedSql);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "");
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 处理 BETWEEN 条件
     */
    private static String processBetweenConditions(String sql) {
        Matcher matcher = BETWEEN_CONDITION_PATTERN.matcher(sql);
        List<String> conditionsToRemove = new ArrayList<>();

        while (matcher.find()) {
            conditionsToRemove.add(matcher.group(0));
        }

        // 移除包含变量的 BETWEEN 条件
        String processedSql = sql;
        for (String condition : conditionsToRemove) {
            // 检查条件前后是否有 AND 或 OR
            Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern conditionAndPattern = Pattern.compile(Pattern.quote(condition) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
            Pattern conditionOrPattern = Pattern.compile(Pattern.quote(condition) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

            // 移除条件及其前面的 AND 或 OR
            processedSql = andPattern.matcher(processedSql).replaceAll(" ");
            processedSql = orPattern.matcher(processedSql).replaceAll(" ");

            // 移除条件及其后面的 AND 或 OR
            processedSql = conditionAndPattern.matcher(processedSql).replaceAll("");
            processedSql = conditionOrPattern.matcher(processedSql).replaceAll("");

            // 移除单独的条件
            processedSql = processedSql.replace(condition, "");
        }

        return processedSql;
    }

    /**
     * 处理函数中的变量
     */
    private static String processFunctionVariables(String sql) {
        Matcher matcher = FUNCTION_VARIABLE_PATTERN.matcher(sql);
        List<String> functionsToRemove = new ArrayList<>();

        while (matcher.find()) {
            functionsToRemove.add(matcher.group(0));
        }

        // 移除包含变量的函数
        String processedSql = sql;
        for (String function : functionsToRemove) {
            // 检查函数前后是否有 AND 或 OR
            Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(function), Pattern.CASE_INSENSITIVE);
            Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(function), Pattern.CASE_INSENSITIVE);
            Pattern functionAndPattern = Pattern.compile(Pattern.quote(function) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
            Pattern functionOrPattern = Pattern.compile(Pattern.quote(function) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

            // 移除函数及其前面的 AND 或 OR
            processedSql = andPattern.matcher(processedSql).replaceAll(" ");
            processedSql = orPattern.matcher(processedSql).replaceAll(" ");

            // 移除函数及其后面的 AND 或 OR
            processedSql = functionAndPattern.matcher(processedSql).replaceAll("");
            processedSql = functionOrPattern.matcher(processedSql).replaceAll("");

            // 移除单独的函数
            processedSql = processedSql.replace(function, "");
        }

        return processedSql;
    }

    /**
     * 处理子查询中的 IN 条件
     */
    private static String processSubqueryInConditions(String sql) {
        // 更强大的子查询匹配模式，可以处理更复杂的嵌套子查询
        Pattern pattern = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s+IN\\s+\\(SELECT[^()]*(?:[^()]|\\([^()]*\\))*\\)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

        Matcher matcher = pattern.matcher(sql);
        List<String> conditionsToRemove = new ArrayList<>();

        while (matcher.find()) {
            String condition = matcher.group(0);
            // 只有包含变量的子查询才需要移除
            if (containsVariables(condition)) {
                conditionsToRemove.add(condition);
            }
        }

        // 移除包含变量的子查询 IN 条件
        String processedSql = sql;
        for (String condition : conditionsToRemove) {
            // 检查条件前后是否有 AND 或 OR
            Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern conditionAndPattern = Pattern.compile(Pattern.quote(condition) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
            Pattern conditionOrPattern = Pattern.compile(Pattern.quote(condition) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

            // 移除条件及其前面的 AND 或 OR
            processedSql = andPattern.matcher(processedSql).replaceAll(" ");
            processedSql = orPattern.matcher(processedSql).replaceAll(" ");

            // 移除条件及其后面的 AND 或 OR
            processedSql = conditionAndPattern.matcher(processedSql).replaceAll("");
            processedSql = conditionOrPattern.matcher(processedSql).replaceAll("");

            // 移除单独的条件，注意不要留下不完整的关键字
            processedSql = processedSql.replaceAll(Pattern.quote(condition), "");

            // 修复可能的 "column IN" 残留问题
            processedSql = processedSql.replaceAll("(?i)(\\w+(?:\\.\\w+)*)\\s+IN\\s+(?!\\().{0,10}", "");
        }

        return processedSql;
    }

    /**
     * 处理 IN 条件中的变量
     */
    private static String processInConditions(String sql) {
        Matcher matcher = IN_CONDITION_PATTERN.matcher(sql);
        List<String> conditionsToRemove = new ArrayList<>();

        while (matcher.find()) {
            conditionsToRemove.add(matcher.group(0));
        }

        // 移除包含变量的 IN 条件
        String processedSql = sql;
        for (String condition : conditionsToRemove) {
            // 检查条件前后是否有 AND 或 OR
            Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern conditionAndPattern = Pattern.compile(Pattern.quote(condition) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
            Pattern conditionOrPattern = Pattern.compile(Pattern.quote(condition) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

            // 移除条件及其前面的 AND 或 OR
            processedSql = andPattern.matcher(processedSql).replaceAll(" ");
            processedSql = orPattern.matcher(processedSql).replaceAll(" ");

            // 移除条件及其后面的 AND 或 OR
            processedSql = conditionAndPattern.matcher(processedSql).replaceAll("");
            processedSql = conditionOrPattern.matcher(processedSql).replaceAll("");

            // 移除单独的条件
            processedSql = processedSql.replace(condition, "");

            // 修复可能的 "column IN" 残留问题
            processedSql = processedSql.replaceAll("(?i)(\\w+(?:\\.\\w+)*)\\s+IN\\s+(?!\\().{0,10}", "");
        }

        return processedSql;
    }

    /**
     * 处理条件表达式
     */
    private static String processConditions(String sql) {
        // 处理条件表达式前预处理SQL
        String processedSql = sql;

        // 移除字符串字面量，避免处理字符串中的变量
        String tempSql = removeStringLiterals(processedSql);

        // 在临时SQL上查找条件
        Matcher conditionMatcher = CONDITION_PATTERN.matcher(tempSql);
        List<String> conditionsToRemove = new ArrayList<>();
        Map<Integer, String> positionMap = new HashMap<>();

        while (conditionMatcher.find()) {
            int start = conditionMatcher.start();
            int end = conditionMatcher.end();

            // 获取原始SQL中对应的条件文本
            String originalCondition = processedSql.substring(start, end);

            // 检查是否在字符串字面量中，如果是则跳过
            if (!isInStringLiteral(processedSql, start)) {
                conditionsToRemove.add(originalCondition);
                positionMap.put(start, originalCondition);
            }
        }

        // 从后向前移除条件，以避免位置偏移
        List<Integer> positions = new ArrayList<>(positionMap.keySet());
        Collections.sort(positions, Collections.reverseOrder());

        for (int pos : positions) {
            String condition = positionMap.get(pos);

            // 检查条件前后是否有 AND 或 OR
            Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(condition), Pattern.CASE_INSENSITIVE);
            Pattern conditionAndPattern = Pattern.compile(Pattern.quote(condition) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
            Pattern conditionOrPattern = Pattern.compile(Pattern.quote(condition) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

            // 移除条件及其前面的 AND 或 OR
            processedSql = andPattern.matcher(processedSql).replaceAll(" ");
            processedSql = orPattern.matcher(processedSql).replaceAll(" ");

            // 移除条件及其后面的 AND 或 OR
            processedSql = conditionAndPattern.matcher(processedSql).replaceAll("");
            processedSql = conditionOrPattern.matcher(processedSql).replaceAll("");

            // 移除单独的条件
            processedSql = processedSql.replace(condition, "");
        }

        return processedSql;
    }

    /**
     * 检查给定位置是否在字符串字面量内
     *
     * @param sql SQL语句
     * @param position 检查位置
     * @return 如果在字符串字面量内则返回true，否则返回false
     */
    private static boolean isInStringLiteral(String sql, int position) {
        // 查找所有单引号字符串
        Pattern singleQuotePattern = Pattern.compile("'[^']*'");
        Matcher matcher = singleQuotePattern.matcher(sql);

        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();

            // 如果位置在字符串范围内，则返回true
            if (position >= start && position < end) {
                return true;
            }
        }

        // 查找所有双引号字符串
        Pattern doubleQuotePattern = Pattern.compile("\"[^\"]*\"");
        matcher = doubleQuotePattern.matcher(sql);

        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();

            // 如果位置在字符串范围内，则返回true
            if (position >= start && position < end) {
                return true;
            }
        }

        return false;
    }

    /**
     * 清理SQL，移除多余的关键字和空白
     */
    private static String cleanupSql(String sql) {
        // 替换多个空格为单个空格
        String cleanedSql = sql.replaceAll("\\s+", " ").trim();

        // 处理字符串字面量中的变量，使他们不会被后续规则误处理
        cleanedSql = processStringLiteralWithVariables(cleanedSql);

        // 修复常见的错误合并，如"WHEREDER BY"变成"ORDER BY"
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREDER BY", "ORDER BY");
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREORDER BY", "ORDER BY");
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREGROUP BY", "GROUP BY");
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREHAVING", "HAVING");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERELIMIT", "LIMIT");

        // 修复不完整的 IN 子句
        cleanedSql = cleanedSql.replaceAll("(?i)\\s+(\\w+(?:\\.\\w+)*)\\s+IN\\s+(?!\\()\\s*", " ");
        cleanedSql = cleanedSql.replaceAll("(?i)\\s+IN\\s+(?!\\()\\s*", " ");

        // 处理多余的括号，但要小心不要影响关键字
        cleanedSql = cleanedSql.replaceAll("\\(\\s*\\)", "");

        // 处理 WHERE 后面直接跟 AND 或 OR 的情况
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+AND", "WHERE");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+OR", "WHERE");

        // 修复 "WHERE AND" 的特殊情况
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+AND\\s+", "WHERE ");

        // 处理 WHERE 后面没有条件的情况
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s*$", "");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+ORDER", "ORDER");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+GROUP", "GROUP");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+HAVING", "HAVING");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERE\\s+LIMIT", "LIMIT");

        // 处理连续的 AND 或 OR
        cleanedSql = cleanedSql.replaceAll("(?i)AND\\s+AND", "AND");
        cleanedSql = cleanedSql.replaceAll("(?i)OR\\s+OR", "OR");

        // 处理末尾的 AND 或 OR
        cleanedSql = cleanedSql.replaceAll("(?i)AND\\s*$", "");
        cleanedSql = cleanedSql.replaceAll("(?i)OR\\s*$", "");

        // 处理 AND 或 OR 后面跟 ORDER BY, GROUP BY 等的情况
        cleanedSql = cleanedSql.replaceAll("(?i)AND\\s+ORDER", "ORDER");
        cleanedSql = cleanedSql.replaceAll("(?i)OR\\s+ORDER", "ORDER");
        cleanedSql = cleanedSql.replaceAll("(?i)AND\\s+GROUP", "GROUP");
        cleanedSql = cleanedSql.replaceAll("(?i)OR\\s+GROUP", "GROUP");

        // 处理多余的右括号，确保不会影响关键字
        cleanedSql = cleanedSql.replaceAll("\\)\\s+ORDER", " ORDER");
        cleanedSql = cleanedSql.replaceAll("\\)\\s+GROUP", " GROUP");
        cleanedSql = cleanedSql.replaceAll("\\)\\s+HAVING", " HAVING");
        cleanedSql = cleanedSql.replaceAll("\\)\\s+LIMIT", " LIMIT");
        cleanedSql = cleanedSql.replaceAll("\\)\\s*$", "");

        // 处理多余的空格
        cleanedSql = cleanedSql.replaceAll("\\s+", " ").trim();

        // 检查SQL中是否有WHERE子句，但没有实际的条件
        // 这种情况通常发生在所有条件都被移除后
        int whereIndex = cleanedSql.toUpperCase().indexOf(" WHERE ");
        if (whereIndex >= 0) {
            // 找到WHERE关键字后的第一个单词
            String afterWhere = cleanedSql.substring(whereIndex + 7).trim();

            // 如果WHERE后面直接跟着ORDER BY, GROUP BY等，或者没有任何内容，则移除WHERE
            if (afterWhere.isEmpty() ||
                    afterWhere.toUpperCase().startsWith("ORDER ") ||
                    afterWhere.toUpperCase().startsWith("GROUP ") ||
                    afterWhere.toUpperCase().startsWith("HAVING ") ||
                    afterWhere.toUpperCase().startsWith("LIMIT ")) {

                cleanedSql = cleanedSql.substring(0, whereIndex) + " " + afterWhere;
            }

            // 检查 "WHERE AND 常量条件" 的模式，移除多余的 AND
            int andIndex = afterWhere.toUpperCase().indexOf("AND ");
            if (andIndex == 0) {
                String afterAnd = afterWhere.substring(4).trim();
                cleanedSql = cleanedSql.substring(0, whereIndex) + " WHERE " + afterAnd;
            }
        }

        // 再次检查可能的错误合并
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREDER BY", "ORDER BY");
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREORDER BY", "ORDER BY");
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREGROUP BY", "GROUP BY");
        cleanedSql = cleanedSql.replaceAll("(?i)WHEREHAVING", "HAVING");
        cleanedSql = cleanedSql.replaceAll("(?i)WHERELIMIT", "LIMIT");

        // 处理常量条件之间的连接
        cleanedSql = processConstantConditions(cleanedSql);

        // 再次处理多余的空格
        cleanedSql = cleanedSql.replaceAll("\\s+", " ").trim();

        return cleanedSql;
    }

    /**
     * 处理包含在字符串字面值中的变量
     * 确保它们不被后续规则错误处理
     *
     * @param sql SQL语句
     * @return 处理后的SQL
     */
    private static String processStringLiteralWithVariables(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 查找包含变量的like子句
        Pattern likePattern = Pattern.compile("(\\w+(?:\\.\\w+)*)\\s+(?i)like\\s+'[^']*(?:#\\{[^{}]+\\}|:[\\w]+)[^']*'", Pattern.CASE_INSENSITIVE);
        Matcher matcher = likePattern.matcher(sql);

        // 如果有包含变量的like子句，需要处理
        if (matcher.find()) {
            // 移除整个LIKE条件及其前面的联接词
            String processedSql = sql;
            matcher.reset();

            while (matcher.find()) {
                String fullClause = matcher.group(0);

                // 检查条件前后是否有 AND 或 OR
                Pattern andPattern = Pattern.compile("\\s+AND\\s+" + Pattern.quote(fullClause), Pattern.CASE_INSENSITIVE);
                Pattern orPattern = Pattern.compile("\\s+OR\\s+" + Pattern.quote(fullClause), Pattern.CASE_INSENSITIVE);
                Pattern clauseAndPattern = Pattern.compile(Pattern.quote(fullClause) + "\\s+AND\\s+", Pattern.CASE_INSENSITIVE);
                Pattern clauseOrPattern = Pattern.compile(Pattern.quote(fullClause) + "\\s+OR\\s+", Pattern.CASE_INSENSITIVE);

                // 移除条件及其前面的 AND 或 OR
                processedSql = andPattern.matcher(processedSql).replaceAll(" ");
                processedSql = orPattern.matcher(processedSql).replaceAll(" ");

                // 移除条件及其后面的 AND 或 OR
                processedSql = clauseAndPattern.matcher(processedSql).replaceAll("");
                processedSql = clauseOrPattern.matcher(processedSql).replaceAll("");

                // 移除单独的条件
                processedSql = processedSql.replace(fullClause, "");
            }

            return processedSql;
        }

        return sql;
    }

    /**
     * 处理常量条件之间的连接
     * 确保常量条件之间有正确的 AND 或 OR 连接词
     */
    private static String processConstantConditions(String sql) {
        // 查找所有常量条件
        List<String> constantConditions = new ArrayList<>();
        Matcher constantMatcher = CONSTANT_CONDITION_PATTERN.matcher(sql);

        while (constantMatcher.find()) {
            constantConditions.add(constantMatcher.group(0));
        }

        // 如果只有一个或没有常量条件，直接返回
        if (constantConditions.size() <= 1) {
            return sql;
        }

        // 检查常量条件之间是否缺少连接词
        String processedSql = sql;
        for (int i = 0; i < constantConditions.size() - 1; i++) {
            String condition1 = constantConditions.get(i);
            String condition2 = constantConditions.get(i + 1);

            // 构建正则表达式，匹配两个条件之间没有连接词的情况
            Pattern noConnectorPattern = Pattern.compile(
                    Pattern.quote(condition1) + "\\s+" + Pattern.quote(condition2),
                    Pattern.CASE_INSENSITIVE
            );

            // 如果找到两个条件之间没有连接词，添加 AND 连接词
            Matcher noConnectorMatcher = noConnectorPattern.matcher(processedSql);
            if (noConnectorMatcher.find()) {
                processedSql = noConnectorMatcher.replaceAll(condition1 + " AND " + condition2);
            }
        }

        return processedSql;
    }
}

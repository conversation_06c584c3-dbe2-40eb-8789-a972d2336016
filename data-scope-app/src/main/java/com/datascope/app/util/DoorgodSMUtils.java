package com.datascope.app.util;

import com.yeepay.g3.utils.gmcrypt.utils.Constants;
import com.yeepay.g3.utils.gmcrypt.utils.SMUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 国密加解密
 * @date 2022/6/285:14 下午
 */
@Slf4j
public class DoorgodSMUtils {

    static {
        SMUtils.init();
    }

    public static String encode(Object message) {
        String value = String.valueOf(message);
        return StringUtils.isBlank(value) ? value : SMUtils.encrypt(value);
    }


    public static String decode(String message) {
        if (StringUtils.isBlank(message)) {
            return message;
        } else {
            try {
                return SMUtils.decrypt(message);
            } catch (Exception var3) {
                // 异常则返回原始内容
                return message;
            }
        }
    }

    public static String decodeByAppId(String message) {
        if (StringUtils.isBlank(message)) {
            return message;
        } else {
            try {
                return SMUtils.decryptByAppId(message, message.split(Constants.CIPHERTEXT_SPLIT_CHAR_REX)[0]);
            } catch (Exception var3) {
                // 异常则返回原始内容
                return message;
            }
        }
    }

    public static String calculateDigest(String message) {
        try {
            return StringUtils.isBlank(message) ? message : SMUtils.calculateDigest(message);
        } catch (Exception e) {
            log.info("DoorgodSMUtils calculateDigest is error , message : {} , error_info : ", message, e);
        }
        return message;
    }

}

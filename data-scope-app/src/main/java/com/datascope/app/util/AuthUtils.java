package com.datascope.app.util;

import com.yeepay.g3.core.yuia.yuiacommons.patron.subject.Subject;
import com.yeepay.g3.core.yuia.yuiacommons.patron.utils.SecurityUtils;
import com.yeepay.g3.core.yuia.yuiacommons.patronclient.UserPrincipal;

import java.util.Optional;

public class AuthUtils {

    /**
     * 获取当前登录用户名
     *
     * @return 用户名
     */
    public static String getUsername() {
        UserPrincipal user = (UserPrincipal) Optional.ofNullable(SecurityUtils.getSubject())
                .map(Subject::getPrincipal).orElse(null);
        return Optional.ofNullable(user).map(UserPrincipal::getUsername).orElse(null);
    }

    /**
     * 获取当前登录用户名
     *
     * @return String
     */
    public static String getLoginName() {
        UserPrincipal user = (UserPrincipal) Optional.ofNullable(SecurityUtils.getSubject())
            .map(Subject::getPrincipal).orElse(null);
        return Optional.ofNullable(user).map(UserPrincipal::getLoginName).orElse(null);
    }

}

package com.datascope.app.util;

import com.datascope.app.entity.Datasource;
import lombok.extern.slf4j.Slf4j;

/**
 * JDBC URL构建工具类
 * 用于统一构建各种数据库类型的JDBC URL
 */
@Slf4j
public class JdbcUrlBuilder {

    /**
     * 构建JDBC URL
     *
     * @param datasource 数据源实体
     * @return JDBC URL
     */
    public static String buildJdbcUrl(Datasource datasource) {
        if (datasource == null) {
            log.error("数据源为空，无法构建JDBC URL");
            throw new IllegalArgumentException("数据源不能为空");
        }

        String type = datasource.getType() != null ? datasource.getType().toLowerCase() : "";
        String host = datasource.getHost();
        Integer port = datasource.getPort();
        String database = datasource.getDatabaseName();
        String schema = datasource.getSchema();

        // 验证必要参数
        if (host == null || host.isEmpty()) {
            log.error("数据源主机为空，无法构建JDBC URL");
            throw new IllegalArgumentException("数据源主机不能为空");
        }
        if (port == null) {
            log.error("数据源端口为空，无法构建JDBC URL");
            throw new IllegalArgumentException("数据源端口不能为空");
        }
        if (database == null || database.isEmpty()) {
            log.error("数据源数据库名为空，无法构建JDBC URL");
            throw new IllegalArgumentException("数据源数据库名不能为空");
        }

        switch (type) {
            case "mysql":
                return String.format("**********************************************************************************",
                    host, port, database);
            case "postgresql":
                return String.format("jdbc:postgresql://%s:%d/%s", host, port, database);
            case "oracle":
                return String.format("**************************", host, port, database);
            case "sqlserver":
                return String.format("**************************************", host, port, database);
            case "db2":
                String baseUrl = String.format("jdbc:db2://%s:%d/%s", host, port, database);
                // 如果指定了schema，则添加到URL中
                if (schema != null && !schema.isEmpty()) {
                    baseUrl += ":currentSchema=" + schema + ";";
                }
                return baseUrl;
            default:
                log.error("不支持的数据库类型: {}", type);
                throw new IllegalArgumentException("不支持的数据库类型: " + type);
        }
    }

    /**
     * 根据数据源类型获取驱动类名
     *
     * @param type 数据库类型
     * @return 驱动类名
     */
    public static String getDriverClassName(String type) {
        if (type == null || type.isEmpty()) {
            throw new IllegalArgumentException("数据库类型不能为空");
        }

        switch (type.toLowerCase()) {
            case "mysql":
                return "com.mysql.cj.jdbc.Driver";
            case "postgresql":
                return "org.postgresql.Driver";
            case "oracle":
                return "oracle.jdbc.OracleDriver";
            case "sqlserver":
                return "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            case "db2":
                return "com.ibm.db2.jcc.DB2Driver";
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + type);
        }
    }
}

package com.datascope.app.util;

import com.datascope.app.security.GmEncryptionService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询结果解密工具类
 * 用于处理查询结果中的加密数据
 */
@Slf4j
@Component
public class QueryResultDecryptor {

    @Setter(onMethod_ = @Autowired)
    private GmEncryptionService gmCryptService;

    /**
     * 处理查询结果中的加密数据
     *
     * @param rows 查询结果数据行
     * @return 处理后的结果数据行
     */
    public List<Map<String, Object>> decryptQueryResult(List<Map<String, Object>> rows) {
        if (rows == null || rows.isEmpty()) {
            return rows;
        }

        List<Map<String, Object>> decryptedRows = new ArrayList<>(rows.size());

        for (Map<String, Object> row : rows) {
            Map<String, Object> decryptedRow = new HashMap<>(row.size());

            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 处理字符串类型的值
                if (value instanceof String) {
                    String strValue = (String) value;
                    // 判断是否为加密数据
                    if (gmCryptService.isEncryptedData(strValue)) {
                        // 解密数据
                        String decryptedValue = gmCryptService.decrypt(strValue);
                        decryptedRow.put(key, decryptedValue);
                        log.debug("解密字段 {}: {} -> {}", key,
                            strValue.substring(0, Math.min(10, strValue.length())) + "...",
                            decryptedValue.substring(0, Math.min(10, decryptedValue.length())) + "...");
                    } else {
                        decryptedRow.put(key, value);
                    }
                } else {
                    decryptedRow.put(key, value);
                }
            }

            decryptedRows.add(decryptedRow);
        }

        return decryptedRows;
    }
}

package com.datascope.app.util;

import com.datascope.app.dto.datasource.TestConnectionResultDTO;
import com.datascope.app.entity.Datasource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库连接测试工具类
 */
@Component
@Slf4j
public class DatabaseConnectionTester {

    /**
     * 测试数据库连接
     *
     * @param datasource 数据源实体
     * @return 连接测试结果
     */
    public TestConnectionResultDTO testConnection(Datasource datasource) {
        TestConnectionResultDTO resultDTO = new TestConnectionResultDTO();

        String jdbcUrl = buildJdbcUrl(datasource);
        long startTime = System.currentTimeMillis();

        try {
            // 加载驱动程序
            loadDriver(datasource.getType());

            // 尝试建立连接
            try (Connection connection = DriverManager.getConnection(
                    jdbcUrl, datasource.getUsername(), datasource.getPassword())) {

                // 获取数据库元数据
                DatabaseMetaData metaData = connection.getMetaData();
                long pingTime = System.currentTimeMillis() - startTime;

                // 连接成功，设置结果
                resultDTO.setSuccess(true);
                resultDTO.setMessage("连接成功");

                // 设置详细信息
                Map<String, Object> connectionInfo = new HashMap<>();
                connectionInfo.put("databaseType", metaData.getDatabaseProductName());
                connectionInfo.put("databaseVersion", metaData.getDatabaseProductVersion());
                connectionInfo.put("driverVersion", metaData.getDriverVersion());
                connectionInfo.put("pingTime", pingTime);
                resultDTO.setDetails(connectionInfo);
            }
        } catch (ClassNotFoundException e) {
            log.error("数据库驱动加载失败", e);
            resultDTO.setSuccess(false);
            resultDTO.setMessage("数据库驱动加载失败: " + e.getMessage());
        } catch (SQLException e) {
            log.error("数据库连接失败", e);
            resultDTO.setSuccess(false);
            resultDTO.setMessage("数据库连接失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("连接测试发生未知错误", e);
            resultDTO.setSuccess(false);
            resultDTO.setMessage("连接测试发生未知错误: " + e.getMessage());
        }

        return resultDTO;
    }

    /**
     * 根据数据源类型加载对应的数据库驱动
     */
    private void loadDriver(String databaseType) throws ClassNotFoundException {
        try {
            String driverClassName = JdbcUrlBuilder.getDriverClassName(databaseType);
            Class.forName(driverClassName);
        } catch (IllegalArgumentException e) {
            throw new ClassNotFoundException("不支持的数据库类型: " + databaseType);
        }
    }

    /**
     * 构建JDBC URL
     */
    private String buildJdbcUrl(Datasource datasource) {
        return JdbcUrlBuilder.buildJdbcUrl(datasource);
    }
}

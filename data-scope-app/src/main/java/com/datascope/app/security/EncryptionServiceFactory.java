/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.datascope.app.security;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/16 09:31
 */
public class EncryptionServiceFactory {

    private static final Map<String, EncryptionService> ENCRYPTION_SERVICE_MAP = new HashMap<>();

    public static void register(EncryptionService encryptionService) {
        ENCRYPTION_SERVICE_MAP.put(encryptionService.name(), encryptionService);
    }

    public static EncryptionService get(String name) {
        return ENCRYPTION_SERVICE_MAP.get(name);
    }
}

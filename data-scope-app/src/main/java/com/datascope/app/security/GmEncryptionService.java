package com.datascope.app.security;

import com.yeepay.g3.utils.gmcrypt.utils.SMUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 国密算法加解密服务
 * 使用 yp-gm-crypt 组件进行加解密操作
 */
@Slf4j
@Component
public class GmEncryptionService implements EncryptionService {

    @Override
    public String encrypt(String plainText) {
        throw new UnsupportedOperationException("加密方法未实现");
    }

    /**
     * 解密数据
     *
     * @param encryptedData 加密数据
     * @return 解密后的数据
     */
    public String decrypt(String encryptedData) {
        if (encryptedData == null || encryptedData.isEmpty()) {
            return encryptedData;
        }

        // 直接调用 SMUtils 进行解密，它会处理密钥和格式

        try {
            // 直接调用 SMUtils.decrypt
            String decryptedData = SMUtils.decrypt(encryptedData);

            // 可以在这里添加更详细的日志，但避免记录完整明文/密文
            log.debug("尝试解密数据: {}", encryptedData.substring(0, Math.min(10, encryptedData.length())) + "...");

            return decryptedData;
        } catch (Exception e) {
            log.error("使用 SMUtils 解密数据失败: {}", encryptedData.substring(0, Math.min(10, encryptedData.length())) + "...", e);
            // 解密失败时返回原始数据，符合文档要求的一种处理方式
            return encryptedData; // 或者可以根据业务需求返回特定错误标识或 null
        }
    }

    @Override
    public String name() {
        return "sm4";
    }

    /**
     * 判断数据是否为加密数据
     *
     * @param data 待检查的数据
     * @return 是否为加密数据
     */
    public boolean isEncryptedData(String data) {
        if (data == null || data.isEmpty()) {
            return false;
        }

        // 根据 yp-gm-crypt 文档，密文格式为 "密钥APPID$密钥节点标识$业务密文"
        // 一个简单的判断是检查是否包含两个 '$' 分隔符
        // 注意：这可能误判包含 '$' 的普通字符串，更精确的判断可能需要结合 APPID 列表等
        int firstDollar = data.indexOf('$');
        if (firstDollar > 0 && firstDollar < data.length() - 1) {
            int secondDollar = data.indexOf('$', firstDollar + 1);
            // 确保第二个 $ 存在且后面还有内容
            return secondDollar > firstDollar && secondDollar < data.length() - 1;
        }
        return false;
        // 或者，如果可以获取配置的 appId 列表，可以更精确地判断
        // return data.contains("$") && configuredAppIds.stream().anyMatch(appId -> data.startsWith(appId + "$"));
    }

    @PostConstruct
    public void register() {
        EncryptionServiceFactory.register(this);
    }

}

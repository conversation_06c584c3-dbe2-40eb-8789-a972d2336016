package com.datascope.app.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

@Component
public class AesGcmEncryptionService implements EncryptionService {

    private static final int GCM_IV_LENGTH = 12; // GCM recommended IV length
    private static final int GCM_TAG_LENGTH = 128; // GCM authentication tag length

    @Value("${datascope.security.aes-key}")
    private String aesKey;

    @Value("${datascope.security.salt}")
    private String salt;

    @Override
    public String encrypt(String plainText) {
        try {
            if (plainText == null || plainText.isEmpty()) {
                return plainText;
            }

            // 1. Generate random IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);

            // 2. Create GCM parameter spec
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);

            // 3. Get the secret key
            SecretKey secretKey = getAesKey();

            // 4. Initialize cipher for encryption
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);

            // 5. Add Associated Authenticated Data (AAD) using salt (optional but recommended)
            if (salt != null && !salt.isEmpty()) {
                cipher.updateAAD(salt.getBytes(StandardCharsets.UTF_8));
            }

            // 6. Encrypt the data
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 7. Combine IV and encrypted data (IV first)
            byte[] combined = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryptedData, 0, combined, iv.length, encryptedData.length);

            // 8. Base64 encode the combined array
            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            // Log the error appropriately in a real application
            throw new RuntimeException("Encryption failed", e);
        }
    }

    @Override
    public String decrypt(String encryptedText) {
        try {
            if (encryptedText == null || encryptedText.isEmpty()) {
                return encryptedText;
            }

            // 1. Base64 decode
            byte[] combined = Base64.getDecoder().decode(encryptedText);

            // 2. Extract the IV (first GCM_IV_LENGTH bytes)
            byte[] iv = new byte[GCM_IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, iv.length);

            // 3. Extract the actual encrypted data
            byte[] encryptedData = new byte[combined.length - GCM_IV_LENGTH];
            System.arraycopy(combined, GCM_IV_LENGTH, encryptedData, 0, encryptedData.length);

            // 4. Create GCM parameter spec
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);

            // 5. Get the secret key
            SecretKey secretKey = getAesKey();

            // 6. Initialize cipher for decryption
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);

            // 7. Add AAD (must match the AAD used during encryption)
            if (salt != null && !salt.isEmpty()) {
                cipher.updateAAD(salt.getBytes(StandardCharsets.UTF_8));
            }

            // 8. Decrypt the data
            byte[] decryptedData = cipher.doFinal(encryptedData);

            // 9. Convert back to string
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // Log the error appropriately
            // Consider specific exceptions like AEADBadTagException for integrity failures
            throw new RuntimeException("Decryption failed", e);
        }
    }

    @Override
    public String name() {
        return "aes";
    }

    /**
     * Generates a SecretKey from the configured AES key string.
     * Ensures the key is exactly 32 bytes (256 bits).
     *
     * @return The SecretKeySpec for AES.
     */
    private SecretKey getAesKey() {
        // Ensure the key is 32 bytes (256 bits) for AES-256
        byte[] keyBytes = new byte[32];
        byte[] configuredKeyBytes = aesKey.getBytes(StandardCharsets.UTF_8);

        // Copy the configured key, padding with zeros or truncating if necessary
        System.arraycopy(configuredKeyBytes, 0, keyBytes, 0, Math.min(configuredKeyBytes.length, keyBytes.length));

        return new SecretKeySpec(keyBytes, "AES");
    }

    @PostConstruct
    public void register() {
        EncryptionServiceFactory.register(this);
    }
}

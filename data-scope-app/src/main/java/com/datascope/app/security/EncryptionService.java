package com.datascope.app.security;

public interface EncryptionService {
    /**
     * Encrypts the given plaintext using AES/GCM/NoPadding.
     *
     * @param plainText The text to encrypt.
     * @return The Base64 encoded string containing IV and ciphertext, or the original text if null/empty.
     * @throws RuntimeException if encryption fails.
     */
    String encrypt(String plainText);

    /**
     * Decrypts the given Base64 encoded ciphertext (which includes IV).
     *
     * @param encryptedText The Base64 encoded string containing IV and ciphertext.
     * @return The original plaintext, or the input string if null/empty.
     * @throws RuntimeException if decryption fails (e.g., bad key, tampered data).
     */
    String decrypt(String encryptedText);

    String name();
}

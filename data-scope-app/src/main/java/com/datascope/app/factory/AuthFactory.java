package com.datascope.app.factory;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Component
public class AuthFactory implements ApplicationContextAware, InitializingBean {

    private static final Map<String, Supplier<AbstractAuthCenter>> MAP = new HashMap<>();

    private ApplicationContext applicationContext;

    public AbstractAuthCenter getAuth(String type) {
        Supplier<AbstractAuthCenter> p = MAP.get(type);
        if (p != null) {
            return p.get();
        }
        throw new RuntimeException("not support type: " + type);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        applicationContext.getBeansOfType(AbstractAuthCenter.class)
            .values().forEach(c -> MAP.put(c.getAuthType(), () -> c));
    }

    @Override
    public void setApplicationContext(@Nonnull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

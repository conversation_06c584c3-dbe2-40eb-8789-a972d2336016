package com.datascope.app.dto.datasource;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建数据源请求
 */
@Data
@Accessors(chain = true)
public class CreateDataSourceRequest {

    /**
     * 数据源名称
     */
    @NotBlank(message = "数据源名称不能为空")
    private String name;

    /**
     * 数据源描述
     */
    private String description;

    /**
     * 数据源类型：mysql, postgresql, oracle, sqlserver, mongodb, elasticsearch
     */
    @NotBlank(message = "数据源类型不能为空")
    private String type;

    /**
     * 主机地址
     */
    @NotBlank(message = "主机地址不能为空")
    private String host;

    /**
     * 端口号
     */
    @NotNull(message = "端口号不能为空")
    private Integer port;

    /**
     * 数据库名称
     */
    @NotBlank(message = "数据库名称不能为空")
    private String databaseName;

    /**
     * 数据库名称（兼容）
     */
    private String database;

    /**
     * Schema名称
     */
    private String schema;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 同步频率: manual, hourly, daily, weekly, monthly
     */
    @NotBlank(message = "同步频率不能为空")
    private String syncFrequency;

    /**
     * 连接参数
     */
    private Object connectionParams;

    /**
     * 加密类型: none, ssl, tls
     */
    private String encryptionType;

    /**
     * 加密选项
     */
    private String encryptionOptions;
}

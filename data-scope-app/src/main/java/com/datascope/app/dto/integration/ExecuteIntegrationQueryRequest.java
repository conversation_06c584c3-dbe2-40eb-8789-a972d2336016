package com.datascope.app.dto.integration;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 执行集成查询请求
 */
@Data
@Accessors(chain = true)
public class ExecuteIntegrationQueryRequest {

    /**
     * 集成ID
     */
    @NotBlank(message = "集成ID不能为空")
    private String integrationId;

    /**
     * 查询参数
     */
    private Object parameters;

    /**
     * 分页信息
     */
    private PaginationInfo pagination;

    /**
     * 分页信息
     */
    @Data
    @Accessors(chain = true)
    public static class PaginationInfo {
        /**
         * 页码
         */
        private Integer page = 1;

        /**
         * 每页大小
         */
        private Integer pageSize = 10;
    }
} 
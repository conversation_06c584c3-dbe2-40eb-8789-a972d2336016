package com.datascope.app.dto.metadata;

import com.datascope.app.entity.Column;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 授权DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "授权参数信息")
public class AuthDTO implements Serializable {

    /**
     * 授权类型, TABLE, COLUMN, DATASOURCE, SCHEMA
     */
    @Schema(description = "授权类型:TABLE, COLUMN, DATASOURCE, SCHEMA", example = "COLUMN")
    private String type;

    /**
     * 是否需要单独申请授权
     */
    @Schema(description = "是否需要授权: ture, false")
    private Boolean authRequired;

    /**
     * 主键id
     */
    @Schema(description = "行主键id")
    private String id;

    @Schema(hidden = true)
    private Datasource datasource;

    @Schema(hidden = true)
    private Column column;

    @Schema(hidden = true)
    private com.datascope.app.entity.Schema schema;

    @Schema(hidden = true)
    private Table table;

    @Schema(hidden = true)
    private String sql;

    @Schema(hidden = true)
    private String loginName;

}

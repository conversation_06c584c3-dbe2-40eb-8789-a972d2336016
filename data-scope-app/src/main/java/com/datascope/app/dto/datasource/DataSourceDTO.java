package com.datascope.app.dto.datasource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 数据源DTO，用于API响应
 */
@Data
@Accessors(chain = true)
public class DataSourceDTO {

    /**
     * 数据源ID
     */
    private String id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源描述
     */
    private String description;

    /**
     * 数据源类型：mysql, postgresql, oracle, sqlserver, mongodb, elasticsearch
     */
    private String type;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 数据库名称
     */
    private String databaseName;

    /**
     * 数据库名称（兼容）
     */
    private String database;

    /**
     * Schema名称
     */
    private String schema;

    /**
     * 用户名
     */
    private String username;

    /**
     * 状态: active, inactive, error, syncing
     */
    private String status;

    /**
     * 同步频率: manual, hourly, daily, weekly, monthly
     */
    private String syncFrequency;

    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSyncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 连接参数
     */
    private String connectionParams;

    /**
     * 加密类型: none, ssl, tls
     */
    private String encryptionType;

    /**
     * 加密选项
     */
    private String encryptionOptions;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 是否需要单独申请授权
     */
    private Boolean isAuthRequired;
}

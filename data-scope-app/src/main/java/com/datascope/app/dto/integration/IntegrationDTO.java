package com.datascope.app.dto.integration;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 集成信息DTO
 */
@Data
@Accessors(chain = true)
public class IntegrationDTO {

    /**
     * 集成ID
     */
    private String id;

    /**
     * 集成名称
     */
    private String name;

    /**
     * 集成描述
     */
    private String description;

    /**
     * 集成类型：SIMPLE_TABLE-简单表格，TABLE-表格，CHART-图表
     */
    private String type;

    /**
     * 状态：ACTIVE-活跃，INACTIVE-不活跃，DRAFT-草稿
     */
    private String status;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 查询参数
     */
    private List<CreateIntegrationRequest.QueryParam> queryParams;

    /**
     * 表格配置
     */
    private Object tableConfig;

    /**
     * 图表配置
     */
    private Object chartConfig;

    /**
     * 元数据
     */
    private Object meta;

    /**
     * 集成点信息
     */
    private IntegrationPointDTO integrationPoint;

    /**
     * 乐观锁
     */
    private Integer nonce;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
} 
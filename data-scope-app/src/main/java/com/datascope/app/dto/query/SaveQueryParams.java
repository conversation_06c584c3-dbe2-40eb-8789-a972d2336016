package com.datascope.app.dto.query;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 保存查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveQueryParams {

    /**
     * 查询ID（更新时需要）
     */
    private String id;

    /**
     * 查询名称
     */
    private String name;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * SQL语句
     */
    private String sql;

    /**
     * 查询描述
     */
    private String description;

    /**
     * 状态：DRAFT-草稿，PUBLISHED-已发布，DEPRECATED-已废弃，ARCHIVED-已归档
     */
    private String status;

    /**
     * 服务状态：ENABLED-启用，DISABLED-禁用
     */
    private String serviceStatus;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 是否公开
     */
    private Boolean isPublic;

    /**
     * 查询类型：SQL-SQL查询，NATURAL_LANGUAGE-自然语言查询
     */
    private String queryType;
} 
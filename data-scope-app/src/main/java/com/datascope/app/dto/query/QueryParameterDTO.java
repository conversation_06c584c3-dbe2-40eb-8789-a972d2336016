package com.datascope.app.dto.query;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

/**
 * 查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryParameterDTO {

    /**
     * 参数ID
     */
    private String id;

    /**
     * 参数名称
     */
    private String name;

    /**
     * 参数类型：string-字符串，number-数字，boolean-布尔值，date-日期
     */
    private String type;

    /**
     * 参数标签
     */
    private String label;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 选项列表
     */
    private List<?> options;
} 
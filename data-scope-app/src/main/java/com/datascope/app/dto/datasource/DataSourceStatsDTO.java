package com.datascope.app.dto.datasource;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 数据源统计信息DTO
 */
@Data
@Accessors(chain = true)
public class DataSourceStatsDTO {

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 表数量
     */
    private Integer tablesCount;

    /**
     * 视图数量
     */
    private Integer viewsCount;

    /**
     * 总行数
     */
    private Long totalRows;

    /**
     * 总大小
     */
    private String totalSize;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdate;

    /**
     * 查询次数
     */
    private Integer queriesCount;

    /**
     * 连接池大小
     */
    private Integer connectionPoolSize;

    /**
     * 活跃连接数
     */
    private Integer activeConnections;

    /**
     * 平均查询时间
     */
    private String avgQueryTime;

    /**
     * 总表数
     */
    private Integer totalTables;

    /**
     * 总视图数
     */
    private Integer totalViews;

    /**
     * 总查询数
     */
    private Integer totalQueries;
} 
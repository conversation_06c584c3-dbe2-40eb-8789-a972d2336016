package com.datascope.app.dto.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据库模式DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchemaDTO {

    /**
     * 模式ID
     */
    private String id;

    /**
     * 模式名称
     */
    private String name;

    /**
     * 模式描述
     */
    private String description;

    /**
     * 表数量
     */
    private Integer tablesCount;

    /**
     * 是否需要单独申请授权
     */
    private Boolean isAuthRequired;
}

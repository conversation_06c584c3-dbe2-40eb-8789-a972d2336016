package com.datascope.app.dto.query;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 执行查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteQueryParams {

    /**
     * 查询参数
     */
    private Map<String, Object> parameters;

    /**
     * 结果集限制条数
     */
    private Integer limit;

    /**
     * 结果集起始位置
     */
    private Integer offset;

    /**
     * 分页页码
     */
    private Integer page;

    /**
     * 分页大小
     */
    private Integer size;

    /**
     * SQL语句，用于编辑和创建查询时直接执行
     */
    private String sql;
    /**
     * 数据源ID
     */
    private String dataSourceId;

    private String sort;

}

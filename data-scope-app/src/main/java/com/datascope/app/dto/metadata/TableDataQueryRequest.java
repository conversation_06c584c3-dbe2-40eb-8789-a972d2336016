package com.datascope.app.dto.metadata;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@Schema(description = "表数据查询请求")
public class TableDataQueryRequest {

    @Schema(description = "页码，从1开始", example = "1")
    @Min(value = 1, message = "页码最小为1")
    private Integer page = 1;

    @Schema(description = "每页记录数", example = "10")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 1000, message = "每页记录数最大为1000")
    private Integer size = 10;
}

package com.datascope.app.dto.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableDTO {

    /**
     * 表ID
     */
    private String id;

    /**
     * 表名称
     */
    private String name;

    /**
     * 类型：TABLE-表，VIEW-视图
     */
    private String type;

    /**
     * 表描述
     */
    private String description;

    /**
     * 行数
     */
    private Long rowCount;

    /**
     * 列数
     */
    private Integer columnsCount;

    /**
     * 是否需要单独申请授权
     */
    private Boolean isAuthRequired;
}

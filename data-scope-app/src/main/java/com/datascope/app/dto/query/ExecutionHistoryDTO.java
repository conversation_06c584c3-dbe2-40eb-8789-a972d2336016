package com.datascope.app.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 执行历史DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionHistoryDTO {

    /**
     * 执行历史ID
     */
    private String id;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 执行人
     */
    private QueryDTO.UserInfo executedBy;

    /**
     * 执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date executedAt;

    /**
     * 状态：success-成功，error-错误，running-运行中，cancelled-已取消
     */
    private String status;

    /**
     * 执行时长(毫秒)
     */
    private BigDecimal duration;

    /**
     * 结果行数
     */
    private Integer rowCount;

    /**
     * 执行参数
     */
    private Object parameters;

    /**
     * 结果ID
     */
    private String resultId;
} 
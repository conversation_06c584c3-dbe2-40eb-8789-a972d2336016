package com.datascope.app.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询字段DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryFieldDTO {
    /**
     * 字段名称
     */
    private String name;

    /**
     * 字段类型
     */
    private String type;

    /**
     * 字段标签
     */
    private String label;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 是否加密列
     */
    private Boolean isEncrypted;

    /**
     * 字段规则
     */
    private Object entryConfig;

}

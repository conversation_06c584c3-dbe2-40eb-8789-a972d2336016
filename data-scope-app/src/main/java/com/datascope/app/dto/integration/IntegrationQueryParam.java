package com.datascope.app.dto.integration;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 集成查询参数
 */
@Data
@Accessors(chain = true)
public class IntegrationQueryParam {

    /**
     * 页码，从1开始
     */
    private Integer page = 1;

    /**
     * 每页记录数
     */
    private Integer size = 10;

    /**
     * 按名称过滤
     */
    private String name;

    /**
     * 按集成类型过滤
     */
    private String type;

    /**
     * 按状态过滤
     */
    private String status;
} 
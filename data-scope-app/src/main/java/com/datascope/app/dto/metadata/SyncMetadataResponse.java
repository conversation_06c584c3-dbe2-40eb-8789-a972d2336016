package com.datascope.app.dto.metadata;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 同步元数据响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncMetadataResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 同步ID
     */
    private String syncId;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 表数量
     */
    private Integer tablesCount;

    /**
     * 视图数量
     */
    private Integer viewsCount;

    /**
     * 同步时长(毫秒)
     */
    private Integer syncDuration;

    /**
     * 状态
     */
    private String status;

    /**
     * 消息
     */
    private String message;

    /**
     * 错误列表
     */
    private List<String> errors;
} 
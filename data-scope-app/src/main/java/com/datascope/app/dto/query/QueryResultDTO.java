package com.datascope.app.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 查询结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryResultDTO {

    /**
     * 结果ID
     */
    private String id;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 状态：success-成功，error-错误，running-运行中，cancelled-已取消
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 执行时间(毫秒)
     */
    private Double executionTime;

    /**
     * 结果行数
     */
    private Integer rowCount;

    /**
     * 数据行
     */
    private List<Map<String, Object>> rows;

    /**
     * 字段信息列表
     */
    private List<FieldInfo> fields;

    /**
     * 错误信息
     */
    private String error;

    /**
     * 警告信息
     */
    private List<String> warnings;

    /**
     * 是否有更多数据
     */
    private Boolean hasMore;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 字段信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldInfo {
        /**
         * 字段名称
         */
        private String name;

        /**
         * 字段类型
         */
        private String type;

        /**
         * 字段标签
         */
        private String label;

        /**
         * 是否加密字段
         */
        private Boolean isEncrypted;
    }
}

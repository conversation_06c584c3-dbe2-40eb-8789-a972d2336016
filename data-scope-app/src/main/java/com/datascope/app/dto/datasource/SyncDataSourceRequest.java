package com.datascope.app.dto.datasource;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 同步数据源请求
 */
@Data
@Accessors(chain = true)
public class SyncDataSourceRequest {

    /**
     * 包含的Schema列表
     */
    private List<String> includeSchemas;

    /**
     * 排除的Schema列表
     */
    private List<String> excludeSchemas;

    /**
     * 包含的表列表
     */
    private List<String> includeTables;

    /**
     * 排除的表列表
     */
    private List<String> excludeTables;
} 
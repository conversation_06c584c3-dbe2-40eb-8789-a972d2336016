package com.datascope.app.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询执行计划DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionPlanDTO {

    /**
     * 执行计划ID
     */
    private String id;

    /**
     * 关联的查询ID（如果有）
     */
    private String queryId;

    /**
     * 执行计划生成时间
     */
    private String createdAt;

    /**
     * 估算成本
     */
    private Double estimatedCost;

    /**
     * 估算返回行数
     */
    private Long estimatedRows;

    /**
     * 计划生成时间（毫秒）
     */
    private Long planningTime;

    /**
     * 估算执行时间（毫秒）
     */
    private Long executionTime;

    /**
     * 原始执行计划（数据库返回的原始格式）
     */
    private Object plan;

    /**
     * 执行计划详情（结构化格式，前端使用）
     */
    private PlanDetails planDetails;

    /**
     * 执行计划详情
     */
    @Data
    public static class PlanDetails {
        /**
         * 总成本
         */
        private Double totalCost;

        /**
         * 估算行数
         */
        private Long estimatedRows;

        /**
         * 执行计划步骤列表
         */
        private List<PlanStep> steps;
    }

    /**
     * 执行计划步骤
     */
    @Data
    public static class PlanStep {
        /**
         * 步骤类型（如TABLE SCAN, INDEX SCAN, SORT, JOIN等）
         */
        private String type;

        /**
         * 表名
         */
        private String table;

        /**
         * 过滤条件
         */
        private String condition;

        /**
         * 估算成本
         */
        private Double cost;

        /**
         * 估算行数
         */
        private Long rows;

        /**
         * 使用的索引
         */
        private String index;

        /**
         * 输出列
         */
        private List<String> columns;

        /**
         * 子步骤
         */
        private List<PlanStep> children;
    }
}

/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.datascope.app.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 查询的描述信息<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/9 11:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryDefinitionDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * SQL语句
     */
    private String sql;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 参数列表
     */
    private List<QueryParameterDTO> parameters;

    /**
     * 列表字段列表
     */
    private List<QueryFieldDTO> fields;

    /**
     * 查询字段列表
     */
    private List<QueryFieldDTO> searchFields;
}

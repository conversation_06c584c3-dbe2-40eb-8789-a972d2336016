package com.datascope.app.dto.integration;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 集成查询结果DTO
 */
@Data
@Accessors(chain = true)
public class IntegrationQueryResultDTO {

    /**
     * 列定义
     */
    private List<ColumnDefinition> columns;

    /**
     * 数据行
     */
    private List<Map<String, Object>> rows;

    /**
     * 分页信息
     */
    private PaginationInfo pagination;

    /**
     * 列定义
     */
    @Data
    @Accessors(chain = true)
    public static class ColumnDefinition {
        /**
         * 字段名
         */
        private String field;

        /**
         * 标签
         */
        private String label;

        /**
         * 类型
         */
        private String type;
    }

    /**
     * 分页信息
     */
    @Data
    @Accessors(chain = true)
    public static class PaginationInfo {
        /**
         * 总记录数
         */
        private Long total;

        /**
         * 当前页码
         */
        private Integer page;

        /**
         * 每页大小
         */
        private Integer pageSize;

        /**
         * 总页数
         */
        private Integer totalPages;
    }
} 
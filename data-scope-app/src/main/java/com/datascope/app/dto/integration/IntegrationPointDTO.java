package com.datascope.app.dto.integration;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 集成点信息DTO
 */
@Data
@Accessors(chain = true)
public class IntegrationPointDTO {

    /**
     * 集成点ID
     */
    private String id;

    /**
     * 集成点名称
     */
    private String name;

    /**
     * 集成点类型
     */
    private String type;

    /**
     * URL配置
     */
    private Object urlConfig;
} 
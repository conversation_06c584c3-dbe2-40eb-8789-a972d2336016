package com.datascope.app.dto.query;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 自然语言查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NaturalLanguageQueryParams {

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 问题描述
     */
    private String question;

    /**
     * 上下文表列表
     */
    private List<String> contextTables;

    /**
     * 最大行数
     */
    private Integer maxRows;

    /**
     * 超时时间(毫秒)
     */
    private Integer timeout;
} 
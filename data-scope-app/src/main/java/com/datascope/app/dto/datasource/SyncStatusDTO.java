package com.datascope.app.dto.datasource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 同步状态DTO
 */
@Data
@Accessors(chain = true)
public class SyncStatusDTO {

    /**
     * 同步任务ID
     */
    private String syncId;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 表数量
     */
    private Integer tablesCount;

    /**
     * 视图数量
     */
    private Integer viewsCount;

    /**
     * 同步耗时（毫秒）
     */
    private Integer syncDuration;

    /**
     * 状态: pending, running, completed, failed
     */
    private String status;

    /**
     * 同步进度(0-100)
     */
    private Double progress;

    /**
     * 消息
     */
    private String message;

    /**
     * 错误信息
     */
    private List<String> errors;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedTime;
} 
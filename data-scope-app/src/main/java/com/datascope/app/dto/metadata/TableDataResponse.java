package com.datascope.app.dto.metadata;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "表数据查询响应")
public class TableDataResponse {

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "总页数")
    private Integer pages;

    @Schema(description = "当前页码")
    private Integer page;

    @Schema(description = "每页记录数")
    private Integer size;

    @Schema(description = "数据列表，每行数据用Map表示，key为列名，value为列值")
    private List<Map<String, Object>> items;
} 
package com.datascope.app.dto.datasource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

/**
 * 数据源状态DTO
 */
@Data
@Accessors(chain = true)
public class DataSourceStatusDTO {

    /**
     * 数据源ID
     */
    private String id;

    /**
     * 状态: active, inactive, error, syncing
     */
    private String status;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 最后检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastCheckedAt;

    /**
     * 消息
     */
    private String message;

    /**
     * 详细信息
     */
    private Map<String, Object> details;
} 
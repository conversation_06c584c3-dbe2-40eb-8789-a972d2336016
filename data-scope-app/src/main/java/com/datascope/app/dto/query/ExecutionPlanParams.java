package com.datascope.app.dto.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 执行计划请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionPlanParams {

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * SQL语句
     */
    private String sql;

    /**
     * 查询参数
     */
    private Map<String, Object> parameters;
}

package com.datascope.app.dto.user;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一登录的 User 对象
 * <AUTHOR>
 * @since 2025-04-10 14:49
 */
@NoArgsConstructor
@Data
public class YuiaUser {

    // {"id":18842,"organizationId":"57b2cf71-a552-4fe0-a99b-c203b3f18e8c","loginName":"wenxing.wang","username":"王文星","mobile":"133****3793","email":"<EMAIL>","migrateUserId":"37g5feg4"}


    /**
     * id
     */
    private Integer id;
    /**
     * organizationId
     */
    private String organizationId;
    /**
     * loginName
     */
    private String loginName;
    /**
     * username
     */
    private String username;
    /**
     * mobile
     */
    private String mobile;
    /**
     * email
     */
    private String email;
    /**
     * migrateUserId
     */
    private String migrateUserId;



}

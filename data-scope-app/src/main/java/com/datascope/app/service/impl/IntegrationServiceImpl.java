package com.datascope.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datascope.app.common.response.PageResponse;
import com.datascope.app.dto.integration.*;
import com.datascope.app.entity.Integration;
import com.datascope.app.mapper.IntegrationMapper;
import com.datascope.app.service.IntegrationService;
import com.datascope.app.util.AuthUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 集成管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IntegrationServiceImpl extends ServiceImpl<IntegrationMapper, Integration> implements IntegrationService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public PageResponse<IntegrationDTO> getIntegrationList(IntegrationQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Integration> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(param.getName())) {
            wrapper.like(Integration::getName, param.getName());
        }
        if (StringUtils.hasText(param.getType())) {
            wrapper.eq(Integration::getType, param.getType());
        }
        if (StringUtils.hasText(param.getStatus())) {
            wrapper.eq(Integration::getStatus, param.getStatus());
        }

        // 排序
        wrapper.orderByDesc(Integration::getUpdatedAt);

        // 分页查询
        Page<Integration> page = new Page<>(param.getPage(), param.getSize());
        IPage<Integration> integrationPage = page(page, wrapper);

        // 转换为DTO
        List<IntegrationDTO> integrationDTOList = integrationPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResponse.of(
                integrationDTOList,
                param.getPage(),
                param.getSize(),
                integrationPage.getTotal()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegrationDTO createIntegration(CreateIntegrationRequest request) {
        Integration integration = new Integration();
        BeanUtils.copyProperties(request, integration);

        // 检查是否已提供ID，否则生成新ID
        if (!StringUtils.hasText(integration.getId())) {
            integration.setId(StringUtils.hasText(request.getId()) ?
                request.getId() : "int-" + UUID.randomUUID().toString().replace("-", ""));
        }

        // 设置初始状态
        integration.setStatus(StringUtils.hasText(request.getStatus()) ?
            request.getStatus() : "DRAFT");

        integration.setCreatedAt(LocalDateTime.now());
        integration.setUpdatedAt(LocalDateTime.now());

        try {
            // 处理JSON字段
            if (request.getQueryParams() != null) {
                integration.setQueryParams(objectMapper.writeValueAsString(request.getQueryParams()));
            }

            if (request.getTableConfig() != null) {
                integration.setTableConfig(objectMapper.writeValueAsString(request.getTableConfig()));
            }

            if (request.getChartConfig() != null) {
                integration.setChartConfig(objectMapper.writeValueAsString(request.getChartConfig()));
            }

            if (request.getMeta() != null) {
                integration.setMeta(objectMapper.writeValueAsString(request.getMeta()));
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
            throw new RuntimeException("处理JSON数据时发生错误");
        }

        // 添加当前用户信息
        integration.setCreatedBy(AuthUtils.getUsername()); // 实际应用中应该从安全上下文获取当前用户
        integration.setUpdatedBy(integration.getCreatedBy());

        save(integration);

        return convertToDTO(integration);
    }

    @Override
    public IntegrationDTO getIntegrationById(String id) {
        Integration integration = getById(id);
        if (integration == null) {
            return null;
        }
        return convertToDTO(integration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegrationDTO updateIntegration(UpdateIntegrationRequest request) {
        String id = request.getId();
        Integration integration = getById(id);
        if (integration == null) {
            return null;
        }

        // 更新基本信息
        if (StringUtils.hasText(request.getName())) {
            integration.setName(request.getName());
        }
        if (request.getDescription() != null) {
            integration.setDescription(request.getDescription());
        }
        if (StringUtils.hasText(request.getType())) {
            integration.setType(request.getType());
        }
        if (StringUtils.hasText(request.getStatus())) {
            integration.setStatus(request.getStatus());
        }
        if (StringUtils.hasText(request.getQueryId())) {
            integration.setQueryId(request.getQueryId());
        }
        if (StringUtils.hasText(request.getDataSourceId())) {
            integration.setDataSourceId(request.getDataSourceId());
        }

        try {
            // 处理JSON字段
            if (request.getQueryParams() != null) {
                integration.setQueryParams(objectMapper.writeValueAsString(request.getQueryParams()));
            }

            if (request.getTableConfig() != null) {
                integration.setTableConfig(objectMapper.writeValueAsString(request.getTableConfig()));
            }

            if (request.getChartConfig() != null) {
                integration.setChartConfig(objectMapper.writeValueAsString(request.getChartConfig()));
            }

            if (request.getMeta() != null) {
                integration.setMeta(objectMapper.writeValueAsString(request.getMeta()));
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
            throw new RuntimeException("处理JSON数据时发生错误");
        }

        integration.setUpdatedAt(LocalDateTime.now());
        integration.setUpdatedBy(AuthUtils.getUsername()); // 实际应用中应该从安全上下文获取当前用户

        updateById(integration);

        return convertToDTO(integration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIntegration(String id) {
        return removeById(id);
    }

    @Override
    public IntegrationQueryResultDTO previewIntegration(String id) {
        // 这里应该调用查询执行服务，执行集成关联的查询
        // 由于这是一个示例，我们返回一些模拟数据
        IntegrationQueryResultDTO result = new IntegrationQueryResultDTO();

        List<IntegrationQueryResultDTO.ColumnDefinition> columns = new ArrayList<>();
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("id")
                .setLabel("ID")
                .setType("string"));
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("name")
                .setLabel("名称")
                .setType("string"));

        List<Map<String, Object>> rows = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", "1");
        row1.put("name", "测试数据1");
        rows.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", "2");
        row2.put("name", "测试数据2");
        rows.add(row2);

        result.setColumns(columns);
        result.setRows(rows);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegrationDTO updateIntegrationStatus(String id, UpdateIntegrationStatusRequest request) {
        Integration integration = getById(id);
        if (integration == null) {
            return null;
        }

        integration.setStatus(request.getStatus());
        integration.setUpdatedAt(LocalDateTime.now());
        integration.setUpdatedBy(AuthUtils.getUsername()); // 实际应用中应该从安全上下文获取当前用户

        updateById(integration);

        return convertToDTO(integration);
    }

    @Override
    public IntegrationQueryResultDTO executeIntegrationQuery(ExecuteIntegrationQueryRequest request) {
        // 这里应该调用查询执行服务，执行集成关联的查询
        // 由于这是一个示例，我们返回一些模拟数据
        IntegrationQueryResultDTO result = new IntegrationQueryResultDTO();

        List<IntegrationQueryResultDTO.ColumnDefinition> columns = new ArrayList<>();
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("id")
                .setLabel("ID")
                .setType("string"));
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("name")
                .setLabel("名称")
                .setType("string"));

        List<Map<String, Object>> rows = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", "1");
        row1.put("name", "测试数据1");
        rows.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", "2");
        row2.put("name", "测试数据2");
        rows.add(row2);

        result.setColumns(columns);
        result.setRows(rows);

        return result;
    }

    /**
     * 将实体转换为DTO
     *
     * @param integration 集成实体
     * @return 集成DTO
     */
    private IntegrationDTO convertToDTO(Integration integration) {
        if (integration == null) {
            return null;
        }

        IntegrationDTO dto = new IntegrationDTO();
        BeanUtils.copyProperties(integration, dto);

        // 处理日期格式
        if (integration.getCreatedAt() != null) {
            dto.setCreatedAt(Date.from(integration.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (integration.getUpdatedAt() != null) {
            dto.setUpdatedAt(Date.from(integration.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }

        try {
            // 处理JSON字段
            if (StringUtils.hasText(integration.getQueryParams())) {
                dto.setQueryParams(objectMapper.readValue(integration.getQueryParams(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, CreateIntegrationRequest.QueryParam.class)));
            }

            if (StringUtils.hasText(integration.getTableConfig())) {
                dto.setTableConfig(objectMapper.readValue(integration.getTableConfig(), Object.class));
            }

            if (StringUtils.hasText(integration.getChartConfig())) {
                dto.setChartConfig(objectMapper.readValue(integration.getChartConfig(), Object.class));
            }

            if (StringUtils.hasText(integration.getMeta())) {
                dto.setMeta(objectMapper.readValue(integration.getMeta(), Object.class));
            }
        } catch (JsonProcessingException e) {
            log.error("JSON解析异常", e);
            // 继续处理其他字段，不中断转换
        }

        return dto;
    }
}

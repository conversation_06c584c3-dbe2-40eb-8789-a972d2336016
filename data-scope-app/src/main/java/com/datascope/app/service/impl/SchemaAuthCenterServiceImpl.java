package com.datascope.app.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.factory.AbstractAuthCenter;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.model.ResourceBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SchemaAuthCenterServiceImpl extends AbstractAuthCenter {

    @Autowired
    private SchemaMapper schemaMapper;

    @Autowired
    private DatasourceMapper datasourceMapper;

    @Override
    public String getAuthType() {
        return AuthTypeEnum.SCHEMA.getCode();
    }

    public ResourceBO assembleBo(AuthDTO authDTO) {
        Schema schema = authDTO.getSchema();
        String datasourceId = schema.getDatasourceId();
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Datasource::getId, datasourceId);
        Datasource datasource = datasourceMapper.selectOne(wrapper);
        String code = datasource.getDatabaseName() + StrPool.COLON + schema.getName();
        return new ResourceBO().setResourceCode(code).setResourceName(code);
    }

    @Override
    public ResourceBO addAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public ResourceBO removeAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public Pair<Boolean, Boolean> handle(AuthDTO authDTO) {
        LambdaQueryWrapper<Schema> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Schema::getId, authDTO.getId());
        Schema schema = schemaMapper.selectOne(wrapper);
        Assert.isTrue(Objects.nonNull(schema), () -> new RuntimeException("schema not found"));
        if (Objects.nonNull(authDTO.getAuthRequired()) && !authDTO.getAuthRequired().equals(schema.getIsAuthRequired())) {
            LambdaUpdateWrapper<Schema> wrapperUpdate = new LambdaUpdateWrapper<>();
            wrapperUpdate.eq(Schema::getId, authDTO.getId());
            wrapperUpdate.set(Schema::getIsAuthRequired, authDTO.getAuthRequired());
            this.schemaMapper.update(null, wrapperUpdate);
        }
        authDTO.setSchema(schema);
        return Pair.of(schema.getIsAuthRequired(), authDTO.getAuthRequired());
    }
}

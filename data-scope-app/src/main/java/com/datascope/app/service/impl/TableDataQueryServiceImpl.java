package com.datascope.app.service.impl;

import com.datascope.app.dto.metadata.TableDataQueryRequest;
import com.datascope.app.dto.metadata.TableDataResponse;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.exception.BusinessException;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.mapper.TableMapper;
import com.datascope.app.service.TableDataQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class TableDataQueryServiceImpl implements TableDataQueryService {

    private final TableMapper tableMapper;
    private final SchemaMapper schemaMapper;
    private final JdbcTemplate jdbcTemplate;
    
    /**
     * 默认日期时间格式
     */
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 日期格式化工具
     */
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat(DATE_FORMAT);

    @Override
    public TableDataResponse queryTableData(String tableId, TableDataQueryRequest request) {
        // 1. 获取表信息
        Table table = tableMapper.selectById(tableId);
        if (table == null) {
            throw new BusinessException("表不存在");
        }

        // 2. 获取schema信息
        Schema schema = schemaMapper.selectById(table.getSchemaId());
        if (schema == null) {
            throw new BusinessException("Schema不存在");
        }

        // 3. 构建完整表名
        String fullTableName = String.format("%s.%s", schema.getName(), table.getName());

        // 4. 构建查询SQL
        String countSql = String.format("SELECT COUNT(*) FROM %s", fullTableName);
        String dataSql = String.format("SELECT * FROM %s LIMIT %d OFFSET %d",
                fullTableName,
                request.getSize(),
                (request.getPage() - 1) * request.getSize());

        log.debug("执行查询，countSql: {}, dataSql: {}", countSql, dataSql);

        // 5. 执行查询
        Long total = jdbcTemplate.queryForObject(countSql, Long.class);
        List<Map<String, Object>> items = jdbcTemplate.queryForList(dataSql);
        
        // 处理日期时间类型格式化
        items = processDateInQueryResult(items);

        // 6. 构建响应
        TableDataResponse response = new TableDataResponse();
        response.setTotal(total);
        response.setPage(request.getPage());
        response.setSize(request.getSize());
        response.setPages((int) Math.ceil((double) total / request.getSize()));
        response.setItems(items);

        return response;
    }
    
    /**
     * 处理查询结果中的日期时间类型，将其格式化为指定格式
     *
     * @param rows 查询结果数据行
     * @return 处理后的结果数据行
     */
    private List<Map<String, Object>> processDateInQueryResult(List<Map<String, Object>> rows) {
        if (rows == null || rows.isEmpty()) {
            return rows;
        }

        for (Map<String, Object> row : rows) {
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                if (entry.getValue() instanceof Date) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format((Date) entry.getValue()));
                } else if (entry.getValue() instanceof java.sql.Date) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(new Date(((java.sql.Date) entry.getValue()).getTime())));
                } else if (entry.getValue() instanceof java.sql.Timestamp) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(new Date(((java.sql.Timestamp) entry.getValue()).getTime())));
                } else if (entry.getValue() instanceof LocalDateTime) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(Date.from(((LocalDateTime) entry.getValue()).atZone(ZoneId.systemDefault()).toInstant())));
                }
            }
        }

        return rows;
    }
} 
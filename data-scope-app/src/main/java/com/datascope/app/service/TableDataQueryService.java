package com.datascope.app.service;

import com.datascope.app.dto.metadata.TableDataQueryRequest;
import com.datascope.app.dto.metadata.TableDataResponse;

/**
 * 表数据查询服务
 */
public interface TableDataQueryService {

    /**
     * 查询表数据
     *
     * @param tableId 表ID
     * @param request 查询参数
     * @return 表数据
     */
    TableDataResponse queryTableData(String tableId, TableDataQueryRequest request);
} 
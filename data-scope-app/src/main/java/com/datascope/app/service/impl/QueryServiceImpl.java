package com.datascope.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.constants.Constant;
import com.datascope.app.dto.integration.CreateIntegrationRequest;
import com.datascope.app.dto.integration.IntegrationDTO;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.dto.query.*;
import com.datascope.app.entity.*;
import com.datascope.app.exception.BusinessException;
import com.datascope.app.mapper.*;
import com.datascope.app.model.AuthResourceBO;
import com.datascope.app.model.FieldRule;
import com.datascope.app.service.AuthService;
import com.datascope.app.service.DatasourceService;
import com.datascope.app.service.IntegrationService;
import com.datascope.app.service.QueryService;
import com.datascope.app.util.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 查询服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueryServiceImpl implements QueryService {

    private final QueryMapper queryMapper;
    private final QueryVersionMapper queryVersionMapper;
    private final QueryParameterMapper queryParameterMapper;
    private final QueryFavoriteMapper queryFavoriteMapper;
    private final ExecutionHistoryMapper executionHistoryMapper;
    private final DatasourceMapper datasourceMapper;
    private final TableMapper tableMapper;
    private final ColumnMapper columnMapper;
    private final SchemaMapper schemaMapper;
    private final JdbcTemplate jdbcTemplate;
    private final DatasourceService dataSourceService;
    private final SQLAnalyzer sqlAnalyzer;
    private final IntegrationService integrationService;
    private final ObjectMapper objectMapper;
    private final AuthService authService;
    //private final QueryResultDecryptor queryResultDecryptor;

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT);
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat(DATE_FORMAT);

    @Override
    public Page<QueryDTO> getQueries(int page, int size, String search, String queryType, String status,
                                     String serviceStatus, String dataSourceId, String sortBy, String sortDir,
                                     boolean includeDrafts) {
        log.info("获取查询列表, 参数: page={}, size={}, search={}, queryType={}, status={}, serviceStatus={}, dataSourceId={}, sortBy={}, sortDir={}, includeDrafts={}",
            page, size, search, queryType, status, serviceStatus, dataSourceId, sortBy, sortDir, includeDrafts);

        // 创建分页对象
        Page<Query> queryPage = new Page<>(page, size);

        // 构建查询条件
        LambdaQueryWrapper<Query> wrapper = new LambdaQueryWrapper<>();

        // 添加搜索条件
        if (search != null && !search.trim().isEmpty()) {
            wrapper.like(Query::getName, search)
                .or()
                .like(Query::getDescription, search);
        }

        // 添加查询类型过滤
        if (queryType != null && !queryType.trim().isEmpty()) {
            wrapper.eq(Query::getQueryType, queryType);
        }

        // 添加状态过滤
        if (status != null && !status.trim().isEmpty()) {
            wrapper.eq(Query::getStatus, status);
        } else if (!includeDrafts) {
            // 如果不包含草稿，则排除DRAFT状态
            wrapper.ne(Query::getStatus, "DRAFT");
        }

        // 添加服务状态过滤
        if (serviceStatus != null && !serviceStatus.trim().isEmpty()) {
            wrapper.eq(Query::getServiceStatus, serviceStatus);
        }

        // 添加数据源过滤
        if (dataSourceId != null && !dataSourceId.trim().isEmpty()) {
            wrapper.eq(Query::getDataSourceId, dataSourceId);
        }

        // 添加排序
        if (sortBy != null && !sortBy.trim().isEmpty()) {
            boolean isAsc = !"desc".equalsIgnoreCase(sortDir);

            switch (sortBy) {
                case "name":
                    wrapper.orderBy(true, isAsc, Query::getName);
                    break;
                case "createdAt":
                    wrapper.orderBy(true, isAsc, Query::getCreatedAt);
                    break;
                case "updatedAt":
                    wrapper.orderBy(true, isAsc, Query::getUpdatedAt);
                    break;
                case "executionCount":
                    wrapper.orderBy(true, isAsc, Query::getExecutionCount);
                    break;
                case "lastExecutedAt":
                    wrapper.orderBy(true, isAsc, Query::getLastExecutedAt);
                    break;
                default:
                    // 默认按更新时间降序
                    wrapper.orderByDesc(Query::getUpdatedAt);
            }
        } else {
            // 默认按更新时间降序
            wrapper.orderByDesc(Query::getUpdatedAt);
        }

        // 执行查询
        queryPage = queryMapper.selectPage(queryPage, wrapper);

        // 创建结果分页对象
        Page<QueryDTO> resultPage = new Page<>(page, size);
        resultPage.setTotal(queryPage.getTotal());
        resultPage.setPages(queryPage.getPages());

        // 转换结果
        List<QueryDTO> queryDTOList = new ArrayList<>();
        for (Query query : queryPage.getRecords()) {
            QueryDTO dto = convertToDTO(query);
            queryDTOList.add(dto);
        }

        resultPage.setRecords(queryDTOList);

        log.info("查询结果: 总数={}, 页数={}", resultPage.getTotal(), resultPage.getPages());
        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryDTO createQuery(SaveQueryParams params) {
        log.info("开始创建查询: {}", params);
        Datasource datasource = checkDbAccess(params.getDataSourceId());

        // 生成ID
        String id = UUID.randomUUID().toString().replace("-", "");
        String currentUser = AuthUtils.getUsername(); // 这里应该从上下文获取当前用户
        Date now = new Date();

        // 构建Query实体
        Query query = Query.builder()
            .id(id)
            .name(params.getName())
            .description(params.getDescription())
            .dataSourceId(params.getDataSourceId())
            .status("DRAFT") // 初始状态为草稿
            .serviceStatus(params.getServiceStatus() != null ? params.getServiceStatus() : "ENABLED")
            .queryType(params.getQueryType() != null ? params.getQueryType() : "SQL")
            .isPublic(params.getIsPublic() != null ? params.getIsPublic() : false)
            .executionCount(0)
            .createdBy(currentUser)
            .createdAt(now)
            .updatedBy(currentUser)
            .updatedAt(now)
            .build();

        // 保存到数据库
        int result = queryMapper.insert(query);
        log.info("查询保存结果: {}, 影响行数: {}", result > 0 ? "成功" : "失败", result);

        if (result <= 0) {
            log.error("创建查询失败: {}", params);
            throw new RuntimeException("创建查询失败");
        }

        // 如果有标签，保存标签
        if (params.getTags() != null && !params.getTags().isEmpty()) {
            // 这里需要实现标签保存逻辑
            log.info("保存查询标签: {}", params.getTags());
        }

        // 创建初始版本
        QueryVersion initialVersion = QueryVersion.builder()
            .id(UUID.randomUUID().toString().replace("-", ""))
            .queryId(id)
            .versionNumber(1)
            .name("初始版本")
            .sqlContent(params.getSql())
            .dataSourceId(params.getDataSourceId())
            .status("DRAFT")
            .isLatest(true)
            .createdBy(currentUser)
            .createdAt(now)
            .updatedBy(currentUser)
            .updatedAt(now)
            .build();

        int versionResult = queryVersionMapper.insert(initialVersion);
        log.info("查询版本保存结果: {}, 影响行数: {}", versionResult > 0 ? "成功" : "失败", versionResult);

        // 提取并保存参数信息
        if (params.getSql() != null && !params.getSql().trim().isEmpty()) {
            String sql = MybatisSqlParser.parseSql(params.getSql(), Maps.newHashMap());
            sql = removeComments(sql);
            log.info("移除注释后的SQL: {}", sql);
            authService.checkSqlAuth(new AuthDTO().setSql(sql).setLoginName(AuthUtils.getLoginName()).setDatasource(datasource));

            saveQueryParameters(id, initialVersion.getId(), params.getSql(), currentUser, now);
        }

        // 构建返回的DTO
        QueryDTO dto = convertToDTO(query);
        log.info("查询创建完成: {}", dto.getId());
        return dto;
    }

    @Override
    public QueryDTO getQuery(String id) {
        log.info("获取查询详情: {}", id);

        // 从数据库中查询
        Query query = queryMapper.selectById(id);
        if (query == null) {
            log.error("查询不存在: {}", id);
            throw new RuntimeException("查询不存在: " + id);
        }

        // 转换为DTO
        QueryDTO dto = convertToDTO(query);

        // 查询版本信息：优先获取已发布版本，如果没有则获取最新版本
        QueryVersion version = queryVersionMapper.selectOne(
            new LambdaQueryWrapper<QueryVersion>()
                .eq(QueryVersion::getQueryId, id)
                .eq(QueryVersion::getStatus, "PUBLISHED")
                .orderByDesc(QueryVersion::getVersionNumber)
                .last("LIMIT 1")
        );

        // 如果没有发布态的版本，则获取最新版本
        if (version == null) {
            version = queryVersionMapper.selectOne(
                new LambdaQueryWrapper<QueryVersion>()
                    .eq(QueryVersion::getQueryId, id)
                    .orderByDesc(QueryVersion::getVersionNumber)
                    .last("LIMIT 1")
            );
        }

        if (version != null) {
            // 转换版本信息
            QueryVersionDTO versionDTO = QueryVersionDTO.builder()
                .id(version.getId())
                .queryId(version.getQueryId())
                .versionNumber(version.getVersionNumber())
                .name(version.getName())
                .description(version.getDescription())
                .sql(version.getSqlContent())
                .dataSourceId(version.getDataSourceId())
                .status(version.getStatus())
                .isLatest(version.getIsLatest())
                .createdAt(version.getCreatedAt())
                .updatedAt(version.getUpdatedAt())
                .build();

            // 设置当前版本
            dto.setCurrentVersion(versionDTO);

            // 查询参数信息
            List<QueryParameterDTO> parameters = getVersionParameters(version.getId());
            if (parameters != null && !parameters.isEmpty()) {
                dto.setParameters(parameters);
            }
        }

        log.info("查询详情获取成功: {}", id);
        return dto;
    }

    private Datasource checkDbAccess(String dataSourceId) {
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Datasource::getId, dataSourceId);
        Datasource datasource = datasourceMapper.selectOne(wrapper);
        Assert.isTrue(datasource != null, () -> new RuntimeException("数据源不存在"));
        if (datasource.getIsAuthRequired()) {
            boolean flag = authService.checkAuth(datasource.getDatabaseName(), AuthUtils.getUsername(), authService.getAuthToken());
            Assert.isTrue(flag, () -> new RuntimeException("您没权限操作数据源: " + datasource.getDatabaseName()));
        }
        return datasource;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryDTO updateQuery(String id, SaveQueryParams params) {
        log.info("开始更新查询[{}]: {}", id, params);
        Datasource datasource = checkDbAccess(params.getDataSourceId());

        // 先查询是否存在
        Query query = queryMapper.selectById(id);
        if (query == null) {
            log.error("更新查询失败，查询不存在: {}", id);
            throw new RuntimeException("查询不存在: " + id);
        }

        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 更新实体属性
        query.setName(params.getName());
        if (params.getDescription() != null) {
            query.setDescription(params.getDescription());
        }
        if (params.getDataSourceId() != null) {
            query.setDataSourceId(params.getDataSourceId());
        }
        // 根据查询版本状态自动确定查询状态
        query.setStatus(determineQueryStatus(id));
        if (params.getServiceStatus() != null) {
            query.setServiceStatus(params.getServiceStatus());
        }
        if (params.getQueryType() != null) {
            query.setQueryType(params.getQueryType());
        }
        if (params.getIsPublic() != null) {
            query.setIsPublic(params.getIsPublic());
        }
        query.setUpdatedBy(currentUser);
        query.setUpdatedAt(now);

        // 保存到数据库
        int result = queryMapper.updateById(query);
        log.info("查询更新结果: {}, 影响行数: {}", result > 0 ? "成功" : "失败", result);

        if (result <= 0) {
            log.error("更新查询失败: {}", id);
            throw new RuntimeException("更新查询失败");
        }

        // 如果SQL有更新，创建新版本
        if (params.getSql() != null) {
            String sql = MybatisSqlParser.parseSql(params.getSql(), Maps.newHashMap());
            sql = removeComments(sql);
            log.info("移除注释后的SQL: {}", sql);
            authService.checkSqlAuth(new AuthDTO().setSql(sql).setLoginName(AuthUtils.getLoginName()).setDatasource(datasource));

            // 查询当前最新版本
            LambdaQueryWrapper<QueryVersion> versionWrapper = new LambdaQueryWrapper<>();
            versionWrapper.eq(QueryVersion::getQueryId, id)
                .eq(QueryVersion::getIsLatest, true);
            QueryVersion latestVersion = queryVersionMapper.selectOne(versionWrapper);

            // 判断SQL是否有变化，且当前版本不是草稿态
            boolean sqlChanged = latestVersion == null || !params.getSql().equals(latestVersion.getSqlContent());
            boolean isNotDraft = latestVersion != null && !"DRAFT".equals(latestVersion.getStatus());

            if (sqlChanged && isNotDraft) {
                // 更新最新版本标记为false
                if (latestVersion != null) {
                    latestVersion.setIsLatest(false);
                    queryVersionMapper.updateById(latestVersion);
                }

                // 创建新版本
                int versionNumber = latestVersion != null ? latestVersion.getVersionNumber() + 1 : 1;
                QueryVersion newVersion = QueryVersion.builder()
                    .id(UUID.randomUUID().toString().replace("-", ""))
                    .queryId(id)
                    .versionNumber(versionNumber)
                    .name("版本 " + versionNumber)
                    .sqlContent(params.getSql())
                    .dataSourceId(params.getDataSourceId())
                    .status("DRAFT")
                    .isLatest(true)
                    .createdBy(currentUser)
                    .createdAt(now)
                    .updatedBy(currentUser)
                    .updatedAt(now)
                    .build();

                queryVersionMapper.insert(newVersion);
                log.info("创建新查询版本: {}, 版本号: {}", newVersion.getId(), versionNumber);

                // 提取并保存参数信息
                saveQueryParameters(id, newVersion.getId(), params.getSql(), currentUser, now);
            } else if (latestVersion != null && "DRAFT".equals(latestVersion.getStatus())) {
                // 更新草稿版本的SQL
                latestVersion.setSqlContent(params.getSql());
                latestVersion.setUpdatedBy(currentUser);
                latestVersion.setUpdatedAt(now);
                queryVersionMapper.updateById(latestVersion);

                // 更新参数信息
                // 先删除旧参数
                LambdaQueryWrapper<QueryParameter> paramWrapper = new LambdaQueryWrapper<>();
                paramWrapper.eq(QueryParameter::getVersionId, latestVersion.getId());
                queryParameterMapper.delete(paramWrapper);

                // 提取并保存新参数
                saveQueryParameters(id, latestVersion.getId(), params.getSql(), currentUser, now);

                log.info("更新草稿版本SQL: {}", latestVersion.getId());
            }
        }

        // 更新标签
        if (params.getTags() != null) {
            // 这里需要实现标签更新逻辑
            log.info("更新查询标签: {}", params.getTags());
        }

        // 构建返回的DTO
        QueryDTO dto = convertToDTO(query);
        log.info("查询更新完成: {}", dto.getId());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuery(String id) {
        log.info("开始删除查询: {}", id);

        // 检查查询是否存在
        Query query = queryMapper.selectById(id);
        if (query == null) {
            log.error("删除失败，查询不存在: {}", id);
            throw new BusinessException("查询不存在: " + id);
        }

        // 1. 删除查询版本
        LambdaQueryWrapper<QueryVersion> versionWrapper = new LambdaQueryWrapper<>();
        versionWrapper.eq(QueryVersion::getQueryId, id);
        int versionsDeleted = queryVersionMapper.delete(versionWrapper);
        log.info("删除查询[{}]的版本数量: {}", id, versionsDeleted);

        // 2. 删除收藏记录
        LambdaQueryWrapper<QueryFavorite> favoriteWrapper = new LambdaQueryWrapper<>();
        favoriteWrapper.eq(QueryFavorite::getQueryId, id);
        int favoritesDeleted = queryFavoriteMapper.delete(favoriteWrapper);
        log.info("删除查询[{}]的收藏记录数量: {}", id, favoritesDeleted);

        // 3. 删除执行历史
        LambdaQueryWrapper<ExecutionHistory> historyWrapper = new LambdaQueryWrapper<>();
        historyWrapper.eq(ExecutionHistory::getQueryId, id);
        int historiesDeleted = executionHistoryMapper.delete(historyWrapper);
        log.info("删除查询[{}]的执行历史数量: {}", id, historiesDeleted);

        // 4. 删除查询参数
        LambdaQueryWrapper<QueryParameter> paramWrapper = new LambdaQueryWrapper<>();
        paramWrapper.eq(QueryParameter::getQueryId, id);
        int parametersDeleted = queryParameterMapper.delete(paramWrapper);
        log.info("删除查询[{}]的参数数量: {}", id, parametersDeleted);

        // 5. 删除查询本身
        int queryDeleted = queryMapper.deleteById(id);
        log.info("删除查询[{}]结果: {}", id, queryDeleted > 0 ? "成功" : "失败");

        return queryDeleted > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryResultDTO executeQuery(String id, ExecuteQueryParams params) {
        log.info("开始执行查询: {}, 参数: {}", id, params);


        // 从数据库获取查询信息
        Query query = queryMapper.selectById(id);
        if (query == null) {
            log.error("执行查询失败，查询不存在: {}", id);
            throw new RuntimeException("查询不存在: " + id);
        }

        // 检查查询服务状态
        if ("DISABLED".equals(query.getServiceStatus())) {
            log.error("执行查询失败，查询已禁用: {}", id);
            throw new RuntimeException("查询已禁用，无法执行: " + id);
        }

        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(query.getDataSourceId());
        if (datasource == null) {
            log.error("执行查询失败，数据源不存在: {}", query.getDataSourceId());
            throw new RuntimeException("数据源不存在: " + query.getDataSourceId());
        }

        // 获取最新版本的SQL
        LambdaQueryWrapper<QueryVersion> versionWrapper = new LambdaQueryWrapper<>();
        versionWrapper.eq(QueryVersion::getQueryId, id)
            .eq(QueryVersion::getIsLatest, true);
        QueryVersion latestVersion = queryVersionMapper.selectOne(versionWrapper);

        if (latestVersion == null) {
            log.error("执行查询失败，查询版本不存在: {}", id);
            throw new RuntimeException("查询版本不存在: " + id);
        }

        String sql = latestVersion.getSqlContent();
        log.info("执行SQL: {}", sql);

        // 获取查询参数并处理默认值
        Map<String, Object> executionParams = processQueryParameters(latestVersion.getId(), params.getParameters());

        return executeSql(datasource, query, latestVersion, sql, ExecuteQueryParams.builder()
            .sql(sql)
            .parameters(executionParams)
            .build());
    }

    @Override
    public Page<QueryDTO> getFavorites(int page, int size, String status, String dataSourceId, String search) {
        log.info("获取收藏列表, 参数: page={}, size={}, status={}, dataSourceId={}, search={}",
            page, size, status, dataSourceId, search);

        // 获取当前用户ID - 实际项目中从安全上下文获取，这里使用模拟值
        String currentUserId = AuthUtils.getUsername();

        // 创建查询收藏分页对象
        Page<QueryFavorite> favoritePage = new Page<>(page, size);

        // 构建查询收藏的条件
        LambdaQueryWrapper<QueryFavorite> favoriteWrapper = new LambdaQueryWrapper<>();
        favoriteWrapper.eq(QueryFavorite::getUserId, currentUserId);

        // 查询收藏列表
        favoritePage = queryFavoriteMapper.selectPage(favoritePage, favoriteWrapper);

        // 如果没有收藏记录，直接返回空结果
        if (favoritePage.getRecords().isEmpty()) {
            log.info("没有收藏记录");
            Page<QueryDTO> emptyResult = new Page<>(page, size);
            emptyResult.setRecords(new ArrayList<>());
            emptyResult.setTotal(0);
            return emptyResult;
        }

        // 提取查询ID列表
        List<String> queryIds = favoritePage.getRecords().stream()
            .map(QueryFavorite::getQueryId)
            .collect(Collectors.toList());

        // 构建查询条件
        LambdaQueryWrapper<Query> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Query::getId, queryIds);

        // 添加状态过滤
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq(Query::getStatus, status);
        }

        // 添加数据源过滤
        if (dataSourceId != null && !dataSourceId.trim().isEmpty()) {
            queryWrapper.eq(Query::getDataSourceId, dataSourceId);
        }

        // 添加搜索条件
        if (search != null && !search.trim().isEmpty()) {
            queryWrapper.and(w -> w.like(Query::getName, search)
                .or()
                .like(Query::getDescription, search));
        }

        // 按更新时间降序排序
        queryWrapper.orderByDesc(Query::getUpdatedAt);

        // 查询收藏的查询列表
        List<Query> queries = queryMapper.selectList(queryWrapper);

        // 创建结果分页对象
        Page<QueryDTO> resultPage = new Page<>(page, size);
        resultPage.setTotal(queries.size()); // 注意：这里直接使用查询结果数量作为总数
        resultPage.setPages((int) Math.ceil((double) queries.size() / size));

        // 处理分页 - 手动进行分页处理
        int fromIndex = (page - 1) * size;
        int toIndex = Math.min(fromIndex + size, queries.size());

        if (fromIndex >= queries.size()) {
            // 页码超出范围
            resultPage.setRecords(new ArrayList<>());
        } else {
            List<Query> pageQueries = queries.subList(fromIndex, toIndex);

            // 转换为DTO
            List<QueryDTO> queryDTOList = new ArrayList<>();
            for (Query query : pageQueries) {
                QueryDTO dto = convertToDTO(query);
                dto.setIsFavorite(true); // 确保收藏状态是true
                queryDTOList.add(dto);
            }

            resultPage.setRecords(queryDTOList);
        }

        log.info("收藏查询结果: 总数={}, 页数={}", resultPage.getTotal(), resultPage.getPages());
        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean favoriteQuery(String id) {
        log.info("收藏查询: {}", id);

        // 获取当前用户ID - 实际项目中从安全上下文获取，这里使用模拟值
        String currentUserId = AuthUtils.getUsername();

        // 先检查查询是否存在
        Query query = queryMapper.selectById(id);
        if (query == null) {
            log.error("收藏失败，查询不存在: {}", id);
            throw new RuntimeException("查询不存在: " + id);
        }

        // 检查是否已经收藏
        LambdaQueryWrapper<QueryFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryFavorite::getQueryId, id)
            .eq(QueryFavorite::getUserId, currentUserId);

        Integer count = queryFavoriteMapper.selectCount(wrapper);
        if (count > 0) {
            log.info("查询已经收藏过: {}", id);
            return true;
        }

        // 创建收藏记录
        QueryFavorite favorite = QueryFavorite.builder()
            .id(UUID.randomUUID().toString().replace("-", ""))
            .queryId(id)
            .userId(currentUserId)
            .createdAt(new Date())
            .build();

        int result = queryFavoriteMapper.insert(favorite);

        log.info("收藏查询结果: {}", result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfavoriteQuery(String id) {
        log.info("取消收藏查询: {}", id);

        // 获取当前用户ID - 实际项目中从安全上下文获取，这里使用模拟值
        String currentUserId = AuthUtils.getUsername();

        // 构建条件
        LambdaQueryWrapper<QueryFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryFavorite::getQueryId, id)
            .eq(QueryFavorite::getUserId, currentUserId);

        // 删除收藏记录
        int result = queryFavoriteMapper.delete(wrapper);

        log.info("取消收藏查询结果: {}, 删除记录数: {}", result > 0 ? "成功" : "失败", result);
        return result > 0;
    }

    @Override
    public NaturalLanguageToSqlResult nlToSql(NaturalLanguageQueryParams params) {
        log.info("处理自然语言转SQL请求: {}", params);

        String question = params.getQuestion();
        String dataSourceId = params.getDataSourceId();

        // 检查参数
        if (question == null || question.trim().isEmpty()) {
            log.error("问题为空");
            throw new RuntimeException("问题不能为空");
        }

        // 获取数据源信息（如果有）
        String dataSourceName = "未知数据源";
        if (dataSourceId != null && !dataSourceId.trim().isEmpty()) {
            Datasource datasource = datasourceMapper.selectById(dataSourceId);
            if (datasource != null) {
                dataSourceName = datasource.getName();
                log.info("使用数据源: {}({})", dataSourceName, dataSourceId);
            } else {
                log.warn("找不到数据源: {}", dataSourceId);
            }
        }

        // TODO: 接入真实的自然语言处理服务
        // 这里应该调用NLP服务来处理自然语言问题，生成SQL
        // 目前返回一个示例SQL

        log.info("生成SQL示例，实际问题: {}", question);

        // 根据问题关键词生成不同的SQL示例
        String sql;
        List<String> tables = new ArrayList<>();
        String explanation;

        if (question.toLowerCase().contains("用户") || question.toLowerCase().contains("user")) {
            sql = "SELECT id, username, email, created_at FROM users LIMIT 10";
            tables.add("users");
            explanation = "查询用户表的前10条记录，包括ID、用户名、邮箱和创建时间";
        } else if (question.toLowerCase().contains("订单") || question.toLowerCase().contains("order")) {
            sql = "SELECT id, user_id, total_amount, status, created_at FROM orders ORDER BY created_at DESC LIMIT 10";
            tables.add("orders");
            explanation = "查询订单表的前10条记录，按创建时间降序排列";
        } else if (question.toLowerCase().contains("产品") || question.toLowerCase().contains("商品") || question.toLowerCase().contains("product")) {
            sql = "SELECT id, name, price, category, stock FROM products WHERE stock > 0 LIMIT 10";
            tables.add("products");
            explanation = "查询有库存的产品，包括ID、名称、价格、类别和库存";
        } else {
            // 默认示例
            sql = "SELECT * FROM sample_table LIMIT 10";
            tables.add("sample_table");
            explanation = "这是一个示例SQL查询，返回sample_table表的前10条记录";
        }

        log.info("自然语言转SQL结果: SQL={}, 表={}, 解释={}", sql, tables, explanation);

        return NaturalLanguageToSqlResult.builder()
            .sql(sql)
            .explanation(explanation)
            .tables(tables)
            .build();
    }

    @Override
    public Page<ExecutionHistoryDTO> getExecutionHistory(String queryId, int page, int size) {
        log.info("获取查询[{}]执行历史, page={}, size={}", queryId, page, size);

        // 创建分页对象
        Page<ExecutionHistory> historyPage = new Page<>(page, size);

        // 构建查询条件
        LambdaQueryWrapper<ExecutionHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExecutionHistory::getQueryId, queryId)
            .orderByDesc(ExecutionHistory::getExecutedAt);

        // 执行查询
        historyPage = executionHistoryMapper.selectPage(historyPage, wrapper);

        // 创建结果分页对象
        Page<ExecutionHistoryDTO> resultPage = new Page<>(page, size);
        resultPage.setTotal(historyPage.getTotal());
        resultPage.setPages(historyPage.getPages());

        // 转换结果
        List<ExecutionHistoryDTO> historyDTOList = new ArrayList<>();
        for (ExecutionHistory history : historyPage.getRecords()) {
            // 构建用户信息
            QueryDTO.UserInfo executedByInfo = QueryDTO.UserInfo.builder()
                .id(history.getExecutedBy())
                .name(history.getExecutedBy()) // 这里应该从用户服务获取用户名
                .build();

            // 构建DTO
            ExecutionHistoryDTO dto = ExecutionHistoryDTO.builder()
                .id(history.getId())
                .queryId(history.getQueryId())
                .versionId(history.getVersionId())
                .executedBy(executedByInfo)
                .executedAt(history.getExecutedAt())
                .status(history.getStatus())
                .duration(history.getDuration())
                .rowCount(history.getRowCount())
                .resultId(history.getResultId())
                .build();

            // 如果有参数，尝试解析为对象
            if (history.getParameters() != null && !history.getParameters().isEmpty()) {
                try {
                    dto.setParameters(new ObjectMapper().readValue(history.getParameters(), Map.class));
                } catch (Exception e) {
                    log.error("解析执行参数失败: {}", e.getMessage());
                    // 如果解析失败，直接设置字符串
                    dto.setParameters(history.getParameters());
                }
            }

            historyDTOList.add(dto);
        }

        resultPage.setRecords(historyDTOList);

        log.info("查询执行历史结果: 总数={}, 页数={}", resultPage.getTotal(), resultPage.getPages());
        return resultPage;
    }

    @Override
    public QueryDefinitionDTO getQueryParameters(String id) {
        // 获取查询信息
        Query query = queryMapper.selectById(id);
        if (query == null) {
            throw new BusinessException("查询不存在");
        }

        // 优先获取已发布版本的SQL
        QueryVersion version = queryVersionMapper.selectOne(
            new LambdaQueryWrapper<QueryVersion>()
                .eq(QueryVersion::getQueryId, id)
                .eq(QueryVersion::getStatus, "PUBLISHED")
                .orderByDesc(QueryVersion::getVersionNumber)
                .last("LIMIT 1")
        );

        // 如果没有发布态的版本，则获取最新版本
        if (version == null) {
            version = queryVersionMapper.selectOne(
                new LambdaQueryWrapper<QueryVersion>()
                    .eq(QueryVersion::getQueryId, id)
                    .orderByDesc(QueryVersion::getVersionNumber)
                    .last("LIMIT 1")
            );
        }

        if (version == null) {
            throw new BusinessException("查询版本不存在");
        }

        // 查询该版本的保存参数
        List<QueryParameterDTO> savedParameters = getVersionParameters(version.getId());

        // 如果有保存的参数，直接返回
        if (savedParameters != null && !savedParameters.isEmpty()) {
            QueryDefinitionDTO definition = new QueryDefinitionDTO();
            definition.setParameters(savedParameters);
            definition.setSql(version.getSqlContent());
            definition.setDataSourceId(query.getDataSourceId());

            // 尝试提取字段信息
            try {
                Datasource dataSource = dataSourceService.getById(query.getDataSourceId());
                if (dataSource != null) {
                    List<QueryFieldDTO> fields = sqlAnalyzer.extractFields(version.getSqlContent(), dataSource);
                    definition.setFields(fields);

                    definition.setSearchFields(sqlAnalyzer.extractSearchFields(version.getSqlContent(), dataSource));
                }
            } catch (Exception e) {
                log.warn("提取字段信息失败: {}", e.getMessage());
            }

            return definition;
        }

        // 如果没有保存的参数，分析SQL获取参数
        return analyzeQueryDefinition(version.getSqlContent(), query.getDataSourceId());
    }

    @Override
    public QueryDefinitionDTO analyzeQueryParameters(AnalyzeQueryParams params) {
        return analyzeQueryDefinition(params.getSqlText(), params.getDataSourceId());
    }

    @Override
    public ExecutionPlanDTO getExecutionPlan(String queryId) {
        log.info("获取查询[{}]执行计划", queryId);

        // 从数据库获取查询信息
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("获取执行计划失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 检查查询服务状态
        if ("DISABLED".equals(query.getServiceStatus())) {
            log.error("获取执行计划失败，查询已禁用: {}", queryId);
            throw new RuntimeException("查询已禁用，无法获取执行计划: " + queryId);
        }

        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(query.getDataSourceId());
        if (datasource == null) {
            log.error("获取执行计划失败，数据源不存在: {}", query.getDataSourceId());
            throw new RuntimeException("数据源不存在: " + query.getDataSourceId());
        }

        // 获取最新版本的SQL
        LambdaQueryWrapper<QueryVersion> versionWrapper = new LambdaQueryWrapper<>();
        versionWrapper.eq(QueryVersion::getQueryId, queryId)
            .eq(QueryVersion::getIsLatest, true);
        QueryVersion latestVersion = queryVersionMapper.selectOne(versionWrapper);

        if (latestVersion == null) {
            log.error("获取执行计划失败，查询版本不存在: {}", queryId);
            throw new RuntimeException("查询版本不存在: " + queryId);
        }

        String sql = latestVersion.getSqlContent();
        log.info("获取执行计划的SQL: {}", sql);

        // 移除SQL注释
        sql = MybatisSqlParser.parseSql(sql, null);
        sql = removeComments(sql);
        log.info("移除注释后的SQL: {}", sql);

        // 处理SQL中的变量
        sql = SqlVariableRemover.removeVariables(sql);
        log.info("处理变量后的SQL: {}", sql);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 构建数据源连接URL
        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);

        // 设置数据库连接属性
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(datasource.getUsername());
        dataSource.setPassword(datasource.getPassword());
        dataSource.setDriverClassName(JdbcUrlBuilder.getDriverClassName(datasource.getType()));

        // 创建JDBC模板
        JdbcTemplate template = new JdbcTemplate(dataSource);

        // 根据数据库类型构建EXPLAIN语句并执行
        List<Map<String, Object>> planDetails = new ArrayList<>();
        String planText = "";
        String databaseType = datasource.getType().toLowerCase();

        try {
            switch (databaseType) {
                case "mysql":
                    // MySQL的EXPLAIN语句
                    String explainSql = "EXPLAIN " + sql;
                    planDetails = template.queryForList(explainSql);
                    // 获取文本格式的执行计划
                    planText = getMySQLPlanText(template, sql);
                    break;

                case "postgresql":
                    // PostgreSQL的EXPLAIN语句
                    explainSql = "EXPLAIN (FORMAT JSON) " + sql;
                    List<Map<String, Object>> pgResult = template.queryForList(explainSql);
                    if (!pgResult.isEmpty() && pgResult.get(0).containsKey("QUERY PLAN")) {
                        planText = pgResult.get(0).get("QUERY PLAN").toString();
                        // 解析JSON格式的执行计划
                        try {
                            ObjectMapper mapper = new ObjectMapper();
                            JsonNode planNode = mapper.readTree(planText);
                            // 将JSON转换为Map列表
                            planDetails = convertJsonNodeToMapList(planNode);
                        } catch (Exception e) {
                            log.error("解析PostgreSQL执行计划JSON失败", e);
                            // 如果JSON解析失败，使用原始文本
                            Map<String, Object> planMap = new HashMap<>();
                            planMap.put("plan", planText);
                            planDetails.add(planMap);
                        }
                    }
                    // 获取文本格式的执行计划
                    explainSql = "EXPLAIN " + sql;
                    List<Map<String, Object>> pgTextResult = template.queryForList(explainSql);
                    StringBuilder textBuilder = new StringBuilder();
                    for (Map<String, Object> row : pgTextResult) {
                        if (row.containsKey("QUERY PLAN")) {
                            textBuilder.append(row.get("QUERY PLAN")).append("\n");
                        }
                    }
                    planText = textBuilder.toString();
                    break;

                case "oracle":
                    // Oracle的执行计划需要使用EXPLAIN PLAN FOR语句
                    // 先创建执行计划
                    template.execute("EXPLAIN PLAN FOR " + sql);
                    // 然后从PLAN_TABLE中查询执行计划
                    planDetails = template.queryForList(
                        "SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY())");
                    // 构建文本格式的执行计划
                    textBuilder = new StringBuilder();
                    for (Map<String, Object> row : planDetails) {
                        for (Map.Entry<String, Object> entry : row.entrySet()) {
                            if (entry.getValue() != null) {
                                textBuilder.append(entry.getValue()).append(" ");
                            }
                        }
                        textBuilder.append("\n");
                    }
                    planText = textBuilder.toString();
                    break;

                case "sqlserver":
                    // SQL Server的执行计划
                    explainSql = "SET SHOWPLAN_ALL ON; " + sql + "; SET SHOWPLAN_ALL OFF;";
                    try {
                        planDetails = template.queryForList(explainSql);
                    } catch (Exception e) {
                        // SQL Server可能会抛出异常，因为SET SHOWPLAN_ALL ON会阻止实际执行
                        log.warn("SQL Server执行计划查询异常，尝试使用其他方式", e);
                        // 尝试使用估计执行计划
                        explainSql = "SET STATISTICS PROFILE ON; " + sql + "; SET STATISTICS PROFILE OFF;";
                        try {
                            planDetails = template.queryForList(explainSql);
                        } catch (Exception e2) {
                            log.error("获取SQL Server执行计划失败", e2);
                            Map<String, Object> errorMap = new HashMap<>();
                            errorMap.put("error", "无法获取SQL Server执行计划: " + e2.getMessage());
                            planDetails.add(errorMap);
                        }
                    }
                    // 构建文本格式的执行计划
                    textBuilder = new StringBuilder();
                    for (Map<String, Object> row : planDetails) {
                        for (Map.Entry<String, Object> entry : row.entrySet()) {
                            if (entry.getValue() != null) {
                                textBuilder.append(entry.getKey()).append(": ")
                                    .append(entry.getValue()).append("\n");
                            }
                        }
                        textBuilder.append("---\n");
                    }
                    planText = textBuilder.toString();
                    break;

                case "db2":
                    // DB2的执行计划
                    // 使用EXPLAIN语句获取执行计划
                    try {
                        // 先创建一个临时表来存储执行计划
                        template.execute("EXPLAIN PLAN FOR " + sql);
                        // 从EXPLAIN_STATEMENT表中查询执行计划
                        planDetails = template.queryForList(
                            "SELECT * FROM SYSTOOLS.EXPLAIN_STATEMENT");
                        // 构建文本格式的执行计划
                        textBuilder = new StringBuilder();
                        for (Map<String, Object> row : planDetails) {
                            for (Map.Entry<String, Object> entry : row.entrySet()) {
                                if (entry.getValue() != null) {
                                    textBuilder.append(entry.getKey()).append(": ")
                                        .append(entry.getValue()).append("\n");
                                }
                            }
                            textBuilder.append("---\n");
                        }
                        planText = textBuilder.toString();
                    } catch (Exception e) {
                        log.error("获取DB2执行计划失败", e);
                        Map<String, Object> errorMap = new HashMap<>();
                        errorMap.put("error", "无法获取DB2执行计划: " + e.getMessage());
                        planDetails.add(errorMap);
                        planText = "无法获取DB2执行计划: " + e.getMessage();
                    }
                    break;

                default:
                    log.error("不支持的数据库类型: {}", databaseType);
                    throw new RuntimeException("不支持的数据库类型: " + databaseType);
            }
        } catch (Exception e) {
            log.error("获取执行计划失败", e);
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "获取执行计划失败: " + e.getMessage());
            planDetails.add(errorMap);
            planText = "获取执行计划失败: " + e.getMessage();
        }

        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;

        // 生成唯一ID
        String planId = UUID.randomUUID().toString().replace("-", "");

        // 构建执行计划详情
        ExecutionPlanDTO.PlanDetails planDetailsObj = parsePlanDetails(planDetails, databaseType);

        // 构建返回结果
        return ExecutionPlanDTO.builder()
            .id(planId)
            .queryId(queryId)
            .createdAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .estimatedCost(planDetailsObj != null ? planDetailsObj.getTotalCost() : null)
            .estimatedRows(planDetailsObj != null ? planDetailsObj.getEstimatedRows() : null)
            .planningTime(executionTime)
            .executionTime(null) // 执行计划阶段无法获取实际执行时间
            .plan(planText)
            .planDetails(planDetailsObj)
            .build();
    }

    /**
     * 获取MySQL文本格式的执行计划
     */
    private String getMySQLPlanText(JdbcTemplate template, String sql) {
        try {
            // 使用EXPLAIN FORMAT=TRADITIONAL获取文本格式的执行计划
            List<Map<String, Object>> result = template.queryForList("EXPLAIN FORMAT=TRADITIONAL " + sql);
            StringBuilder textBuilder = new StringBuilder();

            // 获取所有列名
            if (!result.isEmpty()) {
                Map<String, Object> firstRow = result.get(0);
                for (String column : firstRow.keySet()) {
                    textBuilder.append(column).append("\t");
                }
                textBuilder.append("\n");

                // 添加每一行的数据
                for (Map<String, Object> row : result) {
                    for (Object value : row.values()) {
                        textBuilder.append(value != null ? value : "NULL").append("\t");
                    }
                    textBuilder.append("\n");
                }
            }

            return textBuilder.toString();
        } catch (Exception e) {
            log.error("获取MySQL文本格式执行计划失败", e);
            return "获取MySQL文本格式执行计划失败: " + e.getMessage();
        }
    }

    /**
     * 将JsonNode转换为Map列表
     */
    private List<Map<String, Object>> convertJsonNodeToMapList(JsonNode node) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (node.isArray()) {
            // 如果是数组，处理每个元素
            for (JsonNode item : node) {
                if (item.isObject()) {
                    result.add(convertJsonNodeToMap(item));
                }
            }
        } else if (node.isObject()) {
            // 如果是对象，直接转换
            result.add(convertJsonNodeToMap(node));
        }

        return result;
    }

    /**
     * 将JsonNode转换为Map
     */
    private Map<String, Object> convertJsonNodeToMap(JsonNode node) {
        Map<String, Object> map = new HashMap<>();

        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (value.isTextual()) {
                map.put(key, value.asText());
            } else if (value.isNumber()) {
                map.put(key, value.asDouble());
            } else if (value.isBoolean()) {
                map.put(key, value.asBoolean());
            } else if (value.isArray() || value.isObject()) {
                // 递归处理嵌套结构
                map.put(key, value.toString());
            } else if (value.isNull()) {
                map.put(key, null);
            } else {
                map.put(key, value.toString());
            }
        }

        return map;
    }

    /**
     * 解析执行计划详情
     *
     * @param planDetails  原始执行计划数据
     * @param databaseType 数据库类型
     * @return 结构化的执行计划详情
     */
    private ExecutionPlanDTO.PlanDetails parsePlanDetails(List<Map<String, Object>> planDetails, String databaseType) {
        if (planDetails == null || planDetails.isEmpty()) {
            return null;
        }

        ExecutionPlanDTO.PlanDetails result = new ExecutionPlanDTO.PlanDetails();
        List<ExecutionPlanDTO.PlanStep> steps = new ArrayList<>();
        Double totalCost = 0.0;
        Long totalRows = 0L;

        try {
            switch (databaseType) {
                case "mysql":
                    // 解析MySQL执行计划
                    for (Map<String, Object> detail : planDetails) {
                        ExecutionPlanDTO.PlanStep step = new ExecutionPlanDTO.PlanStep();

                        // 设置步骤类型
                        step.setType(detail.containsKey("select_type") ? detail.get("select_type").toString() : null);

                        // 设置表名
                        step.setTable(detail.containsKey("table") ? detail.get("table").toString() : null);

                        // 设置索引
                        step.setIndex(detail.containsKey("key") ? detail.get("key").toString() : null);

                        // 设置条件
                        step.setCondition(detail.containsKey("Extra") ? detail.get("Extra").toString() : null);

                        // 设置行数
                        if (detail.containsKey("rows")) {
                            Object rowsObj = detail.get("rows");
                            if (rowsObj != null) {
                                try {
                                    step.setRows(Long.valueOf(rowsObj.toString()));
                                    totalRows += step.getRows();
                                } catch (NumberFormatException e) {
                                    log.warn("无法解析行数: {}", rowsObj);
                                }
                            }
                        }

                        // 设置成本
                        // MySQL执行计划中没有直接的成本信息，使用行数作为估算
                        if (step.getRows() != null) {
                            step.setCost((double) step.getRows());
                            totalCost += step.getCost();
                        }

                        steps.add(step);
                    }
                    break;

                case "postgresql":
                    // PostgreSQL执行计划解析需要特殊处理
                    // 这里简化处理，实际实现中需要递归解析JSON结构
                    if (!planDetails.isEmpty() && planDetails.get(0).containsKey("Plan")) {
                        Map<String, Object> plan = (Map<String, Object>) planDetails.get(0).get("Plan");
                        ExecutionPlanDTO.PlanStep step = new ExecutionPlanDTO.PlanStep();

                        step.setType(plan.containsKey("Node Type") ? plan.get("Node Type").toString() : null);
                        step.setTable(plan.containsKey("Relation Name") ? plan.get("Relation Name").toString() : null);
                        step.setCondition(plan.containsKey("Filter") ? plan.get("Filter").toString() : null);

                        if (plan.containsKey("Plan Rows")) {
                            Object rowsObj = plan.get("Plan Rows");
                            if (rowsObj != null) {
                                try {
                                    step.setRows(Long.valueOf(rowsObj.toString()));
                                    totalRows = step.getRows();
                                } catch (NumberFormatException e) {
                                    log.warn("无法解析行数: {}", rowsObj);
                                }
                            }
                        }

                        if (plan.containsKey("Total Cost")) {
                            Object costObj = plan.get("Total Cost");
                            if (costObj != null) {
                                try {
                                    step.setCost(Double.valueOf(costObj.toString()));
                                    totalCost = step.getCost();
                                } catch (NumberFormatException e) {
                                    log.warn("无法解析成本: {}", costObj);
                                }
                            }
                        }

                        steps.add(step);
                    }
                    break;

                case "oracle":
                case "sqlserver":
                case "db2":
                    // 其他数据库类型的执行计划解析
                    // 简化处理，实际实现中需要根据具体数据库类型进行特殊处理
                    ExecutionPlanDTO.PlanStep step = new ExecutionPlanDTO.PlanStep();
                    step.setType("UNKNOWN");
                    step.setTable("UNKNOWN");
                    step.setCondition("See plan text for details");
                    steps.add(step);
                    break;

                default:
                    log.warn("不支持的数据库类型: {}", databaseType);
                    return null;
            }

            result.setSteps(steps);
            result.setTotalCost(totalCost);
            result.setEstimatedRows(totalRows);

            return result;
        } catch (Exception e) {
            log.error("解析执行计划详情失败", e);
            return null;
        }
    }

    @Override
    public ExecutionPlanDTO getExecutionPlanForSQL(ExecutionPlanParams params) {
        log.info("获取SQL执行计划，数据源ID: {}, SQL: {}", params.getDataSourceId(), params.getSql());

        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(params.getDataSourceId());
        if (datasource == null) {
            log.error("获取执行计划失败，数据源不存在: {}", params.getDataSourceId());
            throw new RuntimeException("数据源不存在: " + params.getDataSourceId());
        }

        String sql = params.getSql();
        // 校验SQL是否为空
        if (sql == null || sql.trim().isEmpty()) {
            log.error("获取执行计划失败，SQL语句为空");
            throw new RuntimeException("SQL语句不能为空");
        }

        log.info("获取执行计划的SQL: {}", sql);

        // 移除SQL注释
        sql = MybatisSqlParser.parseSql(sql, params.getParameters());
        sql = removeComments(sql);
        log.info("移除注释后的SQL: {}", sql);

        // 处理SQL中的变量
        sql = SqlVariableRemover.removeVariables(sql);
        log.info("处理变量后的SQL: {}", sql);

        // 替换SQL参数
        if (params.getParameters() != null && !params.getParameters().isEmpty()) {
            sql = replaceSQLParameters(sql, params.getParameters());
            log.info("替换参数后的SQL: {}", sql);
        }

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 构建数据源连接URL
        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);

        // 设置数据库连接属性
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(datasource.getUsername());
        dataSource.setPassword(datasource.getPassword());
        dataSource.setDriverClassName(JdbcUrlBuilder.getDriverClassName(datasource.getType()));

        // 创建JDBC模板
        JdbcTemplate template = new JdbcTemplate(dataSource);

        // 根据数据库类型构建EXPLAIN语句并执行
        List<Map<String, Object>> planDetails = new ArrayList<>();
        String planText = "";
        String databaseType = datasource.getType().toLowerCase();

        try {
            switch (databaseType) {
                case "mysql":
                    // MySQL的EXPLAIN语句
                    String explainSql = "EXPLAIN " + sql;
                    planDetails = template.queryForList(explainSql);
                    // 获取文本格式的执行计划
                    planText = getMySQLPlanText(template, sql);
                    break;

                case "postgresql":
                    // PostgreSQL的EXPLAIN语句
                    explainSql = "EXPLAIN (FORMAT JSON) " + sql;
                    List<Map<String, Object>> pgResult = template.queryForList(explainSql);
                    if (!pgResult.isEmpty() && pgResult.get(0).containsKey("QUERY PLAN")) {
                        planText = pgResult.get(0).get("QUERY PLAN").toString();
                        // 解析JSON格式的执行计划
                        try {
                            ObjectMapper mapper = new ObjectMapper();
                            JsonNode planNode = mapper.readTree(planText);
                            // 将JSON转换为Map列表
                            planDetails = convertJsonNodeToMapList(planNode);
                        } catch (Exception e) {
                            log.error("解析PostgreSQL执行计划JSON失败", e);
                            // 如果JSON解析失败，使用原始文本
                            Map<String, Object> planMap = new HashMap<>();
                            planMap.put("plan", planText);
                            planDetails.add(planMap);
                        }
                    }
                    // 获取文本格式的执行计划
                    explainSql = "EXPLAIN " + sql;
                    List<Map<String, Object>> pgTextResult = template.queryForList(explainSql);
                    StringBuilder textBuilder = new StringBuilder();
                    for (Map<String, Object> row : pgTextResult) {
                        if (row.containsKey("QUERY PLAN")) {
                            textBuilder.append(row.get("QUERY PLAN")).append("\n");
                        }
                    }
                    planText = textBuilder.toString();
                    break;

                case "oracle":
                    // Oracle的执行计划需要使用EXPLAIN PLAN FOR语句
                    // 先创建执行计划
                    template.execute("EXPLAIN PLAN FOR " + sql);
                    // 然后从PLAN_TABLE中查询执行计划
                    planDetails = template.queryForList(
                        "SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY())");
                    // 构建文本格式的执行计划
                    textBuilder = new StringBuilder();
                    for (Map<String, Object> row : planDetails) {
                        for (Map.Entry<String, Object> entry : row.entrySet()) {
                            if (entry.getValue() != null) {
                                textBuilder.append(entry.getValue()).append(" ");
                            }
                        }
                        textBuilder.append("\n");
                    }
                    planText = textBuilder.toString();
                    break;

                case "sqlserver":
                    // SQL Server的执行计划
                    explainSql = "SET SHOWPLAN_ALL ON; " + sql + "; SET SHOWPLAN_ALL OFF;";
                    try {
                        planDetails = template.queryForList(explainSql);
                    } catch (Exception e) {
                        // SQL Server可能会抛出异常，因为SET SHOWPLAN_ALL ON会阻止实际执行
                        log.warn("SQL Server执行计划查询异常，尝试使用其他方式", e);
                        // 尝试使用估计执行计划
                        explainSql = "SET STATISTICS PROFILE ON; " + sql + "; SET STATISTICS PROFILE OFF;";
                        try {
                            planDetails = template.queryForList(explainSql);
                        } catch (Exception e2) {
                            log.error("获取SQL Server执行计划失败", e2);
                            Map<String, Object> errorMap = new HashMap<>();
                            errorMap.put("error", "无法获取SQL Server执行计划: " + e2.getMessage());
                            planDetails.add(errorMap);
                        }
                    }
                    // 构建文本格式的执行计划
                    textBuilder = new StringBuilder();
                    for (Map<String, Object> row : planDetails) {
                        for (Map.Entry<String, Object> entry : row.entrySet()) {
                            if (entry.getValue() != null) {
                                textBuilder.append(entry.getKey()).append(": ")
                                    .append(entry.getValue()).append("\n");
                            }
                        }
                        textBuilder.append("---\n");
                    }
                    planText = textBuilder.toString();
                    break;

                case "db2":
                    // DB2的执行计划
                    // 使用EXPLAIN语句获取执行计划
                    try {
                        // 先创建一个临时表来存储执行计划
                        template.execute("EXPLAIN PLAN FOR " + sql);
                        // 从EXPLAIN_STATEMENT表中查询执行计划
                        planDetails = template.queryForList(
                            "SELECT * FROM SYSTOOLS.EXPLAIN_STATEMENT");
                        // 构建文本格式的执行计划
                        textBuilder = new StringBuilder();
                        for (Map<String, Object> row : planDetails) {
                            for (Map.Entry<String, Object> entry : row.entrySet()) {
                                if (entry.getValue() != null) {
                                    textBuilder.append(entry.getKey()).append(": ")
                                        .append(entry.getValue()).append("\n");
                                }
                            }
                            textBuilder.append("---\n");
                        }
                        planText = textBuilder.toString();
                    } catch (Exception e) {
                        log.error("获取DB2执行计划失败", e);
                        Map<String, Object> errorMap = new HashMap<>();
                        errorMap.put("error", "无法获取DB2执行计划: " + e.getMessage());
                        planDetails.add(errorMap);
                        planText = "无法获取DB2执行计划: " + e.getMessage();
                    }
                    break;

                default:
                    log.error("不支持的数据库类型: {}", databaseType);
                    throw new RuntimeException("不支持的数据库类型: " + databaseType);
            }
        } catch (Exception e) {
            log.error("获取执行计划失败", e);
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "获取执行计划失败: " + e.getMessage());
            planDetails.add(errorMap);
            planText = "获取执行计划失败: " + e.getMessage();
        }

        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;

        // 生成唯一ID
        String planId = UUID.randomUUID().toString().replace("-", "");

        // 构建执行计划详情
        ExecutionPlanDTO.PlanDetails planDetailsObj = parsePlanDetails(planDetails, databaseType);

        // 构建返回结果
        return ExecutionPlanDTO.builder()
            .id(planId)
            .queryId(null) // 未保存的查询没有ID
            .createdAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .estimatedCost(planDetailsObj != null ? planDetailsObj.getTotalCost() : null)
            .estimatedRows(planDetailsObj != null ? planDetailsObj.getEstimatedRows() : null)
            .planningTime(executionTime)
            .executionTime(null) // 执行计划阶段无法获取实际执行时间
            .plan(planText)
            .planDetails(planDetailsObj)
            .build();
    }

    /**
     * 分析SQL获取查询定义信息
     *
     * @param sql          SQL语句
     * @param dataSourceId 数据源ID
     * @return 查询定义信息
     */
    private QueryDefinitionDTO analyzeQueryDefinition(String sql, String dataSourceId) {
        // 验证数据源
        Datasource dataSource = dataSourceService.getById(dataSourceId);
        if (dataSource == null) {
            throw new BusinessException("数据源不存在");
        }

        // 分析SQL，获取参数和字段信息
        QueryDefinitionDTO definition = new QueryDefinitionDTO();
        try {
            // 使用SQL解析器分析SQL
            List<QueryParameterDTO> parameters = sqlAnalyzer.extractParameters(sql);

            List<QueryFieldDTO> fields = sqlAnalyzer.extractFields(sql, dataSource);

            // 根据 fields 的 label 填充 parameters 的 label
            for (QueryParameterDTO parameter : parameters) {
                for (QueryFieldDTO field : fields) {
                    if (field.getName().equalsIgnoreCase(parameter.getName())) {
                        parameter.setLabel(field.getLabel());
                    }
                }
            }

            definition.setParameters(parameters);
            definition.setFields(fields);
            definition.setSql(sql);
            definition.setDataSourceId(dataSourceId);
            definition.setSearchFields(sqlAnalyzer.extractSearchFields(sql, dataSource));

            return definition;
        } catch (Exception e) {
            log.error("分析SQL失败: {}", e.getMessage(), e);
            throw new BusinessException("SQL分析失败: " + e.getMessage());
        }
    }

    @Override
    public Page<QueryVersionDTO> getQueryVersions(String queryId, int page, int size, String status) {
        log.info("获取查询[{}]版本列表, page={}, size={}, status={}", queryId, page, size, status);

        // 创建分页对象
        Page<QueryVersion> versionPage = new Page<>(page, size);

        // 构建查询条件
        LambdaQueryWrapper<QueryVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryVersion::getQueryId, queryId);

        // 添加状态过滤
        if (status != null && !status.trim().isEmpty()) {
            wrapper.eq(QueryVersion::getStatus, status);
        }

        // 按版本号降序排序
        wrapper.orderByDesc(QueryVersion::getVersionNumber);

        // 执行查询
        versionPage = queryVersionMapper.selectPage(versionPage, wrapper);

        // 创建结果分页对象
        Page<QueryVersionDTO> resultPage = new Page<>(page, size);
        resultPage.setTotal(versionPage.getTotal());
        resultPage.setPages(versionPage.getPages());

        // 转换结果
        List<QueryVersionDTO> versionDTOList = new ArrayList<>();
        for (QueryVersion version : versionPage.getRecords()) {
            QueryVersionDTO dto = QueryVersionDTO.builder()
                .id(version.getId())
                .queryId(version.getQueryId())
                .versionNumber(version.getVersionNumber())
                .name(version.getName())
                .description(version.getDescription())
                .sql(version.getSqlContent())
                .dataSourceId(version.getDataSourceId())
                .status(version.getStatus())
                .isLatest(version.getIsLatest())
                .createdAt(version.getCreatedAt())
                .updatedAt(version.getUpdatedAt())
                .build();

            // 设置数据源信息
            setDataSourceInfo(dto);

            versionDTOList.add(dto);
        }

        resultPage.setRecords(versionDTOList);

        log.info("查询版本列表结果: 总数={}, 页数={}", resultPage.getTotal(), resultPage.getPages());
        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryVersionDTO createQueryVersion(String queryId, String sqlContent, String description, String comment) {
        log.info("创建查询[{}]版本, SQL内容长度={}, 描述={}", queryId,
            sqlContent != null ? sqlContent.length() : 0, description);

        // 检查查询是否存在
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("创建版本失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 查询当前最新版本
        // 按照数据库的最大 versionNumber 获取

        LambdaQueryWrapper<QueryVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryVersion::getQueryId, queryId)
            .orderByDesc(QueryVersion::getVersionNumber)
            .last(" limit 1");
            // .eq(QueryVersion::getIsLatest, true);
        QueryVersion latestVersion = queryVersionMapper.selectOne(wrapper);

        // 计算新版本号
        int versionNumber = 1;
        if (latestVersion != null) {
            versionNumber = latestVersion.getVersionNumber() + 1;
        }

        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 创建新版本
        QueryVersion newVersion = QueryVersion.builder()
            .id(UUID.randomUUID().toString().replace("-", ""))
            .queryId(queryId)
            .versionNumber(versionNumber)
            .name("版本 " + versionNumber)
            .description(description)
            .sqlContent(sqlContent)
            .dataSourceId(query.getDataSourceId())
            .status("DRAFT")
            .comment(comment)
            .isLatest(true)
            .createdBy(currentUser)
            .createdAt(now)
            .updatedBy(currentUser)
            .updatedAt(now)
            .build();

        // 如果存在最新版本，更新其isLatest为false
        if (latestVersion != null) {
            latestVersion.setIsLatest(false);
            queryVersionMapper.updateById(latestVersion);
        }

        // 保存新版本
        queryVersionMapper.insert(newVersion);

        // 提取并保存参数信息
        if (sqlContent != null && !sqlContent.trim().isEmpty()) {
            saveQueryParameters(queryId, newVersion.getId(), sqlContent, currentUser, now);
        }

        // 构建返回DTO
        QueryVersionDTO dto = QueryVersionDTO.builder()
            .id(newVersion.getId())
            .queryId(newVersion.getQueryId())
            .versionNumber(newVersion.getVersionNumber())
            .name(newVersion.getName())
            .description(newVersion.getDescription())
            .sql(newVersion.getSqlContent())
            .dataSourceId(newVersion.getDataSourceId())
            .status(newVersion.getStatus())
            .isLatest(newVersion.getIsLatest())
            .createdAt(newVersion.getCreatedAt())
            .updatedAt(newVersion.getUpdatedAt())
            .build();

        // 设置数据源信息
        setDataSourceInfo(dto);

        log.info("创建查询版本成功: id={}, 版本号={}", dto.getId(), dto.getVersionNumber());
        return dto;
    }

    @Override
    public QueryVersionDTO getQueryVersion(String versionId) {
        log.info("获取查询版本详情: {}", versionId);

        // 从数据库中查询
        QueryVersion version = queryVersionMapper.selectById(versionId);
        if (version == null) {
            log.error("查询版本不存在: {}", versionId);
            throw new RuntimeException("查询版本不存在: " + versionId);
        }

        // 转换为DTO
        QueryVersionDTO dto = QueryVersionDTO.builder()
            .id(version.getId())
            .queryId(version.getQueryId())
            .versionNumber(version.getVersionNumber())
            .name(version.getName())
            .description(version.getDescription())
            .sql(version.getSqlContent())
            .dataSourceId(version.getDataSourceId())
            .status(version.getStatus())
            .isLatest(version.getIsLatest())
            .createdAt(version.getCreatedAt())
            .updatedAt(version.getUpdatedAt())
            .build();

        // 设置数据源信息
        setDataSourceInfo(dto);

        // 获取参数信息
        List<QueryParameterDTO> parameters = getVersionParameters(versionId);
        if (parameters != null && !parameters.isEmpty()) {
            dto.setParameters(parameters);
        }

        log.info("查询版本详情获取成功: {}", versionId);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryVersionDTO updateQueryVersion(String versionId, String sqlContent, String description, String comment) {
        log.info("更新查询版本: {}, SQL内容长度={}, 描述={}", versionId,
            sqlContent != null ? sqlContent.length() : 0, description);

        // 从数据库中查询
        QueryVersion version = queryVersionMapper.selectById(versionId);
        if (version == null) {
            log.error("查询版本不存在: {}", versionId);
            throw new RuntimeException("查询版本不存在: " + versionId);
        }

        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 更新版本信息
        version.setSqlContent(sqlContent);
        if (description != null) {
            version.setDescription(description);
        }
        if (comment != null) {
            version.setComment(comment);
        }
        version.setUpdatedBy(currentUser);
        version.setUpdatedAt(now);

        // 保存到数据库
        int result = queryVersionMapper.updateById(version);
        log.info("查询版本更新结果: {}, 影响行数: {}", result > 0 ? "成功" : "失败", result);

        if (result <= 0) {
            log.error("更新查询版本失败: {}", versionId);
            throw new RuntimeException("更新查询版本失败");
        }

        // 更新参数信息
        // 先删除旧参数
        LambdaQueryWrapper<QueryParameter> paramWrapper = new LambdaQueryWrapper<>();
        paramWrapper.eq(QueryParameter::getVersionId, versionId);
        queryParameterMapper.delete(paramWrapper);

        // 提取并保存新参数
        if (sqlContent != null && !sqlContent.trim().isEmpty()) {
            saveQueryParameters(version.getQueryId(), versionId, sqlContent, currentUser, now);
        }

        // 构建返回DTO
        QueryVersionDTO dto = QueryVersionDTO.builder()
            .id(version.getId())
            .queryId(version.getQueryId())
            .versionNumber(version.getVersionNumber())
            .name(version.getName())
            .description(version.getDescription())
            .sql(version.getSqlContent())
            .dataSourceId(version.getDataSourceId())
            .status(version.getStatus())
            .isLatest(version.getIsLatest())
            .createdAt(version.getCreatedAt())
            .updatedAt(version.getUpdatedAt())
            .build();

        // 设置数据源信息
        setDataSourceInfo(dto);

        log.info("查询版本更新完成: {}", dto.getId());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryVersionDTO publishQueryVersion(String versionId) {
        log.info("发布查询版本: {}", versionId);

        // 从数据库中查询版本
        QueryVersion version = queryVersionMapper.selectById(versionId);
        if (version == null) {
            log.error("发布版本失败，查询版本不存在: {}", versionId);
            throw new RuntimeException("查询版本不存在: " + versionId);
        }

        String queryId = version.getQueryId();
        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 1. 将当前版本状态更新为"已发布"
        version.setStatus("PUBLISHED");
        version.setUpdatedBy(currentUser);
        version.setUpdatedAt(now);
        version.setIsLatest(true);
        queryVersionMapper.updateById(version);

        // 更新其他version为 isLatest=false
        LambdaQueryWrapper<QueryVersion> w = new LambdaQueryWrapper<>();
        w.eq(QueryVersion::getQueryId, queryId)
            .ne(QueryVersion::getId, versionId);
        List<QueryVersion> versions = queryVersionMapper.selectList(w);
        for (QueryVersion currVersion : versions) {
          currVersion.setIsLatest(false);
          currVersion.setUpdatedBy(currentUser);
          currVersion.setUpdatedAt(now);
            queryVersionMapper.updateById(currVersion);
        }

        // 更新该版本下参数的状态
        updateQueryParametersStatus(versionId, "PUBLISHED", currentUser, now);

        // 2. 如果存在之前的已发布版本，将其状态更新为"已废弃"
        LambdaQueryWrapper<QueryVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryVersion::getQueryId, queryId)
            .eq(QueryVersion::getStatus, "PUBLISHED")
            .ne(QueryVersion::getId, versionId);

        List<QueryVersion> publishedVersions = queryVersionMapper.selectList(wrapper);
        for (QueryVersion publishedVersion : publishedVersions) {
            publishedVersion.setStatus("DEPRECATED");
            publishedVersion.setUpdatedBy(currentUser);
            publishedVersion.setUpdatedAt(now);
            queryVersionMapper.updateById(publishedVersion);
            log.info("将已发布版本[{}]更新为已废弃状态", publishedVersion.getId());

            // 更新已废弃版本下参数的状态
            updateQueryParametersStatus(publishedVersion.getId(), "DEPRECATED", currentUser, now);
        }

        // 3. 更新查询的状态为"已发布"
        Query query = queryMapper.selectById(queryId);
        if (query != null) {
            query.setStatus("PUBLISHED");
            query.setUpdatedBy(currentUser);
            query.setUpdatedAt(now);
            queryMapper.updateById(query);
            log.info("更新查询[{}]状态为已发布", queryId);
        }

        // 构建返回DTO
        QueryVersionDTO dto = QueryVersionDTO.builder()
            .id(version.getId())
            .queryId(version.getQueryId())
            .versionNumber(version.getVersionNumber())
            .name(version.getName())
            .description(version.getDescription())
            .sql(version.getSqlContent())
            .dataSourceId(version.getDataSourceId())
            .status(version.getStatus())
            .isLatest(version.getIsLatest())
            .createdAt(version.getCreatedAt())
            .updatedAt(version.getUpdatedAt())
            .build();

        // 设置数据源信息
        setDataSourceInfo(dto);

        // 获取参数信息
        List<QueryParameterDTO> parameters = getVersionParameters(versionId);
        if (parameters != null && !parameters.isEmpty()) {
            dto.setParameters(parameters);
        }

        log.info("查询版本发布完成: {}", dto.getId());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public QueryVersionDTO deprecateQueryVersion(String versionId) {
        log.info("废弃查询版本: {}", versionId);

        // 从数据库中查询版本
        QueryVersion version = queryVersionMapper.selectById(versionId);
        if (version == null) {
            log.error("废弃版本失败，查询版本不存在: {}", versionId);
            throw new RuntimeException("查询版本不存在: " + versionId);
        }

        String queryId = version.getQueryId();
        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 将当前版本状态更新为"已废弃"
        version.setStatus("DEPRECATED");
        version.setUpdatedBy(currentUser);
        version.setUpdatedAt(now);
        queryVersionMapper.updateById(version);

        // 更新该版本下参数的状态
        updateQueryParametersStatus(versionId, "DEPRECATED", currentUser, now);

        // 更新查询状态
        Query query = queryMapper.selectById(queryId);
        if (query != null) {
            // 根据查询版本状态自动确定查询状态
            String newStatus = determineQueryStatus(queryId);
            query.setStatus(newStatus);
            query.setUpdatedBy(currentUser);
            query.setUpdatedAt(now);
            queryMapper.updateById(query);
            log.info("更新查询[{}]状态为{}", queryId, newStatus);
        }

        // 构建返回DTO
        QueryVersionDTO dto = QueryVersionDTO.builder()
            .id(version.getId())
            .queryId(version.getQueryId())
            .versionNumber(version.getVersionNumber())
            .name(version.getName())
            .description(version.getDescription())
            .sql(version.getSqlContent())
            .dataSourceId(version.getDataSourceId())
            .status(version.getStatus())
            .isLatest(version.getIsLatest())
            .createdAt(version.getCreatedAt())
            .updatedAt(version.getUpdatedAt())
            .build();

        // 设置数据源信息
        setDataSourceInfo(dto);

        log.info("查询版本废弃完成: {}", dto.getId());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean activateQueryVersion(String queryId, String versionId) {
        log.info("激活查询[{}]版本[{}]", queryId, versionId);

        // 检查查询是否存在
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("激活版本失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 检查版本是否存在
        QueryVersion version = queryVersionMapper.selectById(versionId);
        if (version == null) {
            log.error("激活版本失败，查询版本不存在: {}", versionId);
            throw new RuntimeException("查询版本不存在: " + versionId);
        }

        // 检查版本是否属于该查询
        if (!queryId.equals(version.getQueryId())) {
            log.error("激活版本失败，版本[{}]不属于查询[{}]", versionId, queryId);
            throw new RuntimeException("版本不属于该查询");
        }

        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 将所有版本的isLatest设置为false
        LambdaQueryWrapper<QueryVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryVersion::getQueryId, queryId)
            .eq(QueryVersion::getIsLatest, true);

        List<QueryVersion> latestVersions = queryVersionMapper.selectList(wrapper);
        for (QueryVersion latestVersion : latestVersions) {
            latestVersion.setIsLatest(false);
            latestVersion.setUpdatedBy(currentUser);
            latestVersion.setUpdatedAt(now);
            queryVersionMapper.updateById(latestVersion);
        }

        // 将指定版本设置为最新版本
        version.setIsLatest(true);
        version.setUpdatedBy(currentUser);
        version.setUpdatedAt(now);
        queryVersionMapper.updateById(version);

        // 更新查询状态
        String newStatus = determineQueryStatus(queryId);
        query.setStatus(newStatus);
        query.setUpdatedBy(currentUser);
        query.setUpdatedAt(now);
        queryMapper.updateById(query);

        log.info("激活查询版本成功，查询[{}]状态更新为{}", queryId, newStatus);
        return true;
    }

    @Override
    public QueryResultDTO executeQueryVersion(String queryId, String versionId, ExecuteQueryParams params) {
        log.info("开始执行查询版本: {}, 参数: {}", versionId, params);


        // 从数据库获取查询信息
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("执行查询失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 检查查询服务状态
        if ("DISABLED".equals(query.getServiceStatus())) {
            log.error("执行查询失败，查询已禁用: {}", queryId);
            throw new RuntimeException("查询已禁用，无法执行: " + queryId);
        }

        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(query.getDataSourceId());
        if (datasource == null) {
            log.error("执行查询失败，数据源不存在: {}", query.getDataSourceId());
            throw new RuntimeException("数据源不存在: " + query.getDataSourceId());
        }

        // 获取最新版本的SQL
        LambdaQueryWrapper<QueryVersion> versionWrapper = new LambdaQueryWrapper<>();
        versionWrapper.eq(QueryVersion::getQueryId, queryId)
            .eq(QueryVersion::getId, versionId);
        QueryVersion queryVersion = queryVersionMapper.selectOne(versionWrapper);

        if (queryVersion == null) {
            log.error("执行查询失败，查询版本不存在: {}", queryId);
            throw new RuntimeException("查询版本不存在: " + queryId);
        }

        String sql = queryVersion.getSqlContent();
        log.info("执行SQL: {}", sql);

        // 获取查询参数并处理默认值
        Map<String, Object> executionParams = processQueryParameters(versionId, params.getParameters());

        return executeSql(datasource, query, queryVersion, sql, ExecuteQueryParams.builder()
            .sql(sql)
            .parameters(executionParams)
            .sort(params.getSort())
            .build());
    }


    @Override
    public QueryResultDTO executeAppendQuery(String queryId, String versionId,
                                             String integrationId, ExecuteQueryParams params) {
        log.info("executeAppendQuery params: {}", params);

        // 从数据库获取查询信息
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("执行查询失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 检查查询服务状态
        if ("DISABLED".equals(query.getServiceStatus())) {
            log.error("执行查询失败，查询已禁用: {}", queryId);
            throw new RuntimeException("查询已禁用，无法执行: " + queryId);
        }

        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(query.getDataSourceId());
        if (datasource == null) {
            log.error("执行查询失败，数据源不存在: {}", query.getDataSourceId());
            throw new RuntimeException("数据源不存在: " + query.getDataSourceId());
        }

        // 获取最新版本的SQL
        LambdaQueryWrapper<QueryVersion> versionWrapper = new LambdaQueryWrapper<>();
        versionWrapper.eq(QueryVersion::getQueryId, queryId)
            .eq(QueryVersion::getId, versionId);
        QueryVersion queryVersion = queryVersionMapper.selectOne(versionWrapper);

        if (queryVersion == null) {
            log.error("执行查询失败，查询版本不存在: {}", queryId);
            throw new RuntimeException("查询版本不存在: " + queryId);
        }

        String sql = queryVersion.getSqlContent();
        log.info("执行SQL: {}", sql);

        // 获取查询参数并处理默认值
        Map<String, Object> executionParams = processQueryParameters(versionId, params.getParameters());

        return executeAppendSql(datasource, query, queryVersion, integrationId, sql, ExecuteQueryParams.builder()
            .sql(sql)
            .parameters(executionParams)
            .sort(params.getSort())
            .page(params.getPage())
            .size(params.getSize())
            .build());
    }

    private QueryResultDTO executeAppendSql(Datasource datasource, Query query,
                                            QueryVersion queryVersion,
                                            String integrationId,
                                            String sql,
                                            ExecuteQueryParams params) {
        // 获取查询涉及的表和列信息
        Set<QueryFieldDTO> queryFieldDTOS = getEncryptedColumns(sql, datasource.getId());
        Set<String> encryptedColumns = queryFieldDTOS.stream().map(QueryFieldDTO::getName).collect(Collectors.toSet());

        // 移除SQL注释
        sql = removeComments(sql);
        log.info("移除注释后的SQL: {}", sql);

        sql = MybatisSqlParser.parseSql(sql, params.getParameters());
        log.info("处理完标签后的SQL: {}", sql);

        sql = SqlVariableRemover.removeVariables(sql);
        log.info("removeVariables SQL: {}", sql);

        // 替换SQL参数，处理加密列的参数值
        if (params.getParameters() != null && !params.getParameters().isEmpty()) {
            Map<String, Object> processedParams = new HashMap<>(params.getParameters());
            // 处理加密列的参数值
            for (Map.Entry<String, Object> entry : params.getParameters().entrySet()) {
                String paramName = entry.getKey();
                if (encryptedColumns.contains(paramName)) {
                    Object value = entry.getValue();
                    if (value != null) {
                        processedParams.put(paramName, DoorgodSMUtils.encode(String.valueOf(value)));
                    }
                }
            }
            IntegrationDTO integration = this.integrationService.getIntegrationById(integrationId);
            List<FieldRule> fieldRules = Lists.newArrayList();
            if (Objects.nonNull(integration)) {
                List<CreateIntegrationRequest.QueryParam> queryParams = integration.getQueryParams();
                if (CollUtil.isNotEmpty(queryParams)) {
                    List<String> fields = queryParams.stream()
                        .map(CreateIntegrationRequest.QueryParam::getName).collect(Collectors.toList());
                    processedParams = processedParams.entrySet().stream()
                        .filter(entry -> fields.contains(entry.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    Map<String, Object> finalProcessedParams = processedParams;
                    queryParams = queryParams.stream()
                        .filter(qu -> finalProcessedParams.containsKey(qu.getName()))
                        .collect(Collectors.toList());
                    fieldRules = queryParams.stream().map(queryParam -> {
                        FieldRule fieldRule = new FieldRule();
                        fieldRule.setFieldName(queryParam.getName());
                        fieldRule.setFormType(queryParam.getFormType());
                        fieldRule.setRequired(queryParam.getRequired());
                        fieldRule.setTableName(queryParam.getTableName());
                        CreateIntegrationRequest.ExportConfig exportConfig = queryParam.getExportConfig();
                        if (Objects.nonNull(exportConfig)) {
                            CreateIntegrationRequest.Config config = exportConfig.getConfig();
                            if (Objects.nonNull(config)) {
                                fieldRule.setMultiSelect(config.getIsMultiSelect());
                                fieldRule.setFuzzyMatch(config.getIsFuzzyMatch());
                            }
                        }
                        return fieldRule;
                    }).collect(Collectors.toList());
                }
            }
            sql = SqlDynamicBuilder.buildSql(sql, fieldRules, processedParams);
            log.info("buildSql sql: {}", sql);
        }

        authService.checkSqlAuth(new AuthDTO().setSql(sql).setLoginName(AuthUtils.getLoginName()).setDatasource(datasource));

        // 处理排序条件
        if (params.getSort() != null && !params.getSort().trim().isEmpty()) {
            sql = addOrderByClause(sql, params.getSort());
            log.info("添加排序后的SQL: {}", sql);
        }

        // 检查SQL语句类型，只允许SELECT语句
        String trimmedSql = sql.trim().toLowerCase();
        if (!trimmedSql.startsWith("select")) {
            log.error("执行查询失败，只允许执行SELECT查询语句");
            throw new RuntimeException("安全限制：只允许执行SELECT查询语句");
        }

        // 检查SQL是否包含危险操作
        if (containsDangerousOperations(trimmedSql)) {
            log.error("执行查询失败，SQL包含危险操作");
            throw new RuntimeException("安全限制：SQL包含危险操作");
        }

        // 创建执行记录ID
        String executionId = UUID.randomUUID().toString().replace("-", "");

        // 构建数据源连接URL
        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);

        // 设置数据库连接属性
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(datasource.getUsername());
        dataSource.setPassword(datasource.getPassword());
        dataSource.setDriverClassName(getDriverClassName(datasource.getType()));

        // 创建JDBC模板
        JdbcTemplate template = new JdbcTemplate(dataSource);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 获取总记录数
        String countSql = convertToCountSql(sql);
        log.info("COUNT SQL: {}", countSql);
        Long total = template.queryForObject(countSql, Long.class);
        log.info("sql calculate count : {}", total);

        // 添加分页参数
        int page = params.getPage() != null ? params.getPage() : 1;
        int size = params.getSize() != null ? params.getSize() : 20;
        int offset = (page - 1) * size;
        sql = sql + " LIMIT "+ offset + StrPool.COMMA + size;
        log.info("add page sql: {}", sql);

        // 执行SQL查询
        List<Map<String, Object>> rows = template.queryForList(sql);

        // 处理日期类型
        rows = processDateInQueryResult(rows);

        // 处理加密数据
        rows = processEncryptedDataInQueryResult(rows, queryFieldDTOS);

        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;

        // 构建结果列信息
        List<String> columns = new ArrayList<>();
//      List<String> columnTypes = new ArrayList<>();
        List<QueryResultDTO.FieldInfo> fields = new ArrayList<>();
        if (!rows.isEmpty()) {
            Map<String, Object> firstRow = rows.get(0);
            columns.addAll(firstRow.keySet());
            // 构建列类型信息和字段信息
            for (String column : columns) {
                Object value = firstRow.get(column);
                String type = value != null ? value.getClass().getSimpleName() : "String";
//                columnTypes.add(type);

                // 构建字段信息
                QueryResultDTO.FieldInfo fieldInfo = QueryResultDTO.FieldInfo.builder()
                    .name(column)
                    .type(type)
                    .label(column) // 默认使用列名作为标签
                    .isEncrypted(encryptedColumns.contains(column)) // 设置是否是加密列
                    .build();
                fields.add(fieldInfo);
            }
        }

        // 创建执行历史记录
        String paramsJson = null;
        if (params.getParameters() != null) {
            try {
                paramsJson = new ObjectMapper().writeValueAsString(params.getParameters());
            } catch (JsonProcessingException ex) {
                log.error("参数序列化失败: {}", ex.getMessage());
            }
        }
        String queryId = null;
        if (null != query) {
            queryId = query.getId();
            String versionId = null == queryVersion ? null : queryVersion.getId();
            ExecutionHistory history = ExecutionHistory.builder()
                .id(executionId)
                .queryId(queryId)
                .versionId(versionId)
                .executedBy("admin") // 应从上下文获取当前用户
                .executedAt(new Date())
                .status("running")
                .duration(BigDecimal.valueOf(executionTime))
                .rowCount(rows.size())
                .parameters(paramsJson)
                .resultId(executionId)
                .build();

            // 保存执行历史
            executionHistoryMapper.insert(history);
            // 更新查询执行信息
            query.setExecutionCount(query.getExecutionCount() + 1);
            query.setLastExecutedAt(new Date());
            query.setResultCount(rows.size());
            query.setExecutionTime(BigDecimal.valueOf(executionTime));
            queryMapper.updateById(query);
        }
        // 构建返回结果
        return QueryResultDTO.builder()
            .id(executionId)
            .queryId(queryId)
            .status("running")
            .executionTime(Double.valueOf(executionTime))
            .rowCount(rows.size())
            .total(total)  // 添加总记录数
            .page(page)    // 添加当前页码
            .size(size)    // 添加每页大小
            .rows(rows)
            .fields(fields)
            .createdAt(new Date())
            .build();
    }

    private QueryResultDTO executeSql(Datasource datasource, Query query, QueryVersion queryVersion, String sql, ExecuteQueryParams params) {
        // 获取查询涉及的表和列信息
        Set<QueryFieldDTO> queryFieldDTOS = getEncryptedColumns(sql, datasource.getId());
        Set<String> encryptedColumns = queryFieldDTOS.stream().map(QueryFieldDTO::getName).collect(Collectors.toSet());

        // 移除SQL注释
        sql = MybatisSqlParser.parseSql(sql, params.getParameters());
        sql = removeComments(sql);
        log.info("移除注释后的SQL: {}", sql);

//        sql = MybatisSqlParser.parseSql(sql, params.getParameters());
//        log.info("处理完标签后的SQL: {}", sql);

        // 替换SQL参数，处理加密列的参数值
        if (params.getParameters() != null && !params.getParameters().isEmpty()) {
            Map<String, Object> processedParams = new HashMap<>(params.getParameters());
            // 处理加密列的参数值
            for (Map.Entry<String, Object> entry : params.getParameters().entrySet()) {
                String paramName = entry.getKey();
                if (encryptedColumns.contains(paramName)) {
                    Object value = entry.getValue();
                    if (value != null) {
                        processedParams.put(paramName, DoorgodSMUtils.encode(String.valueOf(value)));
                    }
                }
            }
            sql = replaceSQLParameters(sql, processedParams);
            log.info("替换参数后的SQL: {}", sql);
        }

        // check sql permission
        authService.checkSqlAuth(new AuthDTO().setSql(sql).setLoginName(AuthUtils.getLoginName()).setDatasource(datasource));

        // 处理排序条件
        if (params.getSort() != null && !params.getSort().trim().isEmpty()) {
            sql = addOrderByClause(sql, params.getSort());
            log.info("添加排序后的SQL: {}", sql);
        }

        // 检查SQL语句类型，只允许SELECT语句
        String trimmedSql = sql.trim().toLowerCase();
        if (!trimmedSql.startsWith("select")) {
            log.error("执行查询失败，只允许执行SELECT查询语句");
            throw new RuntimeException("安全限制：只允许执行SELECT查询语句");
        }

        // 检查SQL是否包含危险操作
        if (containsDangerousOperations(trimmedSql)) {
            log.error("执行查询失败，SQL包含危险操作");
            throw new RuntimeException("安全限制：SQL包含危险操作");
        }

        // 创建执行记录ID
        String executionId = UUID.randomUUID().toString().replace("-", "");

        // 构建数据源连接URL
        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);

        // 设置数据库连接属性
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(datasource.getUsername());
        dataSource.setPassword(datasource.getPassword());
        dataSource.setDriverClassName(getDriverClassName(datasource.getType()));

        // 创建JDBC模板
        JdbcTemplate template = new JdbcTemplate(dataSource);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行SQL查询
        List<Map<String, Object>> rows = template.queryForList(sql);

        // 处理日期类型
        rows = processDateInQueryResult(rows);

        // 处理加密数据
        rows = processEncryptedDataInQueryResult(rows, queryFieldDTOS);

        // 计算执行时间
        long executionTime = System.currentTimeMillis() - startTime;

        // 构建结果列信息
        List<String> columns = new ArrayList<>();
//        List<String> columnTypes = new ArrayList<>();
        List<QueryResultDTO.FieldInfo> fields = new ArrayList<>();
        if (!rows.isEmpty()) {
            Map<String, Object> firstRow = rows.get(0);
            columns.addAll(firstRow.keySet());
            // 构建列类型信息和字段信息
            for (String column : columns) {
                Object value = firstRow.get(column);
                String type = value != null ? value.getClass().getSimpleName() : "String";
//                columnTypes.add(type);

                // 构建字段信息
                QueryResultDTO.FieldInfo fieldInfo = QueryResultDTO.FieldInfo.builder()
                    .name(column)
                    .type(type)
                    .label(column) // 默认使用列名作为标签
                    .isEncrypted(encryptedColumns.contains(column)) // 设置是否是加密列
                    .build();
                fields.add(fieldInfo);
            }
        }

        // 创建执行历史记录
        String paramsJson = null;
        if (params.getParameters() != null) {
            try {
                paramsJson = new ObjectMapper().writeValueAsString(params.getParameters());
            } catch (JsonProcessingException ex) {
                log.error("参数序列化失败: {}", ex.getMessage());
            }
        }
        String queryId = null;
        if (null != query) {
            queryId = query.getId();
            String versionId = null == queryVersion ? null : queryVersion.getId();
            ExecutionHistory history = ExecutionHistory.builder()
                .id(executionId)
                .queryId(queryId)
                .versionId(versionId)
                .executedBy("admin") // 应从上下文获取当前用户
                .executedAt(new Date())
                .status("running")
                .duration(BigDecimal.valueOf(executionTime))
                .rowCount(rows.size())
                .parameters(paramsJson)
                .resultId(executionId)
                .build();

            // 保存执行历史
            executionHistoryMapper.insert(history);
            // 更新查询执行信息
            query.setExecutionCount(query.getExecutionCount() + 1);
            query.setLastExecutedAt(new Date());
            query.setResultCount(rows.size());
            query.setExecutionTime(BigDecimal.valueOf(executionTime));
            queryMapper.updateById(query);
        }
        // 构建返回结果
        return QueryResultDTO.builder()
            .id(executionId)
            .queryId(queryId)
            .status("running")
            .executionTime(Double.valueOf(executionTime))
            .rowCount(rows.size())
            .rows(rows)
            .fields(fields)
            .createdAt(new Date())
            .build();
    }

    /**
     * 向SQL添加排序子句
     * @param sql 原始SQL
     * @param sort 排序字符串，格式：字段名[,排序方向]，如：id,desc 或 id 或 id,asc
     * @return 添加排序后的SQL
     */
    private String addOrderByClause(String sql, String sort) {
        if (sort == null || sort.trim().isEmpty()) {
            return sql;
        }

        // 解析排序字符串
        String[] parts = sort.trim().split(",");
        String sortBy = parts[0].trim();
        String sortDir = parts.length > 1 ? parts[1].trim().toLowerCase() : "asc";

        // 检查SQL是否已经包含ORDER BY子句
        String lowerSql = sql.toLowerCase();
        if (lowerSql.contains("order by")) {
            // 如果已经有ORDER BY，则在末尾添加新的排序条件
            return sql + ", " + escapeSqlIdentifier(sortBy) + " " + (sortDir.equals("desc") ? "DESC" : "ASC");
        } else {
            // 如果没有ORDER BY，则添加新的ORDER BY子句
            return sql + " ORDER BY " + escapeSqlIdentifier(sortBy) + " " + (sortDir.equals("desc") ? "DESC" : "ASC");
        }
    }

    /**
     * 转义SQL标识符（列名、表名等）
     * @param identifier SQL标识符
     * @return 转义后的标识符
     */
    private String escapeSqlIdentifier(String identifier) {
        if (identifier == null) {
            return null;
        }
        // 移除任何现有的引号
        identifier = identifier.replace("`", "").replace("\"", "").replace("[", "").replace("]", "");
        // 使用反引号包裹标识符
        return "`" + identifier + "`";
    }

    /**
     * 获取SQL中涉及的加密列
     *
     * @param sql SQL语句
     * @param dataSourceId 数据源ID
     * @return 加密列集合
     */
    private Set<QueryFieldDTO> getEncryptedColumns(String sql, String dataSourceId) {
        Set<QueryFieldDTO> encryptedColumns = new HashSet<>();
        try {
            // 使用SQL解析器分析SQL，获取涉及的表和列
            List<QueryFieldDTO> fields = sqlAnalyzer.extractFields(sql, dataSourceService.getById(dataSourceId));

            // 遍历所有字段，检查是否是加密列
            for (QueryFieldDTO field : fields) {
                String tableName = field.getTableName();
                String columnName = field.getName();

                // 查询表信息
                LambdaQueryWrapper<Table> tableWrapper = new LambdaQueryWrapper<>();
                tableWrapper.eq(Table::getName, tableName)
                    .eq(Table::getDatasourceId, dataSourceId);
                Table table = tableMapper.selectOne(tableWrapper);

                if (table != null) {
                    // 查询列信息
                    LambdaQueryWrapper<Column> columnWrapper = new LambdaQueryWrapper<>();
                    columnWrapper.eq(Column::getTableId, table.getId())
                               .eq(Column::getName, columnName);
                    Column column = columnMapper.selectOne(columnWrapper);

                    if (column != null && Boolean.TRUE.equals(column.getIsEncrypted())) {
                        if (StrUtil.isNotEmpty(column.getEncryConfig())) {
                            field.setEntryConfig(objectMapper.readValue(column.getEncryConfig(), Object.class));
                        }
                        encryptedColumns.add(field);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取加密列信息失败: {}", e.getMessage(), e);
        }
        return encryptedColumns;
    }

    private String processEntryValue(String value, Object entryConfig) {
        if (Objects.isNull(entryConfig) || StrUtil.isBlank(value)) {
            return value;
        }
        try {
            String entry = objectMapper.writeValueAsString(entryConfig);
            JsonNode jsonNode = objectMapper.readTree(entry);
            if (jsonNode.isEmpty()) {
                return value;
            }
            String decodeValue = value;
            if (jsonNode.has(Constant.AES)) {
                JsonNode aseNode = jsonNode.get(Constant.AES);
                if (!aseNode.isEmpty()) {
                    String key = aseNode.get(Constant.AES_KEY).asText();
                    // aes 解密
                    if (StrUtil.isNotBlank(key)) {
                        decodeValue = SecureUtil.aes(key.getBytes()).decryptStr(value);
                    }
                }
            } else if (jsonNode.has(Constant.GM)) {
                JsonNode gmNode = jsonNode.get(Constant.GM);
                if (gmNode.isArray()) {
                    // 新逻辑：选择性解密
                    Set<String> gmFields = new HashSet<>();
                    for (JsonNode field : gmNode) {
                        gmFields.add(field.asText());
                    }

                    if (isJsonFormat(value)) {
                        decodeValue = processJsonValueForGM(value, gmFields);
                    } else if (isMapFormat(value)) {
                        decodeValue = processMapValueForGM(value, gmFields);
                    } else {
                        // 普通字符串，保持原有逻辑
                        decodeValue = DoorgodSMUtils.decodeByAppId(value);
                    }
                } else {
                    // 兼容原有配置格式，直接解密整个value
                    decodeValue = DoorgodSMUtils.decodeByAppId(value);
                }
            }
            return decodeValue;
        } catch (Exception e) {
            log.error("processEntryValue error: ", e);
        }
        return value;
    }

    /**
     * 处理查询结果中的加密数据
     */
    private List<Map<String, Object>> processEncryptedDataInQueryResult(List<Map<String, Object>> rows, Set<QueryFieldDTO> encryptedColumns) {
        if (rows == null || rows.isEmpty() || CollUtil.isEmpty(encryptedColumns)) {
            return rows;
        }

        rows.forEach(line ->
            encryptedColumns.forEach(encryptedColumn -> {
            if (line.containsKey(encryptedColumn.getName()) || line.containsKey(encryptedColumn.getLabel())) {
                Object fieldValue = line.get(encryptedColumn.getName());
                if (Objects.isNull(fieldValue)) {
                    Object alisaValue = line.get(encryptedColumn.getLabel());
                    if (Objects.nonNull(alisaValue)) {
                        String value = processEntryValue(String.valueOf(alisaValue), encryptedColumn.getEntryConfig());
                        line.put(encryptedColumn.getLabel(), value);
                    }
                } else {
                    String value = processEntryValue(String.valueOf(fieldValue), encryptedColumn.getEntryConfig());
                    line.put(encryptedColumn.getName(), value);
                }
            }
        }));
        return rows;
//        return rows.stream().map(row -> {
//            Map<String, Object> processedRow = new HashMap<>(row);
//            for (String columnName : encryptedColumns) {
//                Object value = processedRow.get(columnName);
//                if (value != null) {
//                    try {
//                        String decryptedValue = DoorgodSMUtils.decode(String.valueOf(value));
//                        processedRow.put(columnName, decryptedValue);
//                    } catch (Exception e) {
//                        log.warn("解密列[{}]的值[{}]失败: {}", columnName, value, e.getMessage());
//                    }
//                }
//            }
//            return processedRow;
//        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableQuery(String queryId) {
        log.info("启用查询: {}", queryId);

        // 检查查询是否存在
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("启用查询失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 如果已经是启用状态，直接返回成功
        if ("ENABLED".equals(query.getServiceStatus())) {
            log.info("查询[{}]已经是启用状态", queryId);
            return true;
        }

        String currentUser = AuthUtils.getUsername(); // 这里应该从上下文获取当前用户
        Date now = new Date();

        // 更新查询服务状态为启用
        query.setServiceStatus("ENABLED");
        query.setUpdatedBy(currentUser);
        query.setUpdatedAt(now);
        int result = queryMapper.updateById(query);

        log.info("启用查询[{}]结果: {}", queryId, result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableQuery(String queryId) {
        log.info("禁用查询: {}", queryId);

        // 检查查询是否存在
        Query query = queryMapper.selectById(queryId);
        if (query == null) {
            log.error("禁用查询失败，查询不存在: {}", queryId);
            throw new RuntimeException("查询不存在: " + queryId);
        }

        // 如果已经是禁用状态，直接返回成功
        if ("DISABLED".equals(query.getServiceStatus())) {
            log.info("查询[{}]已经是禁用状态", queryId);
            return true;
        }

        String currentUser = AuthUtils.getUsername();
        Date now = new Date();

        // 更新查询服务状态为禁用
        query.setServiceStatus("DISABLED");
        query.setUpdatedBy(currentUser);
        query.setUpdatedAt(now);
        int result = queryMapper.updateById(query);

        log.info("禁用查询[{}]结果: {}", queryId, result > 0 ? "成功" : "失败");
        return result > 0;
    }

    @Override
    public QueryResultDTO executeSQL(String dataSourceId, ExecuteQueryParams params) {
        log.info("开始直接执行SQL，数据源ID: {}, SQL: {}", dataSourceId, params.getSql());
        // 获取数据源信息
        Datasource datasource = datasourceMapper.selectById(dataSourceId);
        if (datasource == null) {
            log.error("执行SQL失败，数据源不存在: {}", dataSourceId);
            throw new RuntimeException("数据源不存在: " + dataSourceId);
        }
        if (datasource.getIsAuthRequired()) {
            boolean flag = authService.checkAuth(datasource.getDatabaseName(), AuthUtils.getUsername(), authService.getAuthToken());
            Assert.isTrue(flag, () -> new RuntimeException("您没权限操作数据源: " + datasource.getDatabaseName()));
        }

        // 校验SQL是否为空
        String sql = params.getSql();
        if (sql == null || sql.trim().isEmpty()) {
            log.error("执行SQL失败，SQL语句为空");
            throw new RuntimeException("SQL语句不能为空");
        }

        return executeSql(datasource, null, null, sql, params);
    }

    /**
     * 检查SQL语句是否包含危险操作
     *
     * @param sql SQL语句
     * @return 是否包含危险操作
     */
    private boolean containsDangerousOperations(String sql) {
        // 转为小写并去除多余空格
        String normalizedSql = sql.toLowerCase().replaceAll("\\s+", " ");

        // 检查是否包含数据修改操作
        if (normalizedSql.contains(" delete ") ||
            normalizedSql.contains(" update ") ||
            normalizedSql.contains(" insert ") ||
            normalizedSql.contains(" alter ") ||
            normalizedSql.contains(" drop ") ||
            normalizedSql.contains(" truncate ") ||
            normalizedSql.contains(" create ") ||
            normalizedSql.contains(" rename ") ||
            normalizedSql.contains(" replace ")) {
            return true;
        }

        // 检查是否尝试多语句执行
        if (normalizedSql.contains(";")) {
            return true;
        }

        return false;
    }

    /**
     * 将Query实体转换为DTO
     */
    private QueryDTO convertToDTO(Query query) {
        // 构建创建者信息
        QueryDTO.UserInfo createdByInfo = QueryDTO.UserInfo.builder()
            .id(query.getCreatedBy())
            .name(query.getCreatedBy()) // 这里应该从用户服务获取用户名
            .build();

        // 构建更新者信息
        QueryDTO.UserInfo updatedByInfo = QueryDTO.UserInfo.builder()
            .id(query.getUpdatedBy())
            .name(query.getUpdatedBy()) // 这里应该从用户服务获取用户名
            .build();

        // 查询当前活跃版本：优先获取已发布版本，如果没有则获取最新版本
        QueryVersion version = queryVersionMapper.selectOne(
            new LambdaQueryWrapper<QueryVersion>()
                .eq(QueryVersion::getQueryId, query.getId())
                .eq(QueryVersion::getStatus, "PUBLISHED")
                .orderByDesc(QueryVersion::getVersionNumber)
                .last("LIMIT 1")
        );

        // 如果没有发布态的版本，则获取最新版本
        if (version == null) {
            version = queryVersionMapper.selectOne(
                new LambdaQueryWrapper<QueryVersion>()
                    .eq(QueryVersion::getQueryId, query.getId())
                    .orderByDesc(QueryVersion::getVersionNumber)
                    .last("LIMIT 1")
            );
        }

        // 获取数据源名称
        String dataSourceName = null;
        if (query.getDataSourceId() != null) {
            Datasource datasource = datasourceMapper.selectById(query.getDataSourceId());
            if (datasource != null) {
                dataSourceName = datasource.getName();
            }
        }

        // 构建查询DTO
        QueryDTO dto = QueryDTO.builder()
            .id(query.getId())
            .name(query.getName())
            .description(query.getDescription())
            .folderId(query.getFolderId())
            .status(query.getStatus())
            .serviceStatus(query.getServiceStatus())
            .dataSourceId(query.getDataSourceId())
            .dataSourceName(dataSourceName)
            .queryType(query.getQueryType())
            .queryText(version != null ? version.getSqlContent() : null)
            .resultCount(query.getResultCount())
            .executionTime(query.getExecutionTime())
            .executionCount(query.getExecutionCount())
            .lastExecutedAt(query.getLastExecutedAt())
            .isActive(true)
            .isFavorite(checkIsFavorite(query.getId()))
            .createdBy(createdByInfo)
            .createdAt(query.getCreatedAt())
            .updatedBy(updatedByInfo)
            .updatedAt(query.getUpdatedAt())
            .build();

        // 设置当前版本信息
        if (version != null) {
            QueryVersionDTO versionDTO = QueryVersionDTO.builder()
                .id(version.getId())
                .queryId(version.getQueryId())
                .versionNumber(version.getVersionNumber())
                .name(version.getName())
                .description(version.getDescription())
                .sql(version.getSqlContent())
                .dataSourceId(version.getDataSourceId())
                .status(version.getStatus())
                .isLatest(version.getIsLatest())
                .createdAt(version.getCreatedAt())
                .updatedAt(version.getUpdatedAt())
                .build();

            // 设置数据源信息
            setDataSourceInfo(versionDTO);

            dto.setCurrentVersion(versionDTO);

            // 查询参数信息
            List<QueryParameterDTO> parameters = getVersionParameters(version.getId());
            if (parameters != null && !parameters.isEmpty()) {
                dto.setParameters(parameters);
            }
        }

        return dto;
    }

    /**
     * 检查查询是否被当前用户收藏
     */
    private boolean checkIsFavorite(String queryId) {
        try {
            // 这里应该从上下文获取当前用户ID
            String currentUserId = AuthUtils.getUsername();

            LambdaQueryWrapper<QueryFavorite> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QueryFavorite::getQueryId, queryId)
                .eq(QueryFavorite::getUserId, currentUserId);

            return queryFavoriteMapper.selectCount(wrapper) > 0;
        } catch (Exception e) {
            log.error("检查查询是否收藏失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据查询版本状态自动确定查询状态
     * 查询的状态取决于其关联的查询版本状态，优先级为：
     * 1.已发布(PUBLISHED)>草稿(DRAFT)>已废弃(DEPRECATED)
     * 2.如果存在已发布的版本，查询的状态为已发布
     * 3.如果不存在已发布的版本，但存在草稿态版本，则查询的状态为草稿
     * 4.如果既没有已发布的版本也没有草稿态版本，则查询的状态为已废弃
     *
     * @param queryId 查询ID
     * @return 确定的查询状态
     */
    private String determineQueryStatus(String queryId) {
        log.info("确定查询[{}]状态", queryId);

        // 查询所有版本
        LambdaQueryWrapper<QueryVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QueryVersion::getQueryId, queryId);
        List<QueryVersion> versions = queryVersionMapper.selectList(wrapper);

        if (versions.isEmpty()) {
            log.info("查询[{}]没有版本，默认为草稿状态", queryId);
            return "DRAFT"; // 默认为草稿状态
        }

        // 检查是否有已发布版本
        boolean hasPublished = versions.stream()
            .anyMatch(v -> "PUBLISHED".equals(v.getStatus()));
        if (hasPublished) {
            log.info("查询[{}]存在已发布版本，状态为已发布", queryId);
            return "PUBLISHED";
        }

        // 检查是否有草稿版本
        boolean hasDraft = versions.stream()
            .anyMatch(v -> "DRAFT".equals(v.getStatus()));
        if (hasDraft) {
            log.info("查询[{}]存在草稿版本，状态为草稿", queryId);
            return "DRAFT";
        }

        // 既没有已发布也没有草稿，则为已废弃
        log.info("查询[{}]既没有已发布版本也没有草稿版本，状态为已废弃", queryId);
        return "DEPRECATED";
    }

    /**
     * 根据数据源类型获取驱动类名
     */
    private String getDriverClassName(String type) {
        return JdbcUrlBuilder.getDriverClassName(type);
    }

    /**
     * 处理查询结果中的日期类型，确保所有日期都格式化为yyyy-MM-dd HH:mm:ss格式的字符串
     *
     * @param rows 查询结果数据行
     * @return 处理后的结果数据行
     */
    private List<Map<String, Object>> processDateInQueryResult(List<Map<String, Object>> rows) {
        if (rows == null || rows.isEmpty()) {
            return rows;
        }

        for (Map<String, Object> row : rows) {
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                if (entry.getValue() instanceof Date) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format((Date) entry.getValue()));
                } else if (entry.getValue() instanceof java.sql.Date) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(new Date(((java.sql.Date) entry.getValue()).getTime())));
                } else if (entry.getValue() instanceof java.sql.Timestamp) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(new Date(((java.sql.Timestamp) entry.getValue()).getTime())));
                } else if (entry.getValue() instanceof LocalDateTime) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(Date.from(((LocalDateTime) entry.getValue()).atZone(ZoneId.systemDefault()).toInstant())));
                }
            }
        }

        return rows;
    }

    /**
     * 替换SQL中的参数
     * 支持MyBatis风格的参数：#{paramName}
     *
     * @param sql        原始SQL
     * @param parameters 参数Map
     * @return 替换参数后的SQL
     */
    private String replaceSQLParameters(String sql, Map<String, Object> parameters) {
        if (sql == null || parameters == null || parameters.isEmpty()) {
            return sql;
        }

        // 先处理SQL中的可选参数表达式
        // 用于存储处理过的区域，避免重复处理
        List<int[]> processedRegions = new ArrayList<>();
        StringBuilder result = new StringBuilder(sql);

        // 1. 处理(:param IS NULL OR col IN (:param)) 格式的可选参数
        processOptionalInParams(result, parameters, processedRegions);

        // 2. 处理常规参数替换 #{}
        processNormalParams(result, parameters, processedRegions, "#\\{(\\w+)\\}");

        // 3. 处理常规参数替换 :param
        processNormalParams(result, parameters, processedRegions, ":(\\w+)");

        return result.toString();
    }

    /**
     * 处理可选参数的IN子句，如 (:param IS NULL OR col IN (:param))
     */
    private void processOptionalInParams(StringBuilder sql, Map<String, Object> parameters, List<int[]> processedRegions) {
        // 匹配模式: (:param IS NULL OR col IN (:param))
        Pattern optionalInPattern = Pattern.compile(
            "\\(\\s*:(\\w+)\\s+IS\\s+NULL\\s+OR\\s+\\w+\\s+IN\\s*\\(\\s*:(\\1)\\s*\\)\\s*\\)",
            Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = optionalInPattern.matcher(sql);
        int offset = 0;

        while (matcher.find()) {
            int start = matcher.start() + offset;
            int end = matcher.end() + offset;
            String paramName = matcher.group(1);

            // 检查这个区域是否已经处理过
            if (isRegionProcessed(start, end, processedRegions)) {
                continue;
            }

            Object paramValue = parameters.get(paramName);
            String replacement;

            if (paramValue == null ||
               (paramValue instanceof String && ((String)paramValue).isEmpty()) ||
               (paramValue instanceof Collection && ((Collection<?>)paramValue).isEmpty()) ||
               (paramValue.getClass().isArray() && Array.getLength(paramValue) == 0)) {
                // 参数为空，使用1=1
                replacement = "(1=1)";
            } else {
                // 处理非空参数 - 直接生成IN子句
                String inValues = processInClauseParameter(paramValue);
                // 提取列名
                Pattern colPattern = Pattern.compile("OR\\s+(\\w+)\\s+IN", Pattern.CASE_INSENSITIVE);
                Matcher colMatcher = colPattern.matcher(matcher.group(0));
                String column = colMatcher.find() ? colMatcher.group(1) : "column";

                replacement = "(" + column + " IN (" + inValues + "))";
            }

            sql.replace(start, end, replacement);
            offset += replacement.length() - (end - start);

            // 标记为已处理
            processedRegions.add(new int[]{start, start + replacement.length()});
        }
    }

    /**
     * 处理常规参数替换（#{param}或:param格式）
     */
    private void processNormalParams(StringBuilder sql, Map<String, Object> parameters,
                                    List<int[]> processedRegions, String patternStr) {
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(sql);

        List<ParameterInfo> paramInfos = new ArrayList<>();

        // 找出所有参数
        while (matcher.find()) {
            String paramName = matcher.group(1);
            int start = matcher.start();
            int end = matcher.end();

            // 检查这个区域是否已经处理过
            if (isRegionProcessed(start, end, processedRegions)) {
                continue;
            }

            boolean isQuoted = isParameterInQuotes(sql.toString(), start);
            boolean isInInClause = isParameterInInClause(sql.toString(), start);

            paramInfos.add(new ParameterInfo(paramName, start, end, isQuoted, isInInClause));
        }

        // 按照位置从后向前排序
        paramInfos.sort((a, b) -> Integer.compare(b.start, a.start));

        // 处理所有参数
        for (ParameterInfo info : paramInfos) {
            Object paramValue = parameters.get(info.name);

            if (isRegionProcessed(info.start, info.end, processedRegions)) {
                continue;
            }

            String replacement;
            if (paramValue == null) {
                replacement = "null";
            } else if (info.isInInClause) {
                replacement = processInClauseParameter(paramValue);
            } else if ((paramValue instanceof Number || paramValue instanceof Boolean) && !info.isQuoted) {
                replacement = String.valueOf(paramValue);
            } else {
                String valueStr;
                if (paramValue instanceof Date) {
                    valueStr = SIMPLE_DATE_FORMAT.format((Date) paramValue);
                } else {
                    valueStr = String.valueOf(paramValue);
                }

                // 转义单引号
                valueStr = escapeSql(valueStr);

                if (info.isQuoted) {
                    replacement = valueStr;
                } else {
                    replacement = "'" + valueStr + "'";
                }
            }

            sql.replace(info.start, info.end, replacement);

            // 更新已处理区域中其它参数的位置
            int delta = replacement.length() - (info.end - info.start);
            if (delta != 0) {
                for (ParameterInfo other : paramInfos) {
                    if (other.start > info.end) {
                        other.start += delta;
                        other.end += delta;
                    }
                }
            }

            // 标记当前区域为已处理
            processedRegions.add(new int[]{info.start, info.start + replacement.length()});
        }
    }

    /**
     * 检查区域是否已经处理过
     */
    private boolean isRegionProcessed(int start, int end, List<int[]> processedRegions) {
        for (int[] region : processedRegions) {
            // 如果当前位置在已处理区域内或与之重叠
            if ((start >= region[0] && start < region[1]) ||
                (end > region[0] && end <= region[1]) ||
                (start <= region[0] && end >= region[1])) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理IN子句中的参数
     * 支持多种参数类型：Collection、数组、逗号分隔的字符串
     */
    private String processInClauseParameter(Object paramValue) {
        if (paramValue == null) {
            return "null";
        }

        // 如果是集合类型
        if (paramValue instanceof Collection) {
            return processCollectionForInClause((Collection<?>) paramValue);
        }

        // 如果是数组类型
        if (paramValue.getClass().isArray()) {
            List<Object> list = new ArrayList<>();
            int length = Array.getLength(paramValue);
            for (int i = 0; i < length; i++) {
                list.add(Array.get(paramValue, i));
            }
            return processCollectionForInClause(list);
        }

        // 如果是逗号分隔的字符串
        String valueStr = String.valueOf(paramValue);
        if (valueStr.contains(",")) {
            String[] values = valueStr.split(",");
            List<String> list = new ArrayList<>();
            for (String val : values) {
                val = val.trim();
                if (!val.isEmpty()) {
                    list.add(val);
                }
            }
            return processCollectionForInClause(list);
        }

        // 单值情况，添加引号（除非是数字）
        try {
            Double.parseDouble(valueStr);
            return valueStr;  // 数字不加引号
        } catch (NumberFormatException e) {
            return "'" + escapeSql(valueStr) + "'";
        }
    }

    /**
     * 处理集合类型为IN子句
     */
    private String processCollectionForInClause(Collection<?> values) {
        if (values == null || values.isEmpty()) {
            return "null";
        }

        List<String> quotedValues = new ArrayList<>();
        for (Object value : values) {
            if (value == null) {
                continue;
            }

            String valueStr = String.valueOf(value).trim();
            if (!valueStr.isEmpty()) {
                // 尝试识别数字
                try {
                    Double.parseDouble(valueStr);
                    quotedValues.add(valueStr);  // 数字不加引号
                } catch (NumberFormatException e) {
                    // 非数字类型，添加引号并转义
                    quotedValues.add("'" + escapeSql(valueStr) + "'");
                }
            }
        }

        if (quotedValues.isEmpty()) {
            return "null";
        }

        return String.join(", ", quotedValues);
    }

    /**
     * 检查参数是否在IN子句中
     */
    private boolean isParameterInInClause(String sql, int position) {
        // 找到参数前的上下文
        int searchStart = Math.max(0, position - 100);  // 向前搜索更多上下文
        String precedingText = sql.substring(searchStart, position).toLowerCase();

        // 检查是否在IN子句中
        boolean inInClause = false;

        // 检查IN (或 IN(格式
        int inIndex = precedingText.lastIndexOf("in (");
        if (inIndex < 0) {
            inIndex = precedingText.lastIndexOf("in(");
        }

        if (inIndex >= 0) {
            // 提取in与参数之间的文本进行分析
            String betweenInAndParam = precedingText.substring(inIndex + 3);

            // 排除其他SQL条件关键字
            boolean hasOtherCondition = betweenInAndParam.contains(" and ") ||
                                       betweenInAndParam.contains(" or ") ||
                                       betweenInAndParam.contains(" where ");

            // 排除有其他闭合的括号情况
            int openParens = 0;
            int closeParens = 0;
            for (char c : betweenInAndParam.toCharArray()) {
                if (c == '(') openParens++;
                else if (c == ')') closeParens++;
            }

            // 确保开括号多于闭括号，说明我们在一个未闭合的IN子句中
            inInClause = !hasOtherCondition && (openParens > closeParens);
        }

        return inInClause;
    }

    /**
     * 检查参数是否在引号中
     */
    private boolean isParameterInQuotes(String sql, int position) {
        // 统计从开始到当前位置的引号数量
        int quoteCount = 0;
        boolean escaped = false;

        for (int i = 0; i < position; i++) {
            char c = sql.charAt(i);

            if (c == '\'') {
                // 处理转义的引号 (''), MySQL中单引号用另一个单引号转义
                if (i + 1 < sql.length() && sql.charAt(i + 1) == '\'') {
                    i++; // 跳过转义的引号
                    continue;
                }
                quoteCount++;
            }
        }

        // 如果引号数为奇数，则参数在引号内
        return quoteCount % 2 == 1;
    }

    /**
     * 参数信息类
     */
    private static class ParameterInfo {
        String name;
        int start;
        int end;
        boolean isQuoted;
        boolean isInInClause;
        boolean isOptionalParam;

        ParameterInfo(String name, int start, int end, boolean isQuoted) {
            this(name, start, end, isQuoted, false, false);
        }

        ParameterInfo(String name, int start, int end, boolean isQuoted, boolean isInInClause) {
            this(name, start, end, isQuoted, isInInClause, false);
        }

        ParameterInfo(String name, int start, int end, boolean isQuoted, boolean isInInClause, boolean isOptionalParam) {
            this.name = name;
            this.start = start;
            this.end = end;
            this.isQuoted = isQuoted;
            this.isInInClause = isInInClause;
            this.isOptionalParam = isOptionalParam;
        }
    }

    /**
     * 对SQL特殊字符进行转义，防止SQL注入
     *
     * @param input 输入字符串
     * @return 转义后的字符串
     */
    private String escapeSql(String input) {
        if (input == null) {
            return null;
        }
        // 基本的SQL转义：替换单引号
        return input.replace("'", "''");
    }

    /**
     * 从SQL中提取参数并保存到数据库
     *
     * @param queryId 查询ID
     * @param versionId 版本ID
     * @param sql SQL语句
     * @param currentUser 当前用户
     * @param now 当前时间
     */
    private void saveQueryParameters(String queryId, String versionId, String sql, String currentUser, Date now) {
        try {
            // 先删除该版本的所有现有参数，避免版本更新时参数不一致
            LambdaQueryWrapper<QueryParameter> existingParamWrapper = new LambdaQueryWrapper<>();
            existingParamWrapper.eq(QueryParameter::getVersionId, versionId);
            int deleted = queryParameterMapper.delete(existingParamWrapper);
            if (deleted > 0) {
                log.info("已删除查询[{}]版本[{}]的现有参数{}个", queryId, versionId, deleted);
            }

            // 使用SQL分析器提取参数
            List<QueryParameterDTO> parameterDTOs = sqlAnalyzer.extractParameters(sql);

            // 根据queryId 和 versionId 查询数据源ID
            String dataSourceId = queryMapper.selectById(queryId).getDataSourceId();
            // 获取数据源信息
            Datasource dataSource = datasourceMapper.selectById(dataSourceId);

            List<QueryFieldDTO> fields = sqlAnalyzer.extractFields(sql, dataSource);

            // 根据 fields 的 label 填充 parameters 的 label
            for (QueryParameterDTO parameter : parameterDTOs) {
                for (QueryFieldDTO field : fields) {
                    if (field.getName().equalsIgnoreCase(parameter.getName())) {
                        parameter.setLabel(field.getLabel());
                    }
                }
            }

            if (parameterDTOs != null && !parameterDTOs.isEmpty()) {
                log.info("从SQL中提取到{}个参数，保存到查询[{}]版本[{}]", parameterDTOs.size(), queryId, versionId);

                for (QueryParameterDTO paramDTO : parameterDTOs) {
                    QueryParameter parameter = QueryParameter.builder()
                        .id(UUID.randomUUID().toString().replace("-", ""))
                        .queryId(queryId)
                        .versionId(versionId)
                        .name(paramDTO.getName())
                        .type(paramDTO.getType())
                        .label(paramDTO.getLabel())
                        .defaultValue(paramDTO.getDefaultValue())
                        .required(paramDTO.getRequired())
                        .options(paramDTO.getOptions() != null ? new ObjectMapper().writeValueAsString(paramDTO.getOptions()) : null)
                        .createdBy(currentUser)
                        .createdAt(LocalDateTime.ofInstant(now.toInstant(), ZoneId.systemDefault()))
                        .updatedBy(currentUser)
                        .updatedAt(LocalDateTime.ofInstant(now.toInstant(), ZoneId.systemDefault()))
                        .build();

                    queryParameterMapper.insert(parameter);
                }

                log.info("参数保存完成");
            } else {
                log.info("SQL中未提取到参数，无需保存");
            }
        } catch (Exception e) {
            log.error("保存查询参数失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存查询参数失败: " + e.getMessage());
        }
    }

    /**
     * 获取查询版本相关参数
     *
     * @param versionId 版本ID
     * @return 参数DTO列表
     */
    private List<QueryParameterDTO> getVersionParameters(String versionId) {
        // 查询参数
        LambdaQueryWrapper<QueryParameter> paramWrapper = new LambdaQueryWrapper<>();
        paramWrapper.eq(QueryParameter::getVersionId, versionId);
        List<QueryParameter> parameters = queryParameterMapper.selectList(paramWrapper);

        if (parameters == null || parameters.isEmpty()) {
            return null;
        }

        // 转换为DTO
        List<QueryParameterDTO> parameterDTOs = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();

        for (QueryParameter param : parameters) {
            // 构建基本参数信息
            QueryParameterDTO.QueryParameterDTOBuilder builder = QueryParameterDTO.builder()
                .id(param.getId())
                .name(param.getName())
                .type(param.getType())
                .label(param.getLabel())
                .defaultValue(param.getDefaultValue())
                .required(param.getRequired());

            // 解析选项JSON
            if (param.getOptions() != null && !param.getOptions().isEmpty()) {
                try {
                    builder.options(objectMapper.readValue(param.getOptions(), List.class));
                } catch (Exception e) {
                    log.warn("解析参数选项失败: {}, 参数ID: {}", e.getMessage(), param.getId());
                }
            }

            parameterDTOs.add(builder.build());
        }

        return parameterDTOs;
    }

    /**
     * 处理查询参数，合并默认值和用户提供的值
     *
     * @param versionId 版本ID
     * @param userParams 用户提供的参数
     * @return 合并后的参数Map
     */
    private Map<String, Object> processQueryParameters(String versionId, Map<String, Object> userParams) {
        // 创建结果Map
        Map<String, Object> resultParams = new HashMap<>();

        // 查询保存的参数
        List<QueryParameterDTO> savedParams = getVersionParameters(versionId);

        if (savedParams != null && !savedParams.isEmpty()) {
            // 添加所有保存的默认值
            for (QueryParameterDTO param : savedParams) {
                if (param.getDefaultValue() != null && !param.getDefaultValue().isEmpty()) {
                    // 根据参数类型转换默认值
                    Object defaultValue = convertParameterValue(param.getDefaultValue(), param.getType());
                    resultParams.put(param.getName(), defaultValue);
                }
            }
        }

        // 用户提供的参数覆盖默认值
        if (userParams != null && !userParams.isEmpty()) {
            resultParams.putAll(userParams);
        }

        return resultParams;
    }

    /**
     * 根据参数类型转换参数值
     *
     * @param value 字符串值
     * @param type 参数类型
     * @return 转换后的值对象
     */
    private Object convertParameterValue(String value, String type) {
        if (value == null) {
            return null;
        }

        switch (type.toLowerCase()) {
            case "number":
                try {
                    // 尝试转换为整数
                    if (value.indexOf('.') < 0) {
                        return Long.parseLong(value);
                    }
                    // 有小数点则转换为浮点数
                    return Double.parseDouble(value);
                } catch (NumberFormatException e) {
                    log.warn("数字参数转换失败: {}", value);
                    return value;
                }
            case "boolean":
                return Boolean.parseBoolean(value) || "1".equals(value) || "yes".equalsIgnoreCase(value);
            case "date":
                try {
                    // 尝试解析日期
                    return new SimpleDateFormat(DATE_FORMAT).parse(value);
                } catch (Exception e) {
                    log.warn("日期参数转换失败: {}", value);
                    return value;
                }
            case "string":
            default:
                return value;
        }
    }

    /**
     * 更新查询参数的状态
     *
     * @param versionId 版本ID
     * @param status 新状态
     * @param currentUser 当前用户
     * @param now 当前时间
     */
    private void updateQueryParametersStatus(String versionId, String status, String currentUser, Date now) {
        try {
            // 查询该版本下的所有参数
            LambdaQueryWrapper<QueryParameter> paramWrapper = new LambdaQueryWrapper<>();
            paramWrapper.eq(QueryParameter::getVersionId, versionId);
            List<QueryParameter> parameters = queryParameterMapper.selectList(paramWrapper);

            if (parameters != null && !parameters.isEmpty()) {
                log.info("更新查询版本[{}]参数状态为{}, 参数数量: {}", versionId, status, parameters.size());

                // 批量更新方案待实现
                // 简单处理，逐个更新
                for (QueryParameter param : parameters) {
                    // 这里可以添加参数状态字段，暂时只记录日志
                    param.setUpdatedBy(currentUser);
                    param.setUpdatedAt(LocalDateTime.ofInstant(now.toInstant(), ZoneId.systemDefault()));
                    queryParameterMapper.updateById(param);
                }
            }
        } catch (Exception e) {
            log.error("更新查询参数状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 设置查询版本DTO的数据源信息
     */
    private void setDataSourceInfo(QueryVersionDTO versionDTO) {
        if (versionDTO != null && versionDTO.getDataSourceId() != null) {
            try {
                Datasource datasource = datasourceMapper.selectById(versionDTO.getDataSourceId());
                if (datasource != null) {
                    QueryVersionDTO.DataSourceInfo dataSourceInfo = QueryVersionDTO.DataSourceInfo.builder()
                        .id(datasource.getId())
                        .name(datasource.getName())
                        .type(datasource.getType())
                        .build();
                    versionDTO.setDataSource(dataSourceInfo);
                }
            } catch (Exception e) {
                log.error("获取数据源信息失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 移除SQL中的注释
     * 支持以下类型的注释：
     * 1. 单行注释：以--开头
     * 2. 多行注释：以/*开头，以* /结尾
     *
     * @param sql 原始SQL
     * @return 移除注释后的SQL
     */
    private String removeComments(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        StringBuilder result = new StringBuilder();
        boolean inLineComment = false;    // 是否在单行注释中
        boolean inBlockComment = false;   // 是否在多行注释中
        boolean inQuotes = false;         // 是否在引号中
        char[] chars = sql.toCharArray();

        for (int i = 0; i < chars.length; i++) {
            // 处理引号内的内容
            if (chars[i] == '\'') {
                if (!inLineComment && !inBlockComment) {
                    // 检查是否是转义的引号
                    if (i + 1 < chars.length && chars[i + 1] == '\'') {
                        if (!inQuotes) {
                            result.append(chars[i]);
                        }
                        result.append(chars[i]);
                        i++;
                        continue;
                    }
                    inQuotes = !inQuotes;
                    result.append(chars[i]);
                }
                continue;
            }

            // 如果在引号中，直接添加字符
            if (inQuotes) {
                result.append(chars[i]);
                continue;
            }

            // 处理单行注释
            if (!inBlockComment && i + 1 < chars.length && chars[i] == '-' && chars[i + 1] == '-') {
                inLineComment = true;
                i++; // 跳过第二个'-'
                continue;
            }

            // 处理多行注释开始
            if (!inLineComment && i + 1 < chars.length && chars[i] == '/' && chars[i + 1] == '*') {
                inBlockComment = true;
                i++; // 跳过'*'
                continue;
            }

            // 处理多行注释结束
            if (inBlockComment && i + 1 < chars.length && chars[i] == '*' && chars[i + 1] == '/') {
                inBlockComment = false;
                i++; // 跳过'/'
                continue;
            }

            // 处理换行符（结束单行注释）
            if (chars[i] == '\n' || chars[i] == '\r') {
                inLineComment = false;
                // 保留换行符以保持SQL格式
                result.append(chars[i]);
                continue;
            }

            // 如果不在任何注释中，添加字符
            if (!inLineComment && !inBlockComment) {
                result.append(chars[i]);
            }
        }

        // 移除多余的空白字符
        return result.toString().replaceAll("\\s+", " ").trim();
    }

    /**
     * 将SELECT语句转换为COUNT语句
     *
     * @param sql 原始SQL
     * @return COUNT SQL
     */
    private static String convertToCountSql(String sql) {
        // 构建新的COUNT SQL
        return "SELECT COUNT(1) from (" + sql + ") tt";
    }

    /**
     * 判断字符串是否为JSON格式
     *
     * @param value 待判断的字符串
     * @return 是否为JSON格式
     */
    private boolean isJsonFormat(String value) {
        if (StrUtil.isBlank(value)) {
            return false;
        }
        String trimmed = value.trim();
        return trimmed.startsWith("{") && trimmed.endsWith("}") && trimmed.contains(":");
    }

    /**
     * 判断字符串是否为Map格式 key=value, key2=value2
     *
     * @param value 待判断的字符串
     * @return 是否为Map格式
     */
    private boolean isMapFormat(String value) {
        if (StrUtil.isBlank(value)) {
            return false;
        }
        String trimmed = value.trim();
        // Map格式：不包含大括号，包含等号，可能包含逗号分隔多个键值对
        return !trimmed.startsWith("{") && !trimmed.startsWith("\"") && trimmed.contains("=");
    }

    /**
     * 处理JSON格式的value进行GM解密
     *
     * @param value JSON字符串
     * @param gmFields 需要解密的字段集合
     * @return 处理后的JSON字符串
     */
    private String processJsonValueForGM(String value, Set<String> gmFields) {
        try {
            JsonNode jsonNode = objectMapper.readTree(value);
            if (!jsonNode.isObject()) {
                return value;
            }

            ObjectNode objectNode = (ObjectNode) jsonNode;
            boolean hasChanges = false;

            for (String fieldName : gmFields) {
                JsonNode fieldNode = objectNode.get(fieldName);
                if (fieldNode != null && !fieldNode.isNull() && fieldNode.isTextual()) {
                    String fieldValue = fieldNode.asText();
                    if (StrUtil.isNotBlank(fieldValue)) {
                        try {
                            String decryptedValue = DoorgodSMUtils.decodeByAppId(fieldValue);
                            objectNode.put(fieldName, decryptedValue);
                            hasChanges = true;
                        } catch (Exception e) {
                            log.warn("GM解密字段[{}]失败: {}", fieldName, e);
                        }
                    }
                }
            }

            return hasChanges ? objectMapper.writeValueAsString(objectNode) : value;
        } catch (Exception e) {
            log.error("处理JSON格式GM解密失败: {}", e.getMessage());
            return value;
        }
    }

    /**
     * 处理Map格式的value进行GM解密
     *
     * @param value Map字符串，格式如 key=value, key2=value2
     * @param gmFields 需要解密的字段集合
     * @return 处理后的Map字符串
     */
    private String processMapValueForGM(String value, Set<String> gmFields) {
        try {
            // 直接处理Map格式字符串，不需要移除大括号
            String content = value.trim();

            // 解析键值对
            List<String> resultPairs = new ArrayList<>();
            String[] pairs = content.split(",");
            boolean hasChanges = false;

            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String val = keyValue[1].trim();

                    if (gmFields.contains(key) && StrUtil.isNotBlank(val)) {
                        try {
                            String decryptedValue = DoorgodSMUtils.decodeByAppId(val);
                            resultPairs.add(key + "=" + decryptedValue);
                            hasChanges = true;
                        } catch (Exception e) {
                            log.warn("GM解密字段[{}]失败: {}", key, e.getMessage());
                            resultPairs.add(key + "=" + val);
                        }
                    } else {
                        resultPairs.add(key + "=" + val);
                    }
                }
            }

            if (hasChanges) {
                // 重新构建Map字符串，不添加大括号
                return String.join(", ", resultPairs);
            }

            return value;
        } catch (Exception e) {
            log.error("处理Map格式GM解密失败: {}", e.getMessage());
            return value;
        }
    }
}

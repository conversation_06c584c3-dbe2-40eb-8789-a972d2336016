package com.datascope.app.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.factory.AbstractAuthCenter;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.model.ResourceBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DbAuthCenterServiceImpl extends AbstractAuthCenter {

    @Autowired
    private DatasourceMapper datasourceMapper;

    @Override
    public String getAuthType() {
        return AuthTypeEnum.DATASOURCE.getCode();
    }

    /**
     * 构建资源对象
     *
     * @param authDTO 授权DTO
     * @return 资源对象
     */
    public ResourceBO assembleBo(AuthDTO authDTO) {
        Datasource datasource = authDTO.getDatasource();
        if (datasource == null) {
            log.error("构建资源对象失败：数据源对象为空");
            throw new RuntimeException("数据源对象为空，无法构建资源标识");
        }

        String resourceCode = datasource.getDatabaseName();
        log.debug("构建数据源资源标识: {}", resourceCode);

        return new ResourceBO()
                .setResourceCode(resourceCode)
                .setResourceName(resourceCode);
    }

    @Override
    public ResourceBO addAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public ResourceBO removeAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public Pair<Boolean, Boolean> handle(AuthDTO authDTO) {
        // 查询数据源信息
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Datasource::getId, authDTO.getId());
        Datasource datasource = datasourceMapper.selectOne(wrapper);
        Assert.isTrue(Objects.nonNull(datasource), () -> new RuntimeException("datasource not found"));

        // 先设置数据源对象，确保后续操作可以正确获取数据源信息
        authDTO.setDatasource(datasource);

        // 记录操作日志
        log.info("处理数据源授权配置: ID={}, 名称={}, 当前授权状态={}, 目标授权状态={}",
                datasource.getId(), datasource.getName(),
                datasource.getIsAuthRequired(), authDTO.getAuthRequired());

        // 更新授权状态
        if (Objects.nonNull(authDTO.getAuthRequired()) && !authDTO.getAuthRequired().equals(datasource.getIsAuthRequired())) {
            LambdaUpdateWrapper<Datasource> wrapperUpdate = new LambdaUpdateWrapper<>();
            wrapperUpdate.eq(Datasource::getId, authDTO.getId());
            wrapperUpdate.set(Datasource::getIsAuthRequired, authDTO.getAuthRequired());
            this.datasourceMapper.update(null, wrapperUpdate);

            // 记录更新结果
            log.info("数据源授权状态已更新: ID={}, 名称={}, 旧状态={}, 新状态={}",
                    datasource.getId(), datasource.getName(),
                    datasource.getIsAuthRequired(), authDTO.getAuthRequired());
        }

        return Pair.of(datasource.getIsAuthRequired(), authDTO.getAuthRequired());
    }
}

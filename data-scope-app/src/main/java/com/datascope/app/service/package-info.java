/**
 * Domain Services Package
 *
 * Contains domain service interfaces and implementations that encapsulate business logic
 * that doesn't naturally fit within domain entities. These services coordinate multiple
 * domain objects to perform business operations.
 *
 * Key services:
 * - DataSourceService: Handles data source registration and metadata synchronization
 * - QueryService: Manages query execution and processing
 * - RelationshipService: Handles table relationship inference and management
 * - MetadataService: Coordinates metadata extraction and updates
 *
 * Domain services focus on business rules and orchestration, delegating technical
 * concerns to the infrastructure layer through repository interfaces.
 */
package com.datascope.app.service;

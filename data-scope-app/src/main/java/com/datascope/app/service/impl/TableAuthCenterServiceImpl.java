package com.datascope.app.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.factory.AbstractAuthCenter;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.mapper.TableMapper;
import com.datascope.app.model.ResourceBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TableAuthCenterServiceImpl extends AbstractAuthCenter {

    @Autowired
    private TableMapper tableMapper;

    @Autowired
    private DatasourceMapper datasourceMapper;

    @Autowired
    private SchemaMapper schemaMapper;

    @Override
    public String getAuthType() {
        return AuthTypeEnum.TABLE.getCode();
    }

    public ResourceBO assembleBo(AuthDTO authDTO) {
        Table table = authDTO.getTable();
        String datasourceId = table.getDatasourceId();
        String schemaId = table.getSchemaId();
        LambdaQueryWrapper<Datasource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Datasource::getId, datasourceId);
        Datasource datasource = datasourceMapper.selectOne(wrapper);
        LambdaQueryWrapper<Schema> schemaLambdaQueryWrapper = new LambdaQueryWrapper<>();
        schemaLambdaQueryWrapper.eq(Schema::getId, schemaId);
        Schema schema = schemaMapper.selectOne(schemaLambdaQueryWrapper);
        String code = datasource.getDatabaseName() + StrPool.COLON
                + schema.getName() + StrPool.COLON + table.getName();
        return new ResourceBO().setResourceCode(code).setResourceName(code);
    }

    @Override
    public ResourceBO addAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public ResourceBO removeAuthResource(AuthDTO authDTO) {
        return assembleBo(authDTO);
    }

    @Override
    public Pair<Boolean, Boolean> handle(AuthDTO authDTO) {
        // 查询表信息
        LambdaQueryWrapper<Table> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Table::getId, authDTO.getId());
        Table table = tableMapper.selectOne(wrapper);
        Assert.isTrue(Objects.nonNull(table), () -> new RuntimeException("table not found"));

        // 先设置表对象，确保后续操作可以正确获取表信息
        authDTO.setTable(table);

        // 记录操作日志
        log.info("处理表授权配置: ID={}, 名称={}, 当前授权状态={}, 目标授权状态={}",
                table.getId(), table.getName(),
                table.getIsAuthRequired(), authDTO.getAuthRequired());

        // 更新授权状态
        if (Objects.nonNull(authDTO.getAuthRequired()) && !authDTO.getAuthRequired().equals(table.getIsAuthRequired())) {
            LambdaUpdateWrapper<Table> wrapperUpdate = new LambdaUpdateWrapper<>();
            wrapperUpdate.eq(Table::getId, authDTO.getId());
            wrapperUpdate.set(Table::getIsAuthRequired, authDTO.getAuthRequired());
            this.tableMapper.update(null, wrapperUpdate);

            // 记录更新结果
            log.info("表授权状态已更新: ID={}, 名称={}, 旧状态={}, 新状态={}",
                    table.getId(), table.getName(),
                    table.getIsAuthRequired(), authDTO.getAuthRequired());
        }

        return Pair.of(table.getIsAuthRequired(), authDTO.getAuthRequired());
    }
}

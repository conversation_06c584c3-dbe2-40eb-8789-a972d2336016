package com.datascope.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.dto.query.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询服务接口
 */
public interface QueryService {

    /**
     * 获取查询列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param search 搜索关键词
     * @param queryType 查询类型
     * @param status 状态
     * @param serviceStatus 服务状态
     * @param dataSourceId 数据源ID
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @param includeDrafts 是否包含草稿
     * @return 查询分页结果
     */
    Page<QueryDTO> getQueries(int page, int size, String search, String queryType, String status,
                            String serviceStatus, String dataSourceId, String sortBy, String sortDir,
                            boolean includeDrafts);

    /**
     * 创建查询
     *
     * @param params 查询参数
     * @return 创建的查询
     */
    QueryDTO createQuery(SaveQueryParams params);

    /**
     * 获取查询详情
     *
     * @param id 查询ID
     * @return 查询详情
     */
    QueryDTO getQuery(String id);

    /**
     * 更新查询
     *
     * @param id 查询ID
     * @param params 更新参数
     * @return 更新后的查询
     */
    QueryDTO updateQuery(String id, SaveQueryParams params);

    /**
     * 删除查询
     *
     * @param id 查询ID
     * @return 是否成功
     */
    boolean deleteQuery(String id);

    /**
     * 执行查询
     *
     * @param id 查询ID
     * @param params 执行参数
     * @return 查询结果
     */
    QueryResultDTO executeQuery(String id, ExecuteQueryParams params);

    /**
     * 获取收藏的查询列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 状态
     * @param dataSourceId 数据源ID
     * @param search 搜索关键词
     * @return 收藏查询分页结果
     */
    Page<QueryDTO> getFavorites(int page, int size, String status, String dataSourceId, String search);

    /**
     * 收藏查询
     *
     * @param id 查询ID
     * @return 是否成功
     */
    boolean favoriteQuery(String id);

    /**
     * 取消收藏查询
     *
     * @param id 查询ID
     * @return 是否成功
     */
    boolean unfavoriteQuery(String id);

    /**
     * 自然语言转SQL
     *
     * @param params 自然语言查询参数
     * @return SQL结果
     */
    NaturalLanguageToSqlResult nlToSql(NaturalLanguageQueryParams params);

    /**
     * 获取查询执行历史
     *
     * @param queryId 查询ID
     * @param page 页码
     * @param size 每页大小
     * @return 执行历史分页结果
     */
    Page<ExecutionHistoryDTO> getExecutionHistory(String queryId, int page, int size);

    /**
     * 获取查询参数
     *
     * @param queryId 查询ID
     * @return 查询参数列表
     */
    QueryDefinitionDTO getQueryParameters(String queryId);

    /**
     * 获取查询版本列表
     *
     * @param queryId 查询ID
     * @param page 页码
     * @param size 每页大小
     * @param status 状态过滤
     * @return 查询版本分页结果
     */
    Page<QueryVersionDTO> getQueryVersions(String queryId, int page, int size, String status);

    /**
     * 创建查询版本
     *
     * @param queryId 查询ID
     * @param sqlContent SQL内容
     * @param description 描述
     * @param comment 注释
     * @return 创建的版本
     */
    QueryVersionDTO createQueryVersion(String queryId, String sqlContent, String description, String comment);

    /**
     * 获取查询版本详情
     *
     * @param versionId 版本ID
     * @return 版本详情
     */
    QueryVersionDTO getQueryVersion(String versionId);

    /**
     * 更新查询版本
     *
     * @param versionId 版本ID
     * @param sqlContent SQL内容
     * @param description 描述
     * @param comment 注释
     * @return 更新后的版本
     */
    QueryVersionDTO updateQueryVersion(String versionId, String sqlContent, String description, String comment);

    /**
     * 发布查询版本
     *
     * @param versionId 版本ID
     * @return 发布后的版本
     */
    QueryVersionDTO publishQueryVersion(String versionId);

    /**
     * 废弃查询版本
     *
     * @param versionId 版本ID
     * @return 废弃后的版本
     */
    QueryVersionDTO deprecateQueryVersion(String versionId);

    /**
     * 激活查询版本
     *
     * @param queryId 查询ID
     * @param versionId 版本ID
     * @return 是否成功
     */
    boolean activateQueryVersion(String queryId, String versionId);

    /**
     * 执行特定版本的查询
     *
     * @param queryId 查询ID
     * @param versionId 版本ID
     * @param params 执行参数
     * @return 查询结果
     */
    QueryResultDTO executeQueryVersion(String queryId, String versionId, ExecuteQueryParams params);

    /**
     * 执行拼接查询
     *
     * @param queryId 查询ID
     * @param versionId 版本ID
     * @param params 执行参数
     * @param integrationId integrationId
     * @return 查询结果
     */
    QueryResultDTO executeAppendQuery(String queryId, String versionId, String integrationId, ExecuteQueryParams params);

    /**
    * 启用查询
    *
    * @param queryId 查询ID
    * @return 是否成功
    */
   boolean enableQuery(String queryId);

   /**
    * 禁用查询
    *
    * @param queryId 查询ID
    * @return 是否成功
    */
   boolean disableQuery(String queryId);

   /**
    * 直接执行SQL语句
    *
    * @param dataSourceId 数据源ID
    * @param params 执行参数（包含SQL）
    * @return 查询结果
    */
   QueryResultDTO executeSQL(String dataSourceId, ExecuteQueryParams params);

    /**
     * 自然语言转SQL结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class NaturalLanguageToSqlResult {
        private String sql;
        private String explanation;
        private List<String> tables;
    }

    /**
     * 分析SQL获取查询参数信息
     */
    QueryDefinitionDTO analyzeQueryParameters(AnalyzeQueryParams params);

    /**
     * 获取查询执行计划
     *
     * @param queryId 查询ID
     * @return 执行计划
     */
    ExecutionPlanDTO getExecutionPlan(String queryId);

    /**
     * 获取SQL执行计划（无需保存查询）
     *
     * @param params 执行计划参数
     * @return 执行计划
     */
    ExecutionPlanDTO getExecutionPlanForSQL(ExecutionPlanParams params);
}

package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("queries")
public class Query {

    /**
     * 查询ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 查询名称
     */
    @TableField("name")
    private String name;

    /**
     * 查询描述
     */
    @TableField("description")
    private String description;

    /**
     * 文件夹ID
     */
    @TableField("folder_id")
    private String folderId;

    /**
     * 状态：DRAFT-草稿，PUBLISHED-已发布，DEPRECATED-已废弃，ARCHIVED-已归档
     */
    @TableField("status")
    private String status;

    /**
     * 服务状态：ENABLED-启用，DISABLED-禁用
     */
    @TableField("service_status")
    private String serviceStatus;

    /**
     * 数据源ID
     */
    @TableField("data_source_id")
    private String dataSourceId;

    /**
     * 查询类型：SQL-SQL查询，NATURAL_LANGUAGE-自然语言查询
     */
    @TableField("query_type")
    private String queryType;

    /**
     * 是否公开
     */
    @TableField("is_public")
    private Boolean isPublic;

    /**
     * 结果数量
     */
    @TableField("result_count")
    private Integer resultCount;

    /**
     * 执行时间(毫秒)
     */
    @TableField("execution_time")
    private BigDecimal executionTime;

    /**
     * 执行次数
     */
    @TableField("execution_count")
    private Integer executionCount;

    /**
     * 最后执行时间
     */
    @TableField("last_executed_at")
    private Date lastExecutedAt;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 乐观锁
     */
    @TableField("nonce")
    @Version
    private Integer nonce;
}
package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 列信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("`columns`")
public class Column {

    /**
     * 列ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 表ID
     */
    @TableField("table_id")
    private String tableId;

    /**
     * 列名称
     */
    @TableField("name")
    private String name;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 列类型
     */
    @TableField("column_type")
    private String columnType;

    /**
     * 位置
     */
    @TableField("position")
    private Integer position;

    /**
     * 是否可为空
     */
    @TableField("is_nullable")
    private Boolean isNullable;

    /**
     * 是否主键
     */
    @TableField("is_primary_key")
    private Boolean isPrimaryKey;

    /**
     * 是否唯一
     */
    @TableField("is_unique")
    private Boolean isUnique;

    /**
     * 是否索引
     */
    @TableField("is_indexed")
    private Boolean isIndexed;

    /**
     * 是否加密列
     */
    @TableField("is_encrypted")
    private Boolean isEncrypted;

    /**
     * 默认值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 字符长度
     */
    @TableField("character_length")
    private Integer characterLength;

    /**
     * 数值精度
     */
    @TableField("numeric_precision")
    private Integer numericPrecision;

    /**
     * 数值刻度
     */
    @TableField("numeric_scale")
    private Integer numericScale;

    /**
     * 列描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 加密配置
     */
    @TableField("encrypt_config")
    private String encryConfig;

    /**
     * 是否需要单独申请授权
     */
    @TableField("is_auth_required")
    private Boolean isAuthRequired;
}

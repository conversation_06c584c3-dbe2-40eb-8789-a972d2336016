package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

/**
 * 查询参数实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("query_parameters")
public class QueryParameter {

    /**
     * 参数ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 查询ID
     */
    @TableField("query_id")
    private String queryId;

    /**
     * 版本ID
     */
    @TableField("version_id")
    private String versionId;

    /**
     * 参数名称
     */
    @TableField("name")
    private String name;

    /**
     * 参数类型：string-字符串，number-数字，boolean-布尔值，date-日期
     */
    @TableField("type")
    private String type;

    /**
     * 参数标签
     */
    @TableField("label")
    private String label;

    /**
     * 默认值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 是否必填
     */
    @TableField("required")
    private Boolean required;

    /**
     * 选项(JSON格式)
     */
    @TableField("options")
    private String options;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 乐观锁
     */
    @Version
    @TableField("nonce")
    private Integer nonce;
} 
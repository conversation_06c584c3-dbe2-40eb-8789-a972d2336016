package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询执行历史实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("execution_history")
public class ExecutionHistory {

    /**
     * 执行历史ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 查询ID
     */
    @TableField("query_id")
    private String queryId;

    /**
     * 版本ID
     */
    @TableField("version_id")
    private String versionId;

    /**
     * 执行人
     */
    @TableField("executed_by")
    private String executedBy;

    /**
     * 执行时间
     */
    @TableField("executed_at")
    private Date executedAt;

    /**
     * 状态：success-成功，error-错误，running-运行中，cancelled-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 执行时长(毫秒)
     */
    @TableField("duration")
    private BigDecimal duration;

    /**
     * 结果行数
     */
    @TableField("row_count")
    private Integer rowCount;

    /**
     * 执行参数(JSON格式)
     */
    @TableField("parameters")
    private String parameters;

    /**
     * 错误信息
     */
    @TableField("error")
    private String error;

    /**
     * 结果ID
     */
    @TableField("result_id")
    private String resultId;
} 
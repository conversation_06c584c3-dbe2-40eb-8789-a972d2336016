package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 数据库模式实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("`schemas`")
public class Schema {

    /**
     * 模式ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 数据源ID
     */
    @TableField("datasource_id")
    private String datasourceId;

    /**
     * 模式名称
     */
    @TableField("name")
    private String name;

    /**
     * 模式描述
     */
    @TableField("description")
    private String description;

    /**
     * 表数量
     */
    @TableField("tables_count")
    private Integer tablesCount;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否需要单独申请授权
     */
    @TableField("is_auth_required")
    private Boolean isAuthRequired;
}

package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.datascope.app.mapper.typehandler.EncryptedPasswordTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据源实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "datasources", autoResultMap = true)
public class Datasource {

    /**
     * 数据源ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 数据源名称
     */
    @TableField("name")
    private String name;

    /**
     * 数据源描述
     */
    @TableField("description")
    private String description;

    /**
     * 数据源类型：mysql, postgresql, oracle, sqlserver, mongodb, elasticsearch
     */
    @TableField("type")
    private String type;

    /**
     * 主机地址
     */
    @TableField("host")
    private String host;

    /**
     * 端口号
     */
    @TableField("port")
    private Integer port;

    /**
     * 数据库名称
     */
    @TableField("database_name")
    private String databaseName;

    /**
     * 模式名称
     */
    @TableField(value = "`schema`")
    private String schema;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码(加密存储)
     */
    @TableField(value = "password", typeHandler = EncryptedPasswordTypeHandler.class)
    private String password;

    /**
     * 状态：active-活跃，inactive-不活跃，error-错误，syncing-同步中
     */
    @TableField("status")
    private String status;

    /**
     * 同步频率：manual-手动，hourly-每小时，daily-每天，weekly-每周，monthly-每月
     */
    @TableField("sync_frequency")
    private String syncFrequency;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private Date lastSyncTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 连接参数(JSON格式)
     */
    @TableField("connection_params")
    private String connectionParams;

    /**
     * 加密类型：none-无，ssl-SSL，tls-TLS
     */
    @TableField("encryption_type")
    private String encryptionType;

    /**
     * 加密选项(JSON格式)
     */
    @TableField("encryption_options")
    private String encryptionOptions;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 乐观锁
     */
    @Version
    @TableField("nonce")
    private Integer nonce;

    /**
     * 是否需要单独申请授权
     */
    @TableField("is_auth_required")
    private Boolean isAuthRequired;
}

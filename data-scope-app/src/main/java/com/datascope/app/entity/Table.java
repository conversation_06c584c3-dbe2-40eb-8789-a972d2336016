package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 数据表信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("`tables`")
@EqualsAndHashCode(callSuper = true)
public class Table extends BaseEntity {

    /**
     * 表ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 模式ID
     */
    @TableField("schema_id")
    private String schemaId;

    /**
     * 数据源ID
     */
    @TableField("datasource_id")
    private String datasourceId;

    /**
     * 表名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型：TABLE-表，VIEW-视图
     */
    @TableField("type")
    private String type;

    /**
     * 表描述
     */
    @TableField("description")
    private String description;

    /**
     * 行数
     */
    @TableField("row_count")
    private Long rowCount;

    /**
     * 列数
     */
    @TableField("columns_count")
    private Integer columnsCount;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否需要单独申请授权
     */
    @TableField("is_auth_required")
    private Boolean isAuthRequired;

    /**
     * 获取完整表名（包含schema）
     */
    public String getFullName() {
        return String.format("%s.%s", schemaId, name);
    }
}

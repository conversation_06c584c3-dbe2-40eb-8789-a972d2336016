package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 元数据同步记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("metadata_sync")
public class MetadataSync {

    /**
     * 同步ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 数据源ID
     */
    @TableField("datasource_id")
    private String datasourceId;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 状态：running-运行中，completed-完成，failed-失败
     */
    @TableField("status")
    private String status;

    /**
     * 表数量
     */
    @TableField("tables_count")
    private Integer tablesCount;

    /**
     * 视图数量
     */
    @TableField("views_count")
    private Integer viewsCount;

    /**
     * 同步时长(毫秒)
     */
    @TableField("sync_duration")
    private Integer syncDuration;

    /**
     * 消息
     */
    @TableField("message")
    private String message;

    /**
     * 错误信息(JSON格式)
     */
    @TableField("errors")
    private String errors;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;
}
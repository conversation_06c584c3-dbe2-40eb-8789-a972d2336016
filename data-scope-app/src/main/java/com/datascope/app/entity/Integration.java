package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 集成表实体类
 */
@Data
@Accessors(chain = true)
@TableName("integrations")
public class Integration {

    /**
     * 集成ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 集成名称
     */
    @TableField("name")
    private String name;

    /**
     * 集成描述
     */
    @TableField("description")
    private String description;

    /**
     * 集成类型：SIMPLE_TABLE-简单表格，TABLE-表格，CHART-图表
     */
    @TableField("type")
    private String type;

    /**
     * 状态：ACTIVE-活跃，INACTIVE-不活跃，DRAFT-草稿
     */
    @TableField("status")
    private String status;

    /**
     * 查询ID
     */
    @TableField("query_id")
    private String queryId;

    /**
     * 数据源ID
     */
    @TableField("datasource_id")
    private String dataSourceId;

    /**
     * 查询参数(JSON格式)
     */
    @TableField("query_params")
    private String queryParams;

    /**
     * 表格配置(JSON格式)
     */
    @TableField("table_config")
    private String tableConfig;

    /**
     * 图表配置(JSON格式)
     */
    @TableField("chart_config")
    private String chartConfig;

    /**
     * 元数据(JSON格式)
     */
    @TableField("meta")
    private String meta;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 乐观锁
     */
    @Version
    @TableField("nonce")
    private Integer nonce;
} 
package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 数据源统计信息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("datasource_stats")
public class DatasourceStats {

    /**
     * 统计ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 数据源ID
     */
    @TableField("datasource_id")
    private String datasourceId;

    /**
     * 表数量
     */
    @TableField("tables_count")
    private Integer tablesCount;

    /**
     * 视图数量
     */
    @TableField("views_count")
    private Integer viewsCount;

    /**
     * 总行数
     */
    @TableField("total_rows")
    private Long totalRows;

    /**
     * 总大小
     */
    @TableField("total_size")
    private String totalSize;

    /**
     * 查询数量
     */
    @TableField("queries_count")
    private Integer queriesCount;

    /**
     * 连接池大小
     */
    @TableField("connection_pool_size")
    private Integer connectionPoolSize;

    /**
     * 活跃连接数
     */
    @TableField("active_connections")
    private Integer activeConnections;

    /**
     * 平均查询时间
     */
    @TableField("avg_query_time")
    private String avgQueryTime;

    /**
     * 平均响应时间(毫秒)
     */
    @TableField("avg_response_time")
    private Double avgResponseTime;

    /**
     * 峰值连接数
     */
    @TableField("peak_connections")
    private Integer peakConnections;

    /**
     * 最后更新时间
     */
    @TableField("last_update")
    private Date lastUpdate;
} 
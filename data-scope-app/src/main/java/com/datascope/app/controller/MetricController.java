package com.datascope.app.controller;

import com.datascope.app.util.DoorgodSMUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-04-18 11:58
 */
@RestController
@Slf4j
public class MetricController {

    @RequestMapping(value = {"/metrics/healthcheck", "/"})
    public Map<String, Object> healthCheck() {
        /*
        {
  "deadlock": {
    "healthy": true,
    "timestamp": "2025-04-18T11:59:12.243"
  }
}
         */
        // json 响应
        Map<String, Object> result = new HashMap<>();
        result.put("deadlock", new HashMap<String, Object>() {{
            put("healthy", true);
            put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }});
        return result;
    }

    @RequestMapping("/test")
    public Map<String, Object> test(String name) {
        if (StringUtils.isEmpty(name)) {
            name = "你好";
        }

        String encodeResult = DoorgodSMUtils.encode(name);
        log.info("encodeResult = {}", encodeResult);
        String decodeResult = DoorgodSMUtils.decode(encodeResult);
        log.info("decodeResult = {}", decodeResult);

        // json 响应
        Map<String, Object> result = new HashMap<>();
        result.put("encodeResult", encodeResult);
        result.put("decodeResult", decodeResult);

        return result;
    }


}

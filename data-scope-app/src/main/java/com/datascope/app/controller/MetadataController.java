package com.datascope.app.controller;

import com.datascope.app.common.response.Response;
import com.datascope.app.dto.metadata.*;
import com.datascope.app.service.MetadataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 元数据管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/metadata")
@Tag(name = "元数据管理模块")
public class MetadataController {

    private final MetadataService metadataService;

    /**
     * 同步数据源元数据
     *
     * @param dataSourceId 数据源ID
     * @param request      同步请求参数
     * @return 同步结果
     */
    @PostMapping("/datasources/{dataSourceId}/sync")
    @Operation(summary = "同步元数据")
    public Response<SyncMetadataResponse> syncMetadata(
        @PathVariable String dataSourceId,
        @RequestBody SyncMetadataRequest request) {
        log.info("同步数据源[{}]元数据，参数: {}", dataSourceId, request);
        SyncMetadataResponse response = metadataService.syncMetadata(dataSourceId, request);
        return Response.ok(response);
    }

    /**
     * 获取数据源的schema列表
     *
     * @param id 数据源ID
     * @return schema列表
     */
    @GetMapping("/datasources/{id}/schemas")
    @Operation(summary = "获取数据源下的schema列表")
    public Response<List<SchemaDTO>> getSchemas(@PathVariable("id") String id) {
        log.info("获取数据源[{}]的schema列表", id);
        List<SchemaDTO> schemas = metadataService.getSchemas(id);
        return Response.ok(schemas);
    }

    /**
     * 获取schema的表列表
     *
     * @param id Schema ID
     * @return 表列表
     */
    @GetMapping("/schemas/{id}/tables")
    @Operation(summary = "获取schema下的表")
    public Response<List<TableDTO>> getTables(@PathVariable("id") String id) {
        log.info("获取Schema[{}]的表列表", id);
        List<TableDTO> tables = metadataService.getTables(id);
        return Response.ok(tables);
    }

    /**
     * 获取表的列列表
     *
     * @param id 表ID
     * @return 列列表
     */
    @GetMapping("/tables/{id}/columns")
    @Operation(summary = "获取表下的字段")
    public Response<List<ColumnDTO>> getColumns(@PathVariable("id") String id) {
        log.info("获取表[{}]的列列表", id);
        List<ColumnDTO> columns = metadataService.getColumns(id);
        return Response.ok(columns);
    }

    /**
     * 分页查询表数据
     *
     * @param tableId 表ID
     * @param request 查询参数
     * @return 表数据
     */
    @GetMapping("/tables/{tableId}/datas")
    @Operation(summary = "分页查询表数据")
    public Response<TableDataResponse> getTableData(
        @PathVariable("tableId") String tableId,
        @Valid TableDataQueryRequest request) {
        log.info("查询表[{}]数据，参数: {}", tableId, request);
        TableDataResponse response = metadataService.getTableData(tableId, request);
        return Response.ok(response);
    }

    /**
     * 更新表信息
     *
     * @param columnId 字段ID
     * @param param 查询参数
     * @return 表数据
     */
    @PutMapping("/columns/{columnId}/config")
    @Operation(summary = "更新字段元数据")
    public Response<Void> updateColumnEntry(@PathVariable("columnId") String columnId, @RequestBody Object param) {
        metadataService.updateColumnEntry(columnId, param);
        return Response.ok();
    }

    /**
     * 更新授权信息
     *
     * @param authDTO 参数
     * @return Void
     */
    @PutMapping(value = "/auth", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "更新授权信息")
    public Response<Void> authMetaData(@RequestBody AuthDTO authDTO) {
        metadataService.authMetaData(authDTO);
        return Response.ok();
    }
}

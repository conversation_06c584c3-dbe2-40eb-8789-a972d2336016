package com.datascope.app.controller;

import com.datascope.app.common.response.Response;
import com.datascope.app.dto.integration.ExecuteIntegrationQueryRequest;
import com.datascope.app.dto.integration.IntegrationQueryResultDTO;
import com.datascope.app.service.IntegrationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 集成管理兼容控制器
 * 用于处理旧版API路径
 *
 * @deprecated 该控制器仅用于兼容旧版API，新代码应使用 IntegrationController
 */
@Slf4j
@RestController
@RequestMapping("/api/integration")
@RequiredArgsConstructor
@Deprecated
@Tag(name = "集成管理兼容")
public class IntegrationCompatController {

    private final IntegrationService integrationService;

    /**
     * 执行集成查询（兼容旧路径）
     *
     * @param request 执行请求
     * @return 查询结果
     * @deprecated 请使用 /api/integrations/execute-query 路径
     */
    @Deprecated
    @PostMapping("/execute-query")
    public Response<IntegrationQueryResultDTO> executeIntegrationQuery(@Valid @RequestBody ExecuteIntegrationQueryRequest request) {
        log.warn("使用了已弃用的API路径: /api/integration/execute-query，请改用 /api/integrations/execute-query");
        IntegrationQueryResultDTO resultDTO = integrationService.executeIntegrationQuery(request);
        if (resultDTO == null) {
            return Response.error("执行查询失败");
        }
        return Response.ok(resultDTO);
    }
}

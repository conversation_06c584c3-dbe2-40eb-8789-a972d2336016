<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datascope.app.mapper.IntegrationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.datascope.app.entity.Integration">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="query_id" property="queryId"/>
        <result column="datasource_id" property="dataSourceId"/>
        <result column="chart_config" property="chartConfig"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="nonce" property="nonce"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, type, status, query_id, datasource_id, chart_config, 
        created_by, created_at, updated_by, updated_at, nonce
    </sql>

    <!-- 分页查询集成列表 -->
    <select id="selectIntegrationPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM integrations
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>
</mapper> 
# yp-gm-crypt SDK \u914D\u7F6E\u6587\u4EF6
# \u8BF7\u6839\u636E\u5B9E\u9645\u73AF\u5883\uFF08\u6D4B\u8BD5/\u751F\u4EA7\uFF09\u4FEE\u6539\u4EE5\u4E0B\u914D\u7F6E
# \u5BC6\u7801\u670D\u52A1\u5E73\u53F0\u5730\u5740 (KMS)
# \u6D4B\u8BD5\u5BB9\u5668\u4E91\uFF1Aqakms.sec.yp
# \u6D4B\u8BD5\u529E\u516C\u7F51\uFF1Aqakms.yeepay.com
# \u5185\u6D4B\u529E\u516C\u7F51\uFF1Aycenc.yeepay.com (\u7528\u4E8E\u5904\u7406\u751F\u4EA7\u95EE\u9898\u65F6\uFF0C\u672C\u5730\u7684\u89E3\u5BC6)
# \u5185\u6D4B\u3001\u751F\u4EA7\uFF1Akms.ptyf.yp
JT.kmsServer=qakms.yeepay.com
# \u5E94\u7528ID (\u65B9\u6848\u6807\u8BC6)\uFF0C\u591A\u4E2A\u7528\u9017\u53F7\u5206\u9694\u3002\u8054\u7CFB\u7BA1\u7406\u5458\u83B7\u53D6\u5E76\u914D\u7F6E\u3002
# *\u5FC5\u9700*
JT.appId=data_scope
# \u5BC6\u94A5\u6A21\u677F\u6807\u8BC6\u3002\u8054\u7CFB\u7BA1\u7406\u5458\u83B7\u53D6\u5E76\u914D\u7F6E\u3002
# *\u5FC5\u9700*
# QA \u73AF\u5883: CS
# \u751F\u4EA7\u3001\u5185\u6D4B\u73AF\u5883: YBZF
JT.keyModelID=CS
# \u63A5\u53E3\u8C03\u7528\u8D85\u65F6\u65F6\u95F4 (\u79D2)\uFF0C\u9ED8\u8BA4 5
JT.timeout=5
# \u63A5\u53E3\u8C03\u7528\u5931\u8D25\u91CD\u8BD5\u6B21\u6570\uFF0C\u9ED8\u8BA4 3
JT.retryTimes=3
# \u63A5\u53E3\u8C03\u7528\u5931\u8D25\u91CD\u8BD5\u95F4\u9694\u65F6\u95F4 (\u79D2)\uFF0C\u9ED8\u8BA4 3
JT.sleepTime=3
# \u52A0\u89E3\u5BC6\u7B56\u7565 (soft: \u8F6F\u5B9E\u73B0\u4F18\u5148, forced: \u5F3A\u5236\u52A0\u5BC6\u673A, priority: \u52A0\u5BC6\u673A\u4F18\u5148\u53EF\u964D\u7EA7)
# \u63A8\u8350\u4F7F\u7528 soft \u6216 priority
policy=soft
# \u6458\u8981\u8BA1\u7B97\u76D0\u503C (16\u4F4D\u968F\u673A\u6570\u5B57\u548C\u5B57\u6BCD\u7EC4\u5408)
# *\u5FC5\u9700* \u4E14\u4E2D\u9014\u4E0D\u53EF\u66F4\u6539\uFF0C\u4E0D\u53EF\u5305\u542B '&'
salt=fedcba0987654321
# --- \u53EF\u9009\u914D\u7F6E ---
# \u4E1A\u52A1\u81EA\u5B9A\u4E49\u7B97\u6CD5\u517C\u5BB9\u6807\u8BC6 (\u5982\u679C\u65E7\u7CFB\u7EDF\u4F7F\u7528\u4E86\u7279\u5B9AAES\u5B9E\u73B0)
# \u53EF\u9009\u503C: commonUtils, jceUtils, jceUtilsUserSource, default, useSpi
# \u5982\u679C\u8BBE\u7F6E\u4E86\u6B64\u9879\uFF0C\u901A\u5E38\u9700\u8981\u914D\u5408 customLocalSecretKey \u6216 decryptByCustomKey/decryptByFlag
# customAlgFlag=commonUtils
# \u4E1A\u52A1\u672C\u5730\u65E7\u5BC6\u94A5 (\u5982\u679C customAlgFlag \u88AB\u8BBE\u7F6E\u4E14\u9700\u8981\u65E7\u5BC6\u94A5)
# customLocalSecretKey=YOUR_OLD_LOCAL_SECRET_KEY

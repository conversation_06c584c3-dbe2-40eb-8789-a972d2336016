spring:
  application:
    name: data-scope-app
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null
    deserialization:
      fail-on-unknown-properties: false
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

server:
  port: 8080
  servlet:
    context-path: /data-scope

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.datascope.app.entity
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    banner: false
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  type-handlers-package: com.datascope.app.mapper.typehandler

logging:
  level:
    root: info
    com.datascope: debug

# 安全配置
datascope:
  security:
    # AES 加密密钥
    aes-key: ${AES_KEY:default-aes-key-do-not-use-in-production}
    # 盐值
    salt: ${SALT:default-salt-do-not-use-in-production}
    # SM4 加密密钥，必须是16位字符
    sm4-key: ${SM4_KEY:1234567890abcdef}

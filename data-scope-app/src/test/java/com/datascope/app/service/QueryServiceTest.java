package com.datascope.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.dto.query.QueryDTO;
import com.datascope.app.dto.query.SaveQueryParams;
import com.datascope.app.entity.Query;
import com.datascope.app.entity.QueryVersion;
import com.datascope.app.mapper.QueryFavoriteMapper;
import com.datascope.app.mapper.QueryMapper;
import com.datascope.app.mapper.QueryVersionMapper;
import com.datascope.app.service.impl.QueryServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 查询服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class QueryServiceTest {

    @Mock
    private QueryMapper queryMapper;

    @Mock
    private QueryVersionMapper queryVersionMapper;

    @Mock
    private QueryFavoriteMapper queryFavoriteMapper;

    @InjectMocks
    private QueryServiceImpl queryService;

    private SaveQueryParams createQueryParams;
    private String testQueryId;

    @BeforeEach
    void setUp() {
        testQueryId = UUID.randomUUID().toString().replace("-", "");

        // 创建测试查询参数
        createQueryParams = SaveQueryParams.builder()
                .name("测试查询")
                .dataSourceId("ds-001")
                .sql("SELECT * FROM users")
                .description("测试用查询")
                .queryType("SQL")
                .build();
    }

    @Test
    void testCreateQuery() {
        // 设置模拟行为
        when(queryMapper.insert(any(Query.class))).thenReturn(1);
        when(queryVersionMapper.insert(any(QueryVersion.class))).thenReturn(1);

        // 调用被测方法
        QueryDTO result = queryService.createQuery(createQueryParams);

        // 验证结果
        assertNotNull(result);
        assertEquals(createQueryParams.getName(), result.getName());
        assertEquals(createQueryParams.getDataSourceId(), result.getDataSourceId());

        // 验证调用
        verify(queryMapper, times(1)).insert(any(Query.class));
        verify(queryVersionMapper, times(1)).insert(any(QueryVersion.class));

        // 捕获并验证实体参数
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        verify(queryMapper).insert(queryCaptor.capture());
        Query insertedQuery = queryCaptor.getValue();

        assertEquals(createQueryParams.getName(), insertedQuery.getName());
        assertEquals(createQueryParams.getDataSourceId(), insertedQuery.getDataSourceId());
        assertEquals(createQueryParams.getDescription(), insertedQuery.getDescription());
        assertEquals(createQueryParams.getQueryType(), insertedQuery.getQueryType());

        // 验证版本
        ArgumentCaptor<QueryVersion> versionCaptor = ArgumentCaptor.forClass(QueryVersion.class);
        verify(queryVersionMapper).insert(versionCaptor.capture());
        QueryVersion insertedVersion = versionCaptor.getValue();

        assertEquals(createQueryParams.getSql(), insertedVersion.getSqlContent());
        assertEquals(createQueryParams.getDataSourceId(), insertedVersion.getDataSourceId());
        assertEquals("DRAFT", insertedVersion.getStatus());
        assertEquals(true, insertedVersion.getIsLatest());
    }

    @Test
    void testUpdateQuery() {
        // 准备测试数据
        Query existingQuery = Query.builder()
                .id(testQueryId)
                .name("原查询")
                .description("原描述")
                .dataSourceId("ds-001")
                .status("DRAFT")
                .serviceStatus("ENABLED")
                .queryType("SQL")
                .isPublic(false)
                .build();

        QueryVersion latestVersion = QueryVersion.builder()
                .id(UUID.randomUUID().toString().replace("-", ""))
                .queryId(testQueryId)
                .versionNumber(1)
                .name("原版本")
                .sqlContent("SELECT * FROM users")
                .dataSourceId("ds-001")
                .status("DRAFT")
                .isLatest(true)
                .build();

        // 设置更新参数
        SaveQueryParams updateParams = SaveQueryParams.builder()
                .name("更新后的查询")
                .description("更新后的描述")
                .dataSourceId("ds-002")
                .sql("SELECT * FROM users WHERE active = true")
                .queryType("SQL")
                .status("PUBLISHED")
                .build();

        // 设置模拟行为
        when(queryMapper.selectById(testQueryId)).thenReturn(existingQuery);
        when(queryMapper.updateById(any(Query.class))).thenReturn(1);

        when(queryVersionMapper.selectOne(any())).thenReturn(latestVersion);
        when(queryVersionMapper.updateById(any(QueryVersion.class))).thenReturn(1);
        when(queryVersionMapper.insert(any(QueryVersion.class))).thenReturn(1);

        // 调用被测方法
        QueryDTO result = queryService.updateQuery(testQueryId, updateParams);

        // 验证结果
        assertNotNull(result);
        assertEquals(updateParams.getName(), result.getName());
        assertEquals(updateParams.getDescription(), result.getDescription());

        // 验证查询更新
        verify(queryMapper, times(1)).updateById(any(Query.class));

        // 验证版本更新
        verify(queryVersionMapper, times(1)).updateById(any(QueryVersion.class));
        verify(queryVersionMapper, times(1)).insert(any(QueryVersion.class));

        // 捕获并验证查询更新
        ArgumentCaptor<Query> queryCaptor = ArgumentCaptor.forClass(Query.class);
        verify(queryMapper).updateById(queryCaptor.capture());
        Query updatedQuery = queryCaptor.getValue();

        assertEquals(updateParams.getName(), updatedQuery.getName());
        assertEquals(updateParams.getDescription(), updatedQuery.getDescription());
        assertEquals(updateParams.getStatus(), updatedQuery.getStatus());

        // 捕获并验证新版本
        ArgumentCaptor<QueryVersion> versionCaptor = ArgumentCaptor.forClass(QueryVersion.class);
        verify(queryVersionMapper).insert(versionCaptor.capture());
        QueryVersion newVersion = versionCaptor.getValue();

        assertEquals(testQueryId, newVersion.getQueryId());
        assertEquals(updateParams.getSql(), newVersion.getSqlContent());
        assertEquals(updateParams.getDataSourceId(), newVersion.getDataSourceId());
        assertEquals(2, newVersion.getVersionNumber());
        assertEquals(true, newVersion.getIsLatest());
    }

    @Test
    void testGetQueries() {
        // 准备测试数据 - 创建模拟查询列表
        List<Query> mockQueries = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            Query query = Query.builder()
                    .id("query-" + i)
                    .name("测试查询" + i)
                    .description("测试描述" + i)
                    .dataSourceId("ds-001")
                    .status(i <= 3 ? "PUBLISHED" : "DRAFT")
                    .serviceStatus("ENABLED")
                    .queryType("SQL")
                    .isPublic(true)
                    .executionCount(i * 10)
                    .createdBy("admin")
                    .createdAt(new Date())
                    .updatedBy("admin")
                    .updatedAt(new Date())
                    .build();
            mockQueries.add(query);
        }

        // 配置模拟行为 - 模拟分页查询结果
        Page<Query> mockPage = new Page<>(1, 10);
        mockPage.setRecords(mockQueries);
        mockPage.setTotal(mockQueries.size());
        mockPage.setPages(1);

        // 模拟任何查询条件都返回mockPage
        when(queryMapper.selectPage(any(), any())).thenReturn(mockPage);

        // 设置版本查询模拟
        QueryVersion mockVersion = QueryVersion.builder()
                .id(UUID.randomUUID().toString())
                .queryId("query-1")
                .versionNumber(1)
                .sqlContent("SELECT * FROM test")
                .isLatest(true)
                .build();
        when(queryVersionMapper.selectOne(any())).thenReturn(mockVersion);

        // 执行测试
        Page<QueryDTO> result = queryService.getQueries(1, 10, null, null, null, null, null, null, "desc", false);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getTotal());
        assertEquals(5, result.getRecords().size());

        // 验证第一条记录内容
        QueryDTO firstDto = result.getRecords().get(0);
        assertEquals("query-1", firstDto.getId());
        assertEquals("测试查询1", firstDto.getName());
        assertEquals("测试描述1", firstDto.getDescription());

        // 验证queryMapper.selectPage方法被调用了一次
        verify(queryMapper, times(1)).selectPage(any(), any());

        // 验证queryVersionMapper.selectOne方法被调用了5次（每个查询获取版本）
        verify(queryVersionMapper, times(5)).selectOne(any());
    }

    @Test
    void testGetQuery() {
        // 准备测试数据
        String queryId = "test-query-id";

        // 创建模拟Query对象
        Query mockQuery = Query.builder()
                .id(queryId)
                .name("测试查询详情")
                .description("查询详情测试描述")
                .dataSourceId("ds-001")
                .status("PUBLISHED")
                .serviceStatus("ENABLED")
                .queryType("SQL")
                .isPublic(true)
                .executionCount(15)
                .createdBy("admin")
                .createdAt(new Date())
                .updatedBy("admin")
                .updatedAt(new Date())
                .build();

        // 创建模拟QueryVersion对象
        QueryVersion mockVersion = QueryVersion.builder()
                .id(UUID.randomUUID().toString())
                .queryId(queryId)
                .versionNumber(2)
                .name("最新版本")
                .sqlContent("SELECT * FROM users WHERE active = 1")
                .dataSourceId("ds-001")
                .status("PUBLISHED")
                .isLatest(true)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();

        // 设置模拟行为
        when(queryMapper.selectById(queryId)).thenReturn(mockQuery);
        when(queryVersionMapper.selectOne(any())).thenReturn(mockVersion);
        when(queryFavoriteMapper.selectCount(any())).thenReturn(0); // 未收藏

        // 执行测试
        QueryDTO result = queryService.getQuery(queryId);

        // 验证结果
        assertNotNull(result);
        assertEquals(queryId, result.getId());
        assertEquals(mockQuery.getName(), result.getName());
        assertEquals(mockQuery.getDescription(), result.getDescription());

        // 验证版本信息
        assertNotNull(result.getCurrentVersion());
        assertEquals(mockVersion.getId(), result.getCurrentVersion().getId());
        assertEquals(mockVersion.getVersionNumber(), result.getCurrentVersion().getVersionNumber());
        assertEquals(mockVersion.getSqlContent(), result.getCurrentVersion().getSql());

        // 验证调用
        verify(queryMapper, times(1)).selectById(queryId);
        verify(queryVersionMapper, times(2)).selectOne(any()); // 一次在convertToDTO方法中，一次在getQuery方法中
        verify(queryFavoriteMapper, times(1)).selectCount(any());
    }
}

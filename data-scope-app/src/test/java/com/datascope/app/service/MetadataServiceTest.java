package com.datascope.app.service;

import com.datascope.app.dto.metadata.SyncMetadataRequest;
import com.datascope.app.dto.metadata.SyncMetadataResponse;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.MetadataSyncMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.mapper.TableMapper;
import com.datascope.app.service.impl.MetadataServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.datascope.app.dto.metadata.SchemaDTO;
import com.datascope.app.dto.metadata.TableDTO;

@ExtendWith(MockitoExtension.class)
public class MetadataServiceTest {

    @Mock
    private MetadataSyncMapper metadataSyncMapper;

    @Mock
    private SchemaMapper schemaMapper;

    @Mock
    private TableMapper tableMapper;

    @Mock
    private ColumnMapper columnMapper;

    @Mock
    private DatasourceMapper datasourceMapper;

    @InjectMocks
    private MetadataServiceImpl metadataService;

    private Datasource mockDatasource;
    private final String dataSourceId = UUID.randomUUID().toString();

    @BeforeEach
    void setUp() {
        // 准备模拟数据源
        mockDatasource = Datasource.builder()
                .id(dataSourceId)
                .name("测试数据源")
                .type("mysql")
                .host("localhost")
                .port(3306)
                .databaseName("test_db")
                .username("test_user")
                .password("test_pass")
                .status("active")
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();

        // 模拟数据源存在
        when(datasourceMapper.selectById(dataSourceId)).thenReturn(mockDatasource);
    }

    @Test
    void testSyncMetadata_Success() {
        // 准备请求参数
        SyncMetadataRequest request = new SyncMetadataRequest();
        SyncMetadataRequest.FiltersConfig filters = new SyncMetadataRequest.FiltersConfig();
        filters.setIncludeSchemas(Collections.singletonList("test_schema"));
        request.setFilters(filters);

        // 配置模拟行为
        // 由于无法直接模拟JDBC连接，此处只测试基本流程和异常处理
        doReturn(1).when(metadataSyncMapper).insert(any());
        doReturn(1).when(metadataSyncMapper).updateById(any());

        try {
            // 调用同步方法（预期会抛出异常，因为无法连接真实数据库）
            metadataService.syncMetadata(dataSourceId, request);
            fail("应该抛出异常，因为无法连接到真实数据库");
        } catch (Exception e) {
            // 验证更新同步记录的调用
            verify(metadataSyncMapper, times(1)).insert(any());
            verify(metadataSyncMapper, times(1)).updateById(any());

            // 确保异常消息包含数据库连接相关信息
            assertTrue(e.getMessage().contains("连接") || e.getMessage().contains("connection"),
                    "异常消息应该与数据库连接相关: " + e.getMessage());
        }
    }

    @Test
    void testSyncMetadata_WithNonExistentDataSource() {
        // 模拟数据源不存在
        when(datasourceMapper.selectById("non_existent_id")).thenReturn(null);

        // 准备请求参数
        SyncMetadataRequest request = new SyncMetadataRequest();

        // 调用同步方法
        Exception exception = assertThrows(RuntimeException.class, () -> {
            metadataService.syncMetadata("non_existent_id", request);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("数据源不存在"));
    }

    @Test
    void testGetSchemas() {
        // 模拟数据库中有两个schema
        Schema schema1 = Schema.builder()
                .id(UUID.randomUUID().toString())
                .datasourceId(dataSourceId)
                .name("schema1")
                .tablesCount(5)
                .build();

        Schema schema2 = Schema.builder()
                .id(UUID.randomUUID().toString())
                .datasourceId(dataSourceId)
                .name("schema2")
                .tablesCount(3)
                .build();

        when(schemaMapper.selectList(any())).thenReturn(java.util.Arrays.asList(schema1, schema2));

        // 调用方法
        java.util.List<SchemaDTO> schemas = metadataService.getSchemas(dataSourceId);

        // 验证结果
        assertEquals(2, schemas.size());
        assertEquals("schema1", schemas.get(0).getName());
        assertEquals(5, schemas.get(0).getTablesCount());
        assertEquals("schema2", schemas.get(1).getName());
    }

    @Test
    void testGetTables() {
        // 模拟数据库中有两个表
        String schemaId = UUID.randomUUID().toString();
        Table table1 = Table.builder()
                .id(UUID.randomUUID().toString())
                .schemaId(schemaId)
                .datasourceId(dataSourceId)
                .name("table1")
                .type("TABLE")
                .columnsCount(10)
                .build();

        Table table2 = Table.builder()
                .id(UUID.randomUUID().toString())
                .schemaId(schemaId)
                .datasourceId(dataSourceId)
                .name("table2")
                .type("VIEW")
                .columnsCount(5)
                .build();

        when(tableMapper.selectList(any())).thenReturn(java.util.Arrays.asList(table1, table2));

        // 调用方法
        java.util.List<TableDTO> tables = metadataService.getTables(schemaId);

        // 验证结果
        assertEquals(2, tables.size());
        assertEquals("table1", tables.get(0).getName());
        assertEquals("TABLE", tables.get(0).getType());
        assertEquals("table2", tables.get(1).getName());
        assertEquals("VIEW", tables.get(1).getType());
    }
}

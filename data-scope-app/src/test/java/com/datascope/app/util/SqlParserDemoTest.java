package com.datascope.app.util;

import java.util.Map;
import java.util.Set;

/**
 * SQL解析工具类的演示
 */
public class SqlParserDemoTest {

    public static void main(String[] args) {
        // 示例SQL语句
        String sql1 = "SELECT a.id, a.name, b.age FROM user a JOIN user_info b ON a.id = b.user_id WHERE a.id > 10 order by a.dt desc";
        String sql2 = "SELECT * FROM product WHERE price > 100";
        String sql3 = "SELECT u.*, p.name as product_name FROM user u JOIN product p ON u.product_id = p.id";
        String sql4 = "SELECT t1.id, t1.name, t2.* FROM table1 t1 LEFT JOIN table2 t2 ON t1.id = t2.t1_id";

        // 添加更复杂的SQL示例，测试ON条件中的字段解析
        String sql5 = "SELECT o.order_id, o.order_date, c.customer_name, p.product_name " +
                      "FROM orders o " +
                      "JOIN customers c ON o.customer_id = c.customer_id " +
                      "JOIN order_items oi ON o.order_id = oi.order_id " +
                      "JOIN products p ON oi.product_id = p.product_id " +
                      "WHERE o.order_date > '2023-01-01'";

        // 更新示例6，确保明确包含location和title字段
        String sql6 = "SELECT e.employee_id, e.name, d.department_name, d.location, m.name as manager_name, m.title " +
                      "FROM employees e " +
                      "LEFT JOIN departments d ON e.department_id = d.department_id " +
                      "LEFT JOIN employees m ON e.manager_id = m.employee_id " +
                      "WHERE e.hire_date > '2020-01-01' AND (d.location = 'HQ' OR m.title = 'Director')";

        // 添加示例7，测试占位符的解析
        String sql7 = "SELECT u.id, u.name, u.email FROM users u " +
                      "WHERE u.status = 'active' " +
                      "AND u.name = #{userName} " +
                      "AND u.department_id = :departmentId " +
                      "AND u.created_at > #{startDate}";

        // 修改示例8，确保明确包含supplier_id和region字段
        String sql8 = "SELECT p.product_id, p.name, p.price, c.category_name, s.supplier_name " +
                      "FROM products p " +
                      "JOIN categories c ON p.category_id = c.category_id " +
                      "JOIN suppliers s ON p.supplier_id = s.supplier_id " +
                      "WHERE p.status = 'active' " +
                      "AND p.price BETWEEN #{minPrice} AND #{maxPrice} " +
                      "AND p.category_id IN (:categoryIds) " +
                      "AND s.region = #{region}";

        // 修改示例9，确保子查询中的表和字段关系更加明确
        String sql9 = "SELECT d.department_id, d.name, d.location, " +
                      "(SELECT COUNT(e.employee_id) FROM employees e WHERE e.department_id = d.department_id) as employee_count, " +
                      "(SELECT AVG(e.salary) FROM employees e WHERE e.department_id = d.department_id) as avg_salary " +
                      "FROM departments d " +
                      "WHERE d.active = 1 " +
                      "AND EXISTS (SELECT 1 FROM projects p WHERE p.department_id = d.department_id AND p.status = 'active') " +
                      "ORDER BY employee_count DESC";

        // 解析SQL
        System.out.println("示例1:");
        Map<String, Set<String>> result1 = SqlParserUtil.extractTableAndColumns(sql1);
        System.out.println(SqlParserUtil.formatResult(result1));

        System.out.println("示例2:");
        SqlParserUtil.SqlParseResult result2 = SqlParserUtil.parse(sql2);
        System.out.println(result2);

        System.out.println("示例3:");
        Map<String, Set<String>> result3 = SqlParserUtil.extractTableAndColumns(sql3);
        System.out.println(SqlParserUtil.formatResult(result3));

        System.out.println("示例4:");
        SqlParserUtil.SqlParseResult result4 = SqlParserUtil.parse(sql4);
        System.out.println(result4);

        System.out.println("示例5 (多表JOIN):");
        Map<String, Set<String>> result5 = SqlParserUtil.extractTableAndColumns(sql5);
        System.out.println(SqlParserUtil.formatResult(result5));

        System.out.println("示例6 (自连接，包含location和title字段):");
        SqlParserUtil.SqlParseResult result6 = SqlParserUtil.parse(sql6);
        System.out.println(result6);

        System.out.println("示例7 (占位符):");
        Map<String, Set<String>> result7 = SqlParserUtil.extractTableAndColumns(sql7);
        System.out.println(SqlParserUtil.formatResult(result7));

        System.out.println("示例8 (IN条件和多种占位符):");
        SqlParserUtil.SqlParseResult result8 = SqlParserUtil.parse(sql8);
        System.out.println(result8);

        System.out.println("示例9 (复杂子查询):");
        Map<String, Set<String>> result9 = SqlParserUtil.extractTableAndColumns(sql9);
        System.out.println(SqlParserUtil.formatResult(result9));
    }
}

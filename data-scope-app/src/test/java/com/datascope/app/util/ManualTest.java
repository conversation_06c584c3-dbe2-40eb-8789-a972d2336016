package com.datascope.app.util;

/**
 * 手动测试类，用于验证SqlVariableRemover的功能
 */
public class ManualTest {

    public static void main(String[] args) {
        // 测试testRemoveVariables_NestedConditions
        testNestedConditions();

        // 测试testRemoveVariables_ThirdExample
        testThirdExample();

        // 测试testRemoveVariables_ten
        testTenExample();

        // 测试testRemoveVariables_eleven
        testElevenExample();
    }

    private static void testNestedConditions() {
        System.out.println("======== 测试 NestedConditions ========");

        String sql = "SELECT * FROM products\n" +
                "WHERE category_id IN (SELECT id FROM categories WHERE name = #{categoryName})\n" +
                "AND price BETWEEN #{minPrice} AND #{maxPrice}\n" +
                "AND status = 'available'\n" +
                "ORDER BY created_at DESC";

        String expected = "SELECT * FROM products\n" +
                "WHERE status = 'available'\n" +
                "ORDER BY created_at DESC";

        // 执行变量移除
        String result = SqlVariableRemover.removeVariables(sql);

        System.out.println("原始SQL:");
        System.out.println(sql);

        System.out.println("\n处理后SQL:");
        System.out.println(result);

        System.out.println("\n期望SQL:");
        System.out.println(expected);

        System.out.println("\n测试结果:");
        // 标准化字符串，去除换行符和多余空格
        String normalizedResult = normalizeString(result);
        String normalizedExpected = normalizeString(expected);

        if (normalizedExpected.equals(normalizedResult)) {
            System.out.println("测试通过!");
        } else {
            System.out.println("测试失败! 实际结果与期望不符");
            System.out.println("标准化后的实际结果: " + normalizedResult);
            System.out.println("标准化后的期望结果: " + normalizedExpected);
        }
    }

    private static void testThirdExample() {
        System.out.println("\n======== 测试 ThirdExample ========");

        String sql = "SELECT * \n" +
                "FROM test_query_users \n" +
                "WHERE (#{userId} IS NULL OR user_id LIKE CONCAT('%', #{userId}, '%'))\n" +
                "AND register_time >= #{startDate}\n" +
                "AND register_time <= #{endDate}\n" +
                "ORDER BY register_time DESC";

        String expected = "SELECT * FROM test_query_users ORDER BY register_time DESC";

        // 执行变量移除
        String result = SqlVariableRemover.removeVariables(sql);

        System.out.println("原始SQL:");
        System.out.println(sql);

        System.out.println("\n处理后SQL:");
        System.out.println(result);

        System.out.println("\n期望SQL:");
        System.out.println(expected);

        System.out.println("\n测试结果:");
        // 标准化字符串，去除换行符和多余空格
        String normalizedResult = normalizeString(result);
        String normalizedExpected = normalizeString(expected);

        if (normalizedExpected.equals(normalizedResult)) {
            System.out.println("测试通过!");
        } else {
            System.out.println("测试失败! 实际结果与期望不符");
            System.out.println("标准化后的实际结果: " + normalizedResult);
            System.out.println("标准化后的期望结果: " + normalizedExpected);
        }
    }

    private static void testTenExample() {
        System.out.println("\n======== 测试 TenExample ========");

        String sql = "SELECT * \n" +
            "FROM test_query_users_2 \n" +
            "WHERE \n" +
            "(#{userId} IS NULL OR user_id LIKE CONCAT('%', #{userId}, '%'))\n" +
            "AND (#{userName} IS NULL OR username LIKE CONCAT('%', #{userName}, '%'))\n" +
            "AND (#{gender} IS NULL OR gender in(#{gender}))\n" +
            "AND (#{startAge} IS NULL OR age >= #{startAge}) \n" +
            "AND (#{endAge} IS NULL OR age <= #{endAge}) \n" +
            "AND  (#{startDate} IS NULL OR register_time >= #{startDate})\n" +
            "AND  (#{endDate} IS NULL OR  register_time <= #{endDate})";

        String expected = "SELECT * FROM test_query_users_2";

        // 执行变量移除
        String result = SqlVariableRemover.removeVariables(sql);

        System.out.println("原始SQL:");
        System.out.println(sql);

        System.out.println("\n处理后SQL:");
        System.out.println(result);

        System.out.println("\n期望SQL:");
        System.out.println(expected);

        System.out.println("\n测试结果:");
        // 标准化字符串，去除换行符和多余空格
        String normalizedResult = normalizeString(result);
        String normalizedExpected = normalizeString(expected);

        if (normalizedExpected.equals(normalizedResult)) {
            System.out.println("测试通过!");
        } else {
            System.out.println("测试失败! 实际结果与期望不符");
            System.out.println("标准化后的实际结果: " + normalizedResult);
            System.out.println("标准化后的期望结果: " + normalizedExpected);
        }
    }

    private static void testElevenExample() {
        System.out.println("\n======== 测试 ElevenExample ========");

        String sql = "select *\n" +
                "from datasources\n" +
                "where name like '%#{data}%' and host=#{host} and ip between #{startDate} and #{endDate}";

        String expected = "select * from datasources";

        // 执行变量移除
        String result = SqlVariableRemover.removeVariables(sql);

        System.out.println("原始SQL:");
        System.out.println(sql);

        System.out.println("\n处理后SQL:");
        System.out.println(result);

        System.out.println("\n期望SQL:");
        System.out.println(expected);

        System.out.println("\n测试结果:");
        // 标准化字符串，去除换行符和多余空格
        String normalizedResult = normalizeString(result);
        String normalizedExpected = normalizeString(expected);

        if (normalizedExpected.equals(normalizedResult)) {
            System.out.println("测试通过!");
        } else {
            System.out.println("测试失败! 实际结果与期望不符");
            System.out.println("标准化后的实际结果: " + normalizedResult);
            System.out.println("标准化后的期望结果: " + normalizedExpected);
        }
    }

    /**
     * 标准化字符串，去除换行符和多余空格
     */
    private static String normalizeString(String input) {
        if (input == null) {
            return "";
        }
        // 将所有换行符替换为空格
        String result = input.replaceAll("\\n", " ");
        // 将多个空格替换为单个空格
        result = result.replaceAll("\\s+", " ");
        // 修剪字符串两端的空格
        return result.trim();
    }
}

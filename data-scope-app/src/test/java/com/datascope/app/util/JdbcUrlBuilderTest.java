package com.datascope.app.util;

import com.datascope.app.entity.Datasource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JDBC URL构建工具类测试
 */
public class JdbcUrlBuilderTest {

    @Test
    @DisplayName("测试MySQL JDBC URL构建")
    public void testBuildMySQLJdbcUrl() {
        Datasource datasource = new Datasource();
        datasource.setType("mysql");
        datasource.setHost("localhost");
        datasource.setPort(3306);
        datasource.setDatabaseName("test_db");

        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);
        assertEquals("************************************************************************************************", url);
    }

    @Test
    @DisplayName("测试PostgreSQL JDBC URL构建")
    public void testBuildPostgreSQLJdbcUrl() {
        Datasource datasource = new Datasource();
        datasource.setType("postgresql");
        datasource.setHost("localhost");
        datasource.setPort(5432);
        datasource.setDatabaseName("test_db");

        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);
        assertEquals("****************************************", url);
    }

    @Test
    @DisplayName("测试Oracle JDBC URL构建")
    public void testBuildOracleJdbcUrl() {
        Datasource datasource = new Datasource();
        datasource.setType("oracle");
        datasource.setHost("localhost");
        datasource.setPort(1521);
        datasource.setDatabaseName("orcl");

        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);
        assertEquals("*************************************", url);
    }

    @Test
    @DisplayName("测试SQL Server JDBC URL构建")
    public void testBuildSqlServerJdbcUrl() {
        Datasource datasource = new Datasource();
        datasource.setType("sqlserver");
        datasource.setHost("localhost");
        datasource.setPort(1433);
        datasource.setDatabaseName("test_db");

        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);
        assertEquals("****************************************************", url);
    }

    @Test
    @DisplayName("测试DB2 JDBC URL构建 - 不带Schema")
    public void testBuildDB2JdbcUrlWithoutSchema() {
        Datasource datasource = new Datasource();
        datasource.setType("db2");
        datasource.setHost("localhost");
        datasource.setPort(50000);
        datasource.setDatabaseName("test_db");

        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);
        assertEquals("**********************************", url);
    }

    @Test
    @DisplayName("测试DB2 JDBC URL构建 - 带Schema")
    public void testBuildDB2JdbcUrlWithSchema() {
        Datasource datasource = new Datasource();
        datasource.setType("db2");
        datasource.setHost("localhost");
        datasource.setPort(50000);
        datasource.setDatabaseName("test_db");
        datasource.setSchema("TEST_SCHEMA");

        String url = JdbcUrlBuilder.buildJdbcUrl(datasource);
        assertEquals("**********************************:currentSchema=TEST_SCHEMA;", url);
    }

    @Test
    @DisplayName("测试不支持的数据库类型")
    public void testUnsupportedDatabaseType() {
        Datasource datasource = new Datasource();
        datasource.setType("unsupported");
        datasource.setHost("localhost");
        datasource.setPort(1234);
        datasource.setDatabaseName("test_db");

        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            JdbcUrlBuilder.buildJdbcUrl(datasource);
        });

        assertTrue(exception.getMessage().contains("不支持的数据库类型"));
    }

    @Test
    @DisplayName("测试空数据源")
    public void testNullDatasource() {
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            JdbcUrlBuilder.buildJdbcUrl(null);
        });

        assertTrue(exception.getMessage().contains("数据源不能为空"));
    }

    @Test
    @DisplayName("测试获取MySQL驱动类名")
    public void testGetMySQLDriverClassName() {
        String driverClassName = JdbcUrlBuilder.getDriverClassName("mysql");
        assertEquals("com.mysql.cj.jdbc.Driver", driverClassName);
    }

    @Test
    @DisplayName("测试获取PostgreSQL驱动类名")
    public void testGetPostgreSQLDriverClassName() {
        String driverClassName = JdbcUrlBuilder.getDriverClassName("postgresql");
        assertEquals("org.postgresql.Driver", driverClassName);
    }

    @Test
    @DisplayName("测试获取Oracle驱动类名")
    public void testGetOracleDriverClassName() {
        String driverClassName = JdbcUrlBuilder.getDriverClassName("oracle");
        assertEquals("oracle.jdbc.OracleDriver", driverClassName);
    }

    @Test
    @DisplayName("测试获取SQL Server驱动类名")
    public void testGetSqlServerDriverClassName() {
        String driverClassName = JdbcUrlBuilder.getDriverClassName("sqlserver");
        assertEquals("com.microsoft.sqlserver.jdbc.SQLServerDriver", driverClassName);
    }

    @Test
    @DisplayName("测试获取DB2驱动类名")
    public void testGetDB2DriverClassName() {
        String driverClassName = JdbcUrlBuilder.getDriverClassName("db2");
        assertEquals("com.ibm.db2.jcc.DB2Driver", driverClassName);
    }

    @Test
    @DisplayName("测试获取不支持的数据库类型的驱动类名")
    public void testGetUnsupportedDriverClassName() {
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            JdbcUrlBuilder.getDriverClassName("unsupported");
        });

        assertTrue(exception.getMessage().contains("不支持的数据库类型"));
    }

    @Test
    @DisplayName("测试获取空数据库类型的驱动类名")
    public void testGetNullDriverClassName() {
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            JdbcUrlBuilder.getDriverClassName(null);
        });

        assertTrue(exception.getMessage().contains("数据库类型不能为空"));
    }
}

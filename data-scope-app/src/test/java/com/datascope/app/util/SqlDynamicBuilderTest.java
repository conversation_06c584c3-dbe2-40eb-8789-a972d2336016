package com.datascope.app.util;

import com.datascope.app.model.FieldRule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SqlDynamicBuilder功能演示类
 * 通过具体示例展示如何使用SQL动态构建工具
 */
public class SqlDynamicBuilderTest {

    /**
     * 主测试方法，运行所有测试
     */
    public static void main(String[] args) {
        // 测试示例1：简单查询
        testExample1();

        // 测试示例2：带有已有WHERE条件的查询
        testExample2();

        // 测试示例3：多表查询，带表别名
        testExample3();

        // 测试示例4：多表查询，使用表名而不是表别名
        testExample4();

        // 测试示例5：多表查询，SQL中没有表别名
        testExample5();

        // 测试多选功能
        testMultiSelectCondition();

        // 测试必填字段
        testRequiredField();
        testRequiredFieldEmptyString();

        // 测试替换已有条件
        testReplaceExistingCondition();

        // 测试替换带表别名的已有条件
        testReplaceTableAliasExistingCondition();

        // 测试没有传值的字段不拼接
        testEmptyFieldValue();

        // 测试传空字符串的字段不拼接
        testEmptyStringFieldValue();

        // 测试保留已有条件
        testReserveExistingCondition();

        // 测试保留多个已有条件
        testReserveMultipleExistingConditions();

        // 测试替换已有的日期条件
        testReplaceDateCondition();

        // 大于两张表的联查
        testExample8();
    }

    /**
     * 示例1：简单查询
     * 基础SQL: select * from queries;
     * 字段规则：
     * 1. 字段name: 必填，支持模糊查询
     * 2. 字段created_at: 表单类型是日期选择器
     * 参数：{"name":"我们", "created_at":"2025-02-09"}
     * 预期SQL: select * from queries where name like concat('%', '我们', '%') and created_at = '2025-02-09';
     */
    private static void testExample1() {
        String baseSql = "select * from queries";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, true, false));
        fieldRules.add(new FieldRule("created_at", "date", false, false, false));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "我们");
        queryParams.put("created_at", "2025-02-09");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("示例1 - 简单查询:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries WHERE name LIKE CONCAT('%', '我们', '%') AND created_at = '2025-02-09'");
        System.out.println();
    }

    /**
     * 示例2：带有已有WHERE条件的查询
     * 基础SQL: select * from queries where status = 'publish' order by created_at desc;
     * 字段规则：
     * 1. 字段name: 必填，支持模糊查询
     * 2. 字段created_at: 表单类型是日期选择区间
     * 参数：{"name":"我们", "created_at":"2025-02-09 ~ 2025-02-11"}
     * 预期SQL: select * from queries where status = 'publish' and name like concat('%', '我们', '%')
     *         and created_at >= '2025-02-09' and created_at <= '2025-02-11' order by created_at desc;
     */
    private static void testExample2() {
        String baseSql = "select * from queries where status = 'publish' order by created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, true, false));
        fieldRules.add(new FieldRule("created_at", "date-range", false, false, false));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "我们");
        queryParams.put("created_at", "2025-02-09,2025-02-11");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("示例2 - 带有已有WHERE条件的查询:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries where status = 'publish' AND name LIKE CONCAT('%', '我们', '%') " +
                "AND created_at >= '2025-02-09' AND created_at <= '2025-02-11' order by created_at desc");
        System.out.println();
    }

    /**
     * 示例3：多表查询，带表别名
     * 基础SQL: select q.*, u.username from queries q left join users u on q.created_by = u.id where q.is_public = 1;
     * 字段规则：
     * 1. 字段name: 表别名q，必填，支持模糊查询
     * 2. 字段username: 表别名u，支持模糊查询，非必填
     * 参数：{"name":"报表", "username":"admin"}
     * 预期SQL: select q.*, u.username from queries q left join users u on q.created_by = u.id
     *         where q.is_public = 1 and q.name like concat('%', '报表', '%') and u.username like concat('%', 'admin', '%');
     */
    private static void testExample3() {
        String baseSql = "select q.*, u.username from queries q left join users u on q.created_by = u.id where q.is_public = 1";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, true, false, "q"));
        fieldRules.add(new FieldRule("username", "text", true, false, false, "u"));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "报表");
        queryParams.put("username", "admin");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("示例3 - 多表查询，带表别名:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select q.*, u.username from queries q left join users u on q.created_by = u.id " +
                "where q.is_public = 1 AND q.name LIKE CONCAT('%', '报表', '%') AND u.username LIKE CONCAT('%', 'admin', '%')");
        System.out.println();
    }

    /**
     * 示例4：多表查询，使用表名而不是表别名
     * 基础SQL: select q.*, u.username from queries q left join users u on q.created_by = u.id where q.is_public = 1;
     * 字段规则：
     * 1. 字段name: 表名queries，必填，支持模糊查询
     * 2. 字段username: 表名users，支持模糊查询，非必填
     * 参数：{"name":"报表", "username":"admin"}
     * 预期SQL: select q.*, u.username from queries q left join users u on q.created_by = u.id
     *         where q.is_public = 1 and q.name like concat('%', '报表', '%') and u.username like concat('%', 'admin', '%');
     */
    private static void testExample4() {
        String baseSql = "select q.*, u.username from queries q left join users u on q.created_by = u.id where q.is_public = 1";

        // 创建字段规则 - 使用表名而不是表别名
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, true, false, "queries", null));
        fieldRules.add(new FieldRule("username", "text", true, false, false, "users", null));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "报表");
        queryParams.put("username", "admin");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("示例4 - 多表查询，使用表名而不是表别名:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select q.*, u.username from queries q left join users u on q.created_by = u.id " +
                "where q.is_public = 1 AND q.name LIKE CONCAT('%', '报表', '%') AND u.username LIKE CONCAT('%', 'admin', '%')");
        System.out.println();
    }

    /**
     * 测试SQL中没有表别名的情况
     */
    public static void testExample5() {
        System.out.println("示例5 - 多表查询，SQL中没有表别名:");

        // 基础SQL - 没有使用表别名
        String baseSql = "select queries.*, users.username from queries left join users on queries.created_by = users.id where queries.is_public = 1";

        // 字段规则 - 手动使用完整表名.字段格式
        List<FieldRule> fieldRules = new ArrayList<>();

        // 使用直接的表名.字段形式，而不依赖解析逻辑
        FieldRule nameRule = new FieldRule("name", "text", true, false, false);
        nameRule.setTableName("queries");

        FieldRule usernameRule = new FieldRule("username", "text", true, false, false);
        usernameRule.setTableName("users");

        fieldRules.add(nameRule);
        fieldRules.add(usernameRule);

        // 查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("name", "报表");
        params.put("username", "admin");

        // 打印字段规则详情，帮助调试
        System.out.println("字段规则:");
        for (FieldRule rule : fieldRules) {
            System.out.println("  字段: " + rule.getFieldName() +
                               ", 表名: " + rule.getTableName() +
                               ", 表别名: " + rule.getTableAlias() +
                               ", 完整字段名: " + rule.getFullFieldName());
        }

        // 构建SQL
        String sql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, params);

        // 打印结果
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + sql);
        System.out.println("预期SQL: select queries.*, users.username from queries left join users on queries.created_by = users.id where queries.is_public = 1 AND queries.name LIKE CONCAT('%', '报表', '%') AND users.username LIKE CONCAT('%', 'admin', '%')");
        System.out.println();
    }

    /**
     * 测试多选功能
     * 基础SQL: select * from queries;
     * 字段规则：
     * 1. 字段status: 支持多选，非必填
     * 参数：{"status":"PUBLISHED,DRAFT"}
     * 预期SQL: select * from queries where status in ('PUBLISHED', 'DRAFT');
     */
    private static void testMultiSelectCondition() {
        String baseSql = "select * from queries";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("status", "text", false, false, true));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("status", "PUBLISHED,DRAFT");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("多选功能测试:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries WHERE status IN ('PUBLISHED', 'DRAFT')");
        System.out.println();
    }

    /**
     * 测试必填字段
     * 如果必填字段的值为空，应抛出异常
     */
    private static void testRequiredField() {
        String baseSql = "select * from queries";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, true, false));

        // 创建参数Map - 不包含必填字段
        Map<String, Object> queryParams = new HashMap<>();

        try {
            // 构建SQL - 应抛出异常
            SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);
            System.out.println("必填字段测试: 失败 - 未抛出预期异常");
        } catch (IllegalArgumentException e) {
            System.out.println("必填字段测试: 成功 - 抛出异常 - " + e.getMessage());
        }

        // 测试空字符串
        queryParams.put("name", "");

        try {
            // 构建SQL - 应抛出异常
            SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);
            System.out.println("必填字段测试(空字符串): 失败 - 未抛出预期异常");
        } catch (IllegalArgumentException e) {
            System.out.println("必填字段测试(空字符串): 成功 - 抛出异常 - " + e.getMessage());
        }
    }

    /**
     * 测试必填字段(传入空字符串)
     */
    private static void testRequiredFieldEmptyString() {
        String baseSql = "select * from queries";

        // 创建字段规则 - name为必填
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, true, false));

        // 创建参数Map - name为空字符串
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "");

        try {
            // 尝试构建SQL - 应该会抛出异常
            SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);
            System.out.println("测试必填字段(空字符串): 失败 - 没有抛出异常");
        } catch (IllegalArgumentException e) {
            System.out.println("测试必填字段(空字符串): 成功 - 抛出异常 - " + e.getMessage());
        }
    }

    /**
     * 测试替换已有条件
     * 基础SQL: select * from queries where status = 'publish' order by created_at desc;
     * 字段规则：
     * 1. 字段status: 支持多选，非必填
     * 参数：{"status":"DRAFT,REVIEW"}
     * 预期SQL: select * from queries where status IN ('DRAFT', 'REVIEW') order by created_at desc;
     */
    private static void testReplaceExistingCondition() {
        String baseSql = "select * from queries where status = 'publish' order by created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("status", "select", false, false, true));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("status", "DRAFT,REVIEW");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("测试替换已有条件:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries WHERE status IN ('DRAFT', 'REVIEW') order by created_at desc");
        System.out.println();

        // 测试带表别名的条件替换
        String baseSqlWithAlias = "select q.* from queries q where q.status = 'publish' order by q.created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRulesWithAlias = new ArrayList<>();
        fieldRulesWithAlias.add(new FieldRule("status", "select", false, false, true, "q"));

        // 构建SQL
        String resultSqlWithAlias = SqlDynamicBuilder.buildSql(baseSqlWithAlias, fieldRulesWithAlias, queryParams);

        // 打印结果
        System.out.println("测试替换带表别名的已有条件:");
        System.out.println("基础SQL: " + baseSqlWithAlias);
        System.out.println("生成SQL: " + resultSqlWithAlias);
        System.out.println("预期SQL: select q.* from queries q WHERE q.status IN ('DRAFT', 'REVIEW') order by q.created_at desc");
        System.out.println();
    }

    /**
     * 测试替换带表别名的已有条件
     */
    private static void testReplaceTableAliasExistingCondition() {
        String baseSql = "select q.* from queries q where q.status = 'publish' order by q.created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("status", "select", false, false, true, "q"));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("status", "DRAFT,REVIEW");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("测试替换带表别名的已有条件:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select q.* from queries q WHERE q.status IN ('DRAFT', 'REVIEW') order by q.created_at desc");
        System.out.println();
    }

    /**
     * 测试没有传值的字段不拼接
     */
    private static void testEmptyFieldValue() {
        String baseSql = "select * from queries";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", false, false, false));
        fieldRules.add(new FieldRule("status", "select", false, false, true));

        // 创建参数Map - 只传name，不传status
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "报表");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("测试没有传值的字段不拼接:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("参数: " + queryParams);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries WHERE name LIKE CONCAT('%', '报表', '%')");
        System.out.println();
    }

    /**
     * 测试传空字符串的字段不拼接
     */
    private static void testEmptyStringFieldValue() {
        String baseSql = "select * from queries";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("name", "text", true, false, false));
        fieldRules.add(new FieldRule("status", "select", false, false, true));

        // 创建参数Map - 只传name，不传status
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "报表");
        queryParams.put("status", ""); // 空字符串

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("测试传空字符串的字段不拼接:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("参数: " + queryParams);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries WHERE name LIKE CONCAT('%', '报表', '%')");
        System.out.println();
    }

    /**
     * 测试保留已有条件
     */
    private static void testReserveExistingCondition() {
        String baseSql = "select * from queries where status = 'publish' order by created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("status", "select", false, false, true));
        fieldRules.add(new FieldRule("name", "text", true, false, false));

        // 创建参数Map - 只传name，不传status
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", "报表");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("测试保留基础SQL中已有的字段条件:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("参数: " + queryParams);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries WHERE status = 'publish' AND name LIKE CONCAT('%', '报表', '%') order by created_at desc");
        System.out.println();
    }

    /**
     * 测试保留多个已有条件
     */
    private static void testReserveMultipleExistingConditions() {
        String baseSql = "select * from queries where status in ('publish') AND is_deleted = 0 and name like concat('%','我们') order by created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("status", "text", false, false, true));
        fieldRules.add(new FieldRule("name", "text", true, false, false));
        fieldRules.add(new FieldRule("order", "text", true, false, false));

        // 创建参数Map - 只传name，不传status和is_deleted
        Map<String, Object> paramsMulti = new HashMap<>();
        paramsMulti.put("name", "报表");
        paramsMulti.put("order", "订单");

        // 构建SQL
        String resultSqlMulti = SqlDynamicBuilder.buildSql(baseSql, fieldRules, paramsMulti);

        // 打印结果
        System.out.println("测试保留基础SQL中多个已有字段条件:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("参数: " + paramsMulti);
        System.out.println("生成SQL: " + resultSqlMulti);
        System.out.println("预期SQL: select * from queries WHERE status = 'publish' AND is_deleted = 0 AND name LIKE CONCAT('%', '报表', '%') order by created_at desc");
        System.out.println();
    }

    /**
     * 测试替换已有的日期条件
     */
    private static void testReplaceDateCondition() {
        String baseSql = "select * from queries where status in ('publish') and created_at > '2023-09-12' order by created_at desc";

        // 创建字段规则
        List<FieldRule> fieldRules = new ArrayList<>();
        fieldRules.add(new FieldRule("created_at", "date-range", false, false, false));

        // 创建参数Map
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("created_at", "2025-05-06,2025-08-23");

        // 构建SQL
        String resultSql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, queryParams);

        // 打印结果
        System.out.println("测试替换已有日期条件:");
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + resultSql);
        System.out.println("预期SQL: select * from queries where status = 'publish' and created_at = '2025-05-06' order by created_at desc");
        System.out.println();
    }

    /**
     * 测试SQL中没有表别名的情况
     */
    public static void testExample8() {
        System.out.println("示例8 - 多表查询，表里有相同字段的");

        // 基础SQL - 没有使用表别名
        String baseSql = "select q.*, u.username, u.age from queries q left join users u on q.created_by = u.id " +
                "left join student stu on u.id = stu.id where q.is_public = 1 and u.age = 20";

        List<FieldRule> fieldRules = new ArrayList<>();

        // 使用直接的表名.字段形式，而不依赖解析逻辑
        FieldRule nameRule = new FieldRule("name", "text", true, false, false);
        nameRule.setTableName("queries");

        FieldRule usernameRule = new FieldRule("username", "text", true, false, false);
        usernameRule.setTableName("users");

        FieldRule nameRule2 = new FieldRule("course", "text", false, false, false);
        nameRule2.setTableName("student");

        FieldRule nameRule3 = new FieldRule("name", "text", true, false, false);
        nameRule3.setTableName("users");

        fieldRules.add(nameRule);
        fieldRules.add(usernameRule);
        fieldRules.add(nameRule2);
        fieldRules.add(nameRule3);

        // 查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("name", "报表");
        params.put("username", "admin");
        params.put("course", "数学");

        // 构建SQL
        String sql = SqlDynamicBuilder.buildSql(baseSql, fieldRules, params);

        // 打印结果
        System.out.println("基础SQL: " + baseSql);
        System.out.println("生成SQL: " + sql);
        System.out.println("预期SQL: select q.*, u.username, u.age from queries q left join users u on q.created_by = u.id left join student stu on u.id = stu.id WHERE q.is_public = 1 and u.age = 20 AND q.name LIKE CONCAT('%', '报表', '%') AND u.username LIKE CONCAT('%', 'admin', '%') AND stu.course = '数学' AND u.name LIKE CONCAT('%', '报表', '%')");
        System.out.println();
    }


}

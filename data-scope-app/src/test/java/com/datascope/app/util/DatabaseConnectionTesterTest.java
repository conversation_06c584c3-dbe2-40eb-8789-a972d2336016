package com.datascope.app.util;

import com.datascope.app.dto.datasource.TestConnectionResultDTO;
import com.datascope.app.entity.Datasource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库连接测试器单元测试
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class DatabaseConnectionTesterTest {

    @InjectMocks
    private DatabaseConnectionTester connectionTester;

    @Test
    @DisplayName("测试无效的数据源连接")
    public void testInvalidConnection() {
        // 创建一个包含错误连接信息的数据源
        Datasource invalidDatasource = Datasource.builder()
                .id("test-id")
                .name("测试数据源")
                .type("mysql")
                .host("non-existent-host")
                .port(3306)
                .databaseName("non_existent_db")
                .username("invalid_user")
                .password("invalid_password")
                .build();

        // 测试连接
        TestConnectionResultDTO result = connectionTester.testConnection(invalidDatasource);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        assertTrue(result.getMessage().contains("数据库连接失败"));
    }

    @Test
    @DisplayName("测试不支持的数据库类型")
    public void testUnsupportedDatabaseType() {
        // 创建一个包含不支持的数据库类型的数据源
        Datasource unsupportedDatasource = Datasource.builder()
                .id("test-id")
                .name("测试数据源")
                .type("unsupported-db")
                .host("localhost")
                .port(3306)
                .databaseName("test_db")
                .username("user")
                .password("password")
                .build();

        // 测试连接
        TestConnectionResultDTO result = connectionTester.testConnection(unsupportedDatasource);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        assertTrue(result.getMessage().contains("数据库驱动加载失败"));
        assertTrue(result.getMessage().contains("不支持的数据库类型"));
    }

    @Test
    @DisplayName("Scenario 1: 测试有效的 MySQL 数据源连接 (预期失败，需要真实或模拟环境)")
    public void testValidMySQLConnection() {
        // Arrange: 配置一个理论上有效的 MySQL 连接信息
        // 注意：此测试的成功需要一个可访问的 MySQL 实例或模拟环境
        Datasource validDatasource = Datasource.builder()
            .id("valid-mysql")
            .name("Valid MySQL")
            .type("mysql") // 确保类型与 DatabaseConnectionTester 支持的一致
            .host("localhost") // 或测试数据库主机
            .port(3306)      // 或测试数据库端口
            .databaseName("testdb") // 或测试数据库名称
            .username("testuser") // 或测试数据库用户
            .password("password123") // 或测试数据库密码
            .build();

        // Act
        TestConnectionResultDTO result = connectionTester.testConnection(validDatasource);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess(), "预期连接成功，但失败。Message: " + result.getMessage());
        assertEquals("连接成功", result.getMessage()); // 假设成功消息是这个
    }

    @Test
    @DisplayName("Scenario 2: 测试无效的 MySQL 数据源连接（密码错误）")
    public void testInvalidPasswordMySQLConnection() {
        // Arrange: 配置一个密码错误的 MySQL 连接信息
        Datasource invalidPassDatasource = Datasource.builder()
            .id("invalid-pass-mysql")
            .name("Invalid Pass MySQL")
            .type("mysql")
            .host("localhost") // 假设主机、端口、数据库、用户有效
            .port(3306)
            .databaseName("testdb")
            .username("testuser")
            .password("wrongPassword") // 错误的密码
            .build();

        // Act
        TestConnectionResultDTO result = connectionTester.testConnection(invalidPassDatasource);

        // Assert
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        // 具体的错误消息可能依赖于 JDBC 驱动和数据库本身
        assertTrue(result.getMessage().toLowerCase().contains("access denied") || result.getMessage().contains("用户名或密码错误"),
            "预期连接因密码错误失败，但实际消息不匹配: " + result.getMessage());
    }

    @Test
    @DisplayName("Scenario 3: 测试无效的 MySQL 数据源连接（主机不可达）")
    public void testUnreachableHostMySQLConnection() {
        // Arrange: 配置一个主机不可达的 MySQL 连接信息
        Datasource unreachableDatasource = Datasource.builder()
            .id("unreachable-mysql")
            .name("Unreachable MySQL")
            .type("mysql")
            .host("non.existent.host.datascope") // 一个明显无法解析的主机
            .port(3306)
            .databaseName("testdb")
            .username("testuser")
            .password("password123")
            .build();

        // Act
        TestConnectionResultDTO result = connectionTester.testConnection(unreachableDatasource);

        // Assert
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertNotNull(result.getMessage());
        // 错误消息可能包含 "Unknown host", "Connection timed out", "无法连接" 等
        assertTrue(result.getMessage().toLowerCase().contains("unknown host")
                || result.getMessage().toLowerCase().contains("timed out")
                || result.getMessage().toLowerCase().contains("communications link failure")
                || result.getMessage().contains("无法连接"),
            "预期连接因主机不可达失败，但实际消息不匹配: " + result.getMessage());
    }
}

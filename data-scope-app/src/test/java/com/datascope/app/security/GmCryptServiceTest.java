package com.datascope.app.security;

import com.yeepay.g3.utils.gmcrypt.utils.SMUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class GmCryptServiceTest {

    private static final String APP_ID_2 = "APP002";

    // 测试数据
    private static final String APP_ID_1 = "APP001";
    private static final String KEY_NODE_1 = "NODE001";
    private static final String KEY_NODE_2 = "NODE002";
    private static final String PLAIN_TEXT = "test plain text";
    private static final String ENCRYPTED_TEXT_1 = APP_ID_1 + "$" + KEY_NODE_1 + "$" + "encryptedData1";
    private static final String ENCRYPTED_TEXT_2 = APP_ID_2 + "$" + KEY_NODE_2 + "$" + "encryptedData2";
    private static final String INVALID_ENCRYPTED_TEXT = "invalid$format";
    private static final String MALFORMED_ENCRYPTED_TEXT = "MALFORMED$PREFIX$";
    private static final String KMS_ERROR_TEXT = "KMS$ERROR$text";
    @InjectMocks
    private GmEncryptionService gmCryptService;

    @Before
    public void setUp() {
        try (MockedStatic<SMUtils> mocked = mockStatic(SMUtils.class)) {
            // 模拟正常解密
            mocked.when(() -> SMUtils.decrypt(ENCRYPTED_TEXT_1)).thenReturn(PLAIN_TEXT);
            mocked.when(() -> SMUtils.decrypt(ENCRYPTED_TEXT_2)).thenReturn(PLAIN_TEXT);

            // 模拟异常情况
            mocked.when(() -> SMUtils.decrypt(INVALID_ENCRYPTED_TEXT))
                .thenThrow(new RuntimeException("Invalid format"));
            mocked.when(() -> SMUtils.decrypt(MALFORMED_ENCRYPTED_TEXT))
                .thenThrow(new RuntimeException("Decryption failed"));
            mocked.when(() -> SMUtils.decrypt(KMS_ERROR_TEXT))
                .thenThrow(new RuntimeException("KMS service unavailable"));
        }
    }

    @Test
    public void testDecryptWithValidAppId1() {
        String result = gmCryptService.decrypt(ENCRYPTED_TEXT_1);
        assertEquals(PLAIN_TEXT, result);
    }

    @Test
    public void testDecryptWithValidAppId2() {
        String result = gmCryptService.decrypt(ENCRYPTED_TEXT_2);
        assertEquals(PLAIN_TEXT, result);
    }

    @Test
    public void testDecryptWithInvalidFormat() {
        String result = gmCryptService.decrypt(INVALID_ENCRYPTED_TEXT);
        assertEquals(INVALID_ENCRYPTED_TEXT, result);
    }

    @Test
    public void testDecryptWithMalformedPrefix() {
        String result = gmCryptService.decrypt(MALFORMED_ENCRYPTED_TEXT);
        assertEquals(MALFORMED_ENCRYPTED_TEXT, result);
    }

    @Test
    public void testDecryptWithKMSError() {
        String result = gmCryptService.decrypt(KMS_ERROR_TEXT);
        assertEquals(KMS_ERROR_TEXT, result);
    }

    @Test
    public void testDecryptWithNullInput() {
        String result = gmCryptService.decrypt(null);
        assertNull(result);
    }

    @Test
    public void testDecryptWithEmptyInput() {
        String result = gmCryptService.decrypt("");
        assertEquals("", result);
    }

    @Test
    public void testIsEncryptedDataWithValidFormat() {
        assertTrue(gmCryptService.isEncryptedData(ENCRYPTED_TEXT_1));
    }

    @Test
    public void testIsEncryptedDataWithInvalidFormat() {
        assertFalse(gmCryptService.isEncryptedData(INVALID_ENCRYPTED_TEXT));
    }

    @Test
    public void testIsEncryptedDataWithNullInput() {
        assertFalse(gmCryptService.isEncryptedData(null));
    }

    @Test
    public void testIsEncryptedDataWithEmptyInput() {
        assertFalse(gmCryptService.isEncryptedData(""));
    }
}

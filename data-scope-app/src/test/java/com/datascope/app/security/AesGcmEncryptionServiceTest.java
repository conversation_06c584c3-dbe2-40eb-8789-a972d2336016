package com.datascope.app.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

class AesGcmEncryptionServiceTest {

    // A dummy 32-byte key for testing
    private final String TEST_KEY = "1234567890ABCDEF1234567890ABCDEF";
    private final String TEST_SALT = "TestSaltForAAD";
    private AesGcmEncryptionService encryptionService;

    @BeforeEach
    void setUp() {
        encryptionService = new AesGcmEncryptionService();
        // Use Spring's ReflectionTestUtils to inject values into private fields
        ReflectionTestUtils.setField(encryptionService, "aesKey", TEST_KEY);
        ReflectionTestUtils.setField(encryptionService, "salt", TEST_SALT);
    }

    @Test
    void shouldEncryptAndDecryptCorrectly() {
        String originalText = "MySecretPassword123!@#";

        // Encrypt
        String encryptedText = encryptionService.encrypt(originalText);

        // Assertions for encryption
        assertNotNull(encryptedText, "Encrypted text should not be null");
        assertNotEquals(originalText, encryptedText, "Encrypted text should differ from original");
        assertTrue(encryptedText.length() > originalText.length(), "Encrypted text should be longer due to IV and encoding");

        // Decrypt
        String decryptedText = encryptionService.decrypt(encryptedText);

        // Assertion for decryption
        assertEquals(originalText, decryptedText, "Decrypted text should match the original");
    }

    @Test
    void shouldHandleEmptyStrings() {
        String originalText = "";
        String encryptedText = encryptionService.encrypt(originalText);
        // Should return empty string as is
        assertEquals("", encryptedText, "Encrypting empty string should return empty string");

        String decryptedText = encryptionService.decrypt(encryptedText);
        assertEquals("", decryptedText, "Decrypting empty string should return empty string");
    }

    @Test
    void shouldHandleNullValues() {
        String originalText = null;
        String encryptedText = encryptionService.encrypt(originalText);
        // Should return null as is
        assertNull(encryptedText, "Encrypting null should return null");

        String decryptedText = encryptionService.decrypt(encryptedText);
        assertNull(decryptedText, "Decrypting null should return null");
    }

    @Test
    void decryptionShouldFailWithWrongKey() {
        String originalText = "PasswordToTestKeyMismatch";
        String encryptedText = encryptionService.encrypt(originalText);

        // Create a new service instance with a different key
        AesGcmEncryptionService wrongKeyService = new AesGcmEncryptionService();
        ReflectionTestUtils.setField(wrongKeyService, "aesKey", "DIFFERENT_KEY_1234567890ABCDEF123"); // Different key
        ReflectionTestUtils.setField(wrongKeyService, "salt", TEST_SALT);

        // Decryption should fail (AEADBadTagException likely, wrapped in RuntimeException)
        assertThrows(RuntimeException.class, () -> {
            wrongKeyService.decrypt(encryptedText);
        }, "Decryption should fail with the wrong key");
    }

    @Test
    void decryptionShouldFailWithWrongSalt() {
        String originalText = "PasswordToTestSaltMismatch";
        String encryptedText = encryptionService.encrypt(originalText);

        // Create a new service instance with a different salt
        AesGcmEncryptionService wrongSaltService = new AesGcmEncryptionService();
        ReflectionTestUtils.setField(wrongSaltService, "aesKey", TEST_KEY);
        ReflectionTestUtils.setField(wrongSaltService, "salt", "DifferentSalt"); // Different salt

        // Decryption should fail due to AAD mismatch
        assertThrows(RuntimeException.class, () -> {
            wrongSaltService.decrypt(encryptedText);
        }, "Decryption should fail with the wrong salt (AAD)");
    }

    @Test
    void encryptionShouldProduceDifferentCiphertextForSamePlaintext() {
        String plainText = "This should be encrypted differently each time";

        String encrypted1 = encryptionService.encrypt(plainText);
        String encrypted2 = encryptionService.encrypt(plainText);

        assertNotNull(encrypted1);
        assertNotNull(encrypted2);
        assertNotEquals(encrypted1, encrypted2, "Two encryptions of the same text should yield different results due to random IVs");

        // Verify both can be decrypted back to the original text
        assertEquals(plainText, encryptionService.decrypt(encrypted1));
        assertEquals(plainText, encryptionService.decrypt(encrypted2));
    }
}

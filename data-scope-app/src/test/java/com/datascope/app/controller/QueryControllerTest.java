package com.datascope.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.dto.query.*;
import com.datascope.app.service.QueryService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 查询控制器测试类
 */
public class QueryControllerTest {

    private MockMvc mockMvc;

    @Mock
    private QueryService queryService;

    @InjectMocks
    private QueryController queryController;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(queryController).build();
    }

    @Test
    public void testGetQueries() throws Exception {
        // 准备模拟数据
        Page<QueryDTO> page = new Page<>();
        page.setRecords(new ArrayList<>());
        page.setTotal(0);
        page.setSize(10);
        page.setCurrent(1);
        page.setPages(0);

        // 模拟服务调用
        when(queryService.getQueries(anyInt(), anyInt(), anyString(), anyString(), anyString(),
                                    anyString(), anyString(), anyString(), anyString(), anyBoolean()))
            .thenReturn(page);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/queries")
                .param("page", "1")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.page").value(1))
                .andExpect(jsonPath("$.data.size").value(10));
    }

    @Test
    public void testGetQuery() throws Exception {
        // 准备模拟数据
        QueryDTO queryDTO = new QueryDTO();
        queryDTO.setId("test-id");
        queryDTO.setName("测试查询");

        // 模拟服务调用
        when(queryService.getQuery(eq("test-id"))).thenReturn(queryDTO);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/queries/test-id")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value("test-id"))
                .andExpect(jsonPath("$.data.name").value("测试查询"));
    }

    @Test
    public void testCreateQuery() throws Exception {
        // 准备模拟数据
        QueryDTO queryDTO = new QueryDTO();
        queryDTO.setId("new-id");
        queryDTO.setName("新建查询");

        // 模拟服务调用
        when(queryService.createQuery(any(SaveQueryParams.class))).thenReturn(queryDTO);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/queries")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"name\":\"新建查询\",\"dataSourceId\":\"ds-1\",\"sql\":\"SELECT * FROM users\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value("new-id"))
                .andExpect(jsonPath("$.data.name").value("新建查询"));
    }

    @Test
    public void testExecuteQuery() throws Exception {
        // 准备模拟数据
        QueryResultDTO resultDTO = new QueryResultDTO();
        resultDTO.setQueryId("test-id");

        // 模拟服务调用
        when(queryService.executeQuery(eq("test-id"), any(ExecuteQueryParams.class))).thenReturn(resultDTO);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/queries/test-id/execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"parameters\":{},\"limit\":10,\"offset\":0}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.queryId").value("test-id"));
    }

    @Test
    public void testDeleteQuery() throws Exception {
        // 模拟服务调用
        when(queryService.deleteQuery(eq("test-id"))).thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(delete("/api/queries/test-id")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("查询已成功删除"));
    }

    @Test
    public void testGetQueryVersion() throws Exception {
        // 准备模拟数据
        QueryVersionDTO versionDTO = new QueryVersionDTO();
        versionDTO.setId("version-id");
        versionDTO.setQueryId("test-id");

        // 模拟服务调用
        when(queryService.getQueryVersion(eq("version-id"))).thenReturn(versionDTO);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/queries/versions/version-id")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value("version-id"))
                .andExpect(jsonPath("$.data.queryId").value("test-id"));
    }

    @Test
    @DisplayName("测试获取查询执行计划")
    public void testGetExecutionPlan() throws Exception {
        // 准备模拟数据
        String queryId = "237bea1967d04710876057b71c4e7822";

        List<Map<String, Object>> planDetails = new ArrayList<>();
        Map<String, Object> planDetail = new HashMap<>();
        planDetail.put("id", 1);
        planDetail.put("select_type", "SIMPLE");
        planDetail.put("table", "users");
        planDetail.put("type", "ALL");
        planDetail.put("possible_keys", null);
        planDetail.put("key", null);
        planDetail.put("key_len", null);
        planDetail.put("ref", null);
        planDetail.put("rows", 100);
        planDetail.put("Extra", "Using where");
        planDetails.add(planDetail);

        // 创建执行计划详情
        ExecutionPlanDTO.PlanDetails planDetailsObj = new ExecutionPlanDTO.PlanDetails();
        planDetailsObj.setTotalCost(100.0);
        planDetailsObj.setEstimatedRows(100L);

        List<ExecutionPlanDTO.PlanStep> steps = new ArrayList<>();
        ExecutionPlanDTO.PlanStep step = new ExecutionPlanDTO.PlanStep();
        step.setType("SIMPLE");
        step.setTable("users");
        step.setCondition("Using where");
        step.setCost(100.0);
        step.setRows(100L);
        steps.add(step);
        planDetailsObj.setSteps(steps);

        ExecutionPlanDTO executionPlanDTO = ExecutionPlanDTO.builder()
            .id("plan-id-123")
            .queryId(queryId)
            .createdAt("2025-05-20 20:00:00")
            .estimatedCost(100.0)
            .estimatedRows(100L)
            .planningTime(123L)
            .executionTime(null)
            .plan("id\tselect_type\ttable\ttype\tpossible_keys\tkey\tkey_len\tref\trows\tExtra\n1\tSIMPLE\tusers\tALL\tnull\tnull\tnull\tnull\t100\tUsing where\n")
            .planDetails(planDetailsObj)
            .build();

        // 模拟服务调用
        when(queryService.getExecutionPlan(anyString())).thenReturn(executionPlanDTO);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/queries/{id}/execution-plan", queryId))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.id").value("plan-id-123"))
            .andExpect(jsonPath("$.data.queryId").value(queryId))
            .andExpect(jsonPath("$.data.createdAt").value("2025-05-20 20:00:00"))
            .andExpect(jsonPath("$.data.estimatedCost").value(100.0))
            .andExpect(jsonPath("$.data.estimatedRows").value(100))
            .andExpect(jsonPath("$.data.planningTime").value(123))
            .andExpect(jsonPath("$.data.planDetails.totalCost").value(100.0))
            .andExpect(jsonPath("$.data.planDetails.steps[0].type").value("SIMPLE"))
            .andExpect(jsonPath("$.data.planDetails.steps[0].table").value("users"))
            .andExpect(jsonPath("$.data.planDetails.steps[0].condition").value("Using where"));
    }

    @Test
    @DisplayName("测试获取SQL执行计划（无需保存查询）")
    public void testGetExecutionPlanForSQL() throws Exception {
        // 准备模拟数据
        String dataSourceId = "ds-1";
        String sql = "SELECT * FROM users WHERE id > 100";

        List<Map<String, Object>> planDetails = new ArrayList<>();
        Map<String, Object> planDetail = new HashMap<>();
        planDetail.put("id", 1);
        planDetail.put("select_type", "SIMPLE");
        planDetail.put("table", "users");
        planDetail.put("type", "ALL");
        planDetail.put("rows", 100);
        planDetail.put("Extra", "Using where");
        planDetails.add(planDetail);

        // 创建执行计划详情
        ExecutionPlanDTO.PlanDetails planDetailsObj = new ExecutionPlanDTO.PlanDetails();
        planDetailsObj.setTotalCost(100.0);
        planDetailsObj.setEstimatedRows(100L);

        List<ExecutionPlanDTO.PlanStep> steps = new ArrayList<>();
        ExecutionPlanDTO.PlanStep step = new ExecutionPlanDTO.PlanStep();
        step.setType("SIMPLE");
        step.setTable("users");
        step.setCondition("Using where");
        step.setCost(100.0);
        step.setRows(100L);
        steps.add(step);
        planDetailsObj.setSteps(steps);

        ExecutionPlanDTO executionPlanDTO = ExecutionPlanDTO.builder()
            .id("plan-id-456")
            .queryId(null) // 未保存的查询没有ID
            .createdAt("2025-05-20 20:00:00")
            .estimatedCost(100.0)
            .estimatedRows(100L)
            .planningTime(123L)
            .executionTime(null)
            .plan("id\tselect_type\ttable\ttype\tpossible_keys\tkey\tkey_len\tref\trows\tExtra\n1\tSIMPLE\tusers\tALL\tnull\tnull\tnull\tnull\t100\tUsing where\n")
            .planDetails(planDetailsObj)
            .build();

        // 模拟服务调用
        when(queryService.getExecutionPlanForSQL(any(ExecutionPlanParams.class))).thenReturn(executionPlanDTO);

        // 构建请求参数
        ExecutionPlanParams params = new ExecutionPlanParams();
        params.setDataSourceId(dataSourceId);
        params.setSql(sql);

        // 将参数转换为JSON
        ObjectMapper objectMapper = new ObjectMapper();
        String requestJson = objectMapper.writeValueAsString(params);

        // 执行请求并验证结果
        mockMvc.perform(post("/api/queries/execution-plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.id").value("plan-id-456"))
            .andExpect(jsonPath("$.data.queryId").isEmpty())
            .andExpect(jsonPath("$.data.createdAt").value("2025-05-20 20:00:00"))
            .andExpect(jsonPath("$.data.estimatedCost").value(100.0))
            .andExpect(jsonPath("$.data.estimatedRows").value(100))
            .andExpect(jsonPath("$.data.planningTime").value(123))
            .andExpect(jsonPath("$.data.planDetails.totalCost").value(100.0))
            .andExpect(jsonPath("$.data.planDetails.steps[0].type").value("SIMPLE"))
            .andExpect(jsonPath("$.data.planDetails.steps[0].table").value("users"))
            .andExpect(jsonPath("$.data.planDetails.steps[0].condition").value("Using where"));
    }
}

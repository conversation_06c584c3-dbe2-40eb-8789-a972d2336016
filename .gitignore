# Compiled class files
*.class

# Log files
*.log
logs/
*.log.*

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE - IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

# IDE - Eclipse
.settings/
.classpath
.project
.factorypath
bin/

# IDE - VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Operating System
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
application-local.yml
application-local.properties
*.env
.env.*
!.env.example

# Docker
.docker/data/
docker-compose.override.yml

# Node (for UI development)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Testing
coverage/
.coverage/
htmlcov/
.tox/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover

# Security
*.pem
*.key
*.p12
*.jks
*.keystore
*.truststore

# Temporary files
*.swp
*.swo
*~
tmp/
temp/

# Build output
build/
dist/
out/

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar

# Spring Boot
spring-boot-*.pid

# Redis
*.rdb
dump.rdb

# Logs and databases
*.sqlite
*.db

# Generated files
generated/
.generated/
src/generated/

# Documentation
docs/_build/
site/

# Cache
.cache/
.npm/
.yarn/

# Other
*.bak
*.tmp
*.temp
.history/

.repomix/
.clinerules*
handoffs/

.qodo
/.cursor/
/.vscode/settings.json
.aider*

# Added by Claude Task Master
# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/

.qodo/
*/.idea/
*/.temp/
*/node_modules/
*/target/

.roo/
.ruru/

# Changelog

All notable changes to DataScope will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- WebUI组件库文档优化：
  - 为所有组件详情页添加统一的左侧导航菜单
  - 添加面包屑导航，增强用户在组件系统中的导航体验
  - 在每个组件页面添加当前位置指示器
  - 统一所有组件页面的页脚设计，改善整体一致性
  - 更新组件分类，包括基础组件、表单组件、容器组件、数据展示、反馈组件和动画效果
  - 更新了按钮、基础输入、卡片、数据表格和过渡动画等组件页面的布局
- 添加boss-data-scope2-new前端项目，基于webUI分支完整版本
- 配置boss-data-scope2-new使用端口8082，避免与现有boss-data-scope2冲突
- 保持原有的boss-data-scope2项目不变，使两个前端项目可同时运行
- 完整迁移UI设计文档(docs/design)到boss-data-scope2-new项目：
  - 基础组件(basic)：按钮、图标按钮、输入框和徽章
  - 表单组件(forms)：各类输入控件、选择器、日期选择器等
  - 容器组件(container)：卡片、面板和盒子
  - 数据展示(display)：数据表格、页头和操作按钮
  - 反馈组件(feedback)：警告、对话框、消息和通知
  - 动画效果(animations)：加载动画和过渡效果
  - 使用指南(guides)：设计原则、开发指南和Vue3迁移
  - 完整保留了所有组件的文档、示例和代码说明

### Changed

- 前端数据源服务改造：
  - 添加统一请求工具类 (utils/request.ts)
  - 重构数据源服务，使用统一请求工具
  - 更新 API 路径为 /api/v1/datasources
  - 优化错误处理和提示
  - 简化代码结构，提高可维护性
- 更新boss-data-scope2-new的README.md：
  - 更新技术栈版本信息，反映当前使用的库版本
  - 更新项目目录结构，添加新增的layouts和mock目录说明
  - 更新开发指南，添加更多启动命令说明
  - 更新API地址配置说明，修正为http://localhost:5000/api
  - 添加全新Mock系统的详细说明，替换原有的重构计划
- 更新项目根目录README.md中对boss-data-scope2-new的描述：
  - 添加技术栈版本信息
  - 更新API地址配置
  - 说明已实现的全新Mock系统

[之前的更新记录...]
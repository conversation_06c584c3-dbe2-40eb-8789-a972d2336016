#!/bin/bash

# 数据源管理接口测试脚本
# 测试记录将保存到 datasource-api-test-results.md 文件中

# 清空测试记录文件
echo "# 数据源管理接口测试记录" > 数据源管理接口测试记录.md
echo "" >> 数据源管理接口测试记录.md
echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')" >> 数据源管理接口测试记录.md
echo "" >> 数据源管理接口测试记录.md

# 基础URL和API路径
BASE_URL="http://127.0.0.1:8080/data-scope"
API_PATH="/api/datasources"  # 正确的API路径

# 测试函数，用于执行请求并记录结果
test_api() {
  local method=$1
  local endpoint=$2
  local data=$3
  local description=$4
  
  echo "## $description" >> 数据源管理接口测试记录.md
  echo "" >> 数据源管理接口测试记录.md
  echo "- **请求方法**: $method" >> 数据源管理接口测试记录.md
  echo "- **请求URL**: $BASE_URL$API_PATH$endpoint" >> 数据源管理接口测试记录.md
  
  if [ -n "$data" ]; then
    echo "- **请求体**:" >> 数据源管理接口测试记录.md
    echo '```json' >> 数据源管理接口测试记录.md
    echo "$data" >> 数据源管理接口测试记录.md
    echo '```' >> 数据源管理接口测试记录.md
  fi
  
  echo "" >> 数据源管理接口测试记录.md
  
  # 执行请求
  if [ -n "$data" ]; then
    response=$(curl -s -X $method "$BASE_URL$API_PATH$endpoint" -H "Content-Type: application/json" -d "$data")
  else
    response=$(curl -s -X $method "$BASE_URL$API_PATH$endpoint" -H "Content-Type: application/json")
  fi
  
  echo "- **响应**:" >> 数据源管理接口测试记录.md
  echo '```json' >> 数据源管理接口测试记录.md
  echo "$response" >> 数据源管理接口测试记录.md
  echo '```' >> 数据源管理接口测试记录.md
  echo "" >> 数据源管理接口测试记录.md
  
  # 检查响应是否为有效的JSON
  if echo "$response" | grep -q "success.*true"; then
    # 尝试提取响应中的ID
    id=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    if [ -n "$id" ]; then
      echo "提取到ID: $id"
      echo "- **提取到的ID**: $id" >> 数据源管理接口测试记录.md
      echo "" >> 数据源管理接口测试记录.md
      return 0
    else
      # 如果无法提取ID但响应成功，仍然返回成功
      echo "响应成功，但无法提取ID"
      echo "- **响应成功，但无法提取ID**" >> 数据源管理接口测试记录.md
      echo "" >> 数据源管理接口测试记录.md
      return 0
    fi
  fi
  
  echo "响应失败或无效"
  return 1
}

echo "开始测试数据源管理接口..."
echo "## 测试环境" >> 数据源管理接口测试记录.md
echo "" >> 数据源管理接口测试记录.md
echo "- **基础URL**: $BASE_URL" >> 数据源管理接口测试记录.md
echo "- **API路径**: $API_PATH" >> 数据源管理接口测试记录.md
echo "" >> 数据源管理接口测试记录.md

# 1. 获取数据源列表
echo "测试: 获取数据源列表"
test_api "GET" "" "" "1. 获取数据源列表"

# 2. 创建数据源
echo "测试: 创建数据源"
create_data='{
  "name": "测试MySQL数据源",
  "description": "用于测试的MySQL数据源",
  "type": "mysql",
  "host": "localhost",
  "port": 3306,
  "databaseName": "test_db",
  "username": "test_user",
  "password": "test_password",
  "syncFrequency": "daily"
}'
test_api "POST" "" "$create_data" "2. 创建数据源"
datasource_id=$id

# 如果创建数据源成功，继续测试其他接口
if [ -n "$datasource_id" ]; then
  # 3. 获取数据源详情
  echo "测试: 获取数据源详情"
  test_api "GET" "/$datasource_id" "" "3. 获取数据源详情"
  
  # 4. 更新数据源
  echo "测试: 更新数据源"
  update_data='{
    "id": "'$datasource_id'",
    "name": "更新后的MySQL数据源",
    "description": "更新后的描述",
    "host": "localhost",
    "port": 3306,
    "databaseName": "updated_db",
    "username": "updated_user",
    "password": "updated_password",
    "syncFrequency": "weekly"
  }'
  test_api "PUT" "/$datasource_id" "$update_data" "4. 更新数据源"
  
  # 5. 检查数据源状态
  echo "测试: 检查数据源状态"
  test_api "GET" "/$datasource_id/check-status" "" "5. 检查数据源状态"
  
  # 6. 测试数据源连接
  echo "测试: 测试数据源连接"
  test_api "POST" "/$datasource_id/test-connection" "" "6. 测试数据源连接"
  
  # 7. 获取数据源统计信息
  echo "测试: 获取数据源统计信息"
  test_api "GET" "/$datasource_id/stats" "" "7. 获取数据源统计信息"
  
  # 8. 删除数据源
  echo "测试: 删除数据源"
  test_api "DELETE" "/$datasource_id" "" "8. 删除数据源"
else
  # 尝试从响应中手动提取ID
  echo "尝试从响应中手动提取ID..."
  response_from_file=$(cat 数据源管理接口测试记录.md | grep -A 20 "## 2. 创建数据源" | grep -o '"id":"[^"]*"' | head -1)
  
  if [ -n "$response_from_file" ]; then
    datasource_id=$(echo "$response_from_file" | cut -d'"' -f4)
    echo "从响应文件中提取到ID: $datasource_id"
    echo "## 手动提取ID" >> 数据源管理接口测试记录.md
    echo "" >> 数据源管理接口测试记录.md
    echo "从响应中手动提取到ID: $datasource_id" >> 数据源管理接口测试记录.md
    echo "" >> 数据源管理接口测试记录.md
  else
    echo "无法从响应中提取ID，使用默认ID继续测试"
    datasource_id="be52c6c739fb43a2bb1332ce2f49c28b"  # 使用之前测试中创建的ID
    echo "## 使用默认ID" >> 数据源管理接口测试记录.md
    echo "" >> 数据源管理接口测试记录.md
    echo "无法从响应中提取ID，使用默认ID继续测试: $datasource_id" >> 数据源管理接口测试记录.md
    echo "" >> 数据源管理接口测试记录.md
  fi
  
  # 继续测试其他接口
  # 3. 获取数据源详情
  echo "测试: 获取数据源详情"
  test_api "GET" "/$datasource_id" "" "3. 获取数据源详情"
  
  # 4. 更新数据源
  echo "测试: 更新数据源"
  update_data='{
    "id": "'$datasource_id'",
    "name": "更新后的MySQL数据源",
    "description": "更新后的描述",
    "host": "localhost",
    "port": 3306,
    "databaseName": "updated_db",
    "username": "updated_user",
    "password": "updated_password",
    "syncFrequency": "weekly"
  }'
  test_api "PUT" "/$datasource_id" "$update_data" "4. 更新数据源"
  
  # 5. 检查数据源状态
  echo "测试: 检查数据源状态"
  test_api "GET" "/$datasource_id/check-status" "" "5. 检查数据源状态"
  
  # 6. 测试数据源连接
  echo "测试: 测试数据源连接"
  test_api "POST" "/$datasource_id/test-connection" "" "6. 测试数据源连接"
  
  # 7. 获取数据源统计信息
  echo "测试: 获取数据源统计信息"
  test_api "GET" "/$datasource_id/stats" "" "7. 获取数据源统计信息"
  
  # 8. 删除数据源
  echo "测试: 删除数据源"
  test_api "DELETE" "/$datasource_id" "" "8. 删除数据源"
fi

echo "测试完成，结果已保存到 数据源管理接口测试记录.md 文件中"
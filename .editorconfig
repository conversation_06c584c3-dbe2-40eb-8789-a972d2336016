# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
charset = utf-8
trim_trailing_whitespace = true

# Java files
[*.{java,gradle,xml,yaml,yml}]
indent_style = space
indent_size = 4
max_line_length = 120

# Markdown files
[*.md]
trim_trailing_whitespace = false
indent_style = space
indent_size = 2

# HTML files
[*.{html,htm,css,js}]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 4

# Properties files
[*.properties]
indent_style = space
indent_size = 4

# Shell scripts
[*.sh]
end_of_line = lf
indent_style = space
indent_size = 2

# Batch files
[*.{cmd,bat}]
end_of_line = crlf
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Docker files
[{Dockerfile,docker-compose.yml}]
indent_style = space
indent_size = 2

# Maven files
[pom.xml]
indent_style = space
indent_size = 4
package com.datascope.app.service;

import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.model.AuthResourceBO;
import com.datascope.app.model.ResourceBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuthService {

    /**
     * 添加/删除资源
     *
     * @param resourceBO resourceBO
     * @param path path
     */
    void callAddOrDelResource(ResourceBO resourceBO, String path);

    /**
     * 获取资源
     *
     * @param path path
     * @param token token
     * @return List
     */
    List<AuthResourceBO> getResources(String path, String token);

    /**
     * checkAuth
     *
     * @param path path
     * @param loginName loginName
     * @param token token
     * @return boolean
     */
    boolean checkAuth(String path, String loginName, String token);

    /**
     * checkSqlAuth
     *
     * @param authDTO authDTO
     */
    void checkSqlAuth(AuthDTO authDTO);

    /**
     * getAuthToken
     *
     * @return String
     */
    String getAuthToken();
}

package com.datascope.app.common.adapter;

import com.datascope.app.common.PageRequest;
import com.datascope.app.common.Pageable;
import org.springframework.stereotype.Component;

/**
 * 分页参数适配器
 * 用于将应用层的Pageable转换为领域层的Pageable
 */
@Component
public class PageableAdapter {

    /**
     * 将应用层的Pageable转换为领域层的Pageable
     *
     * @param appPageable 应用层Pageable
     * @return 领域层Pageable
     */
    public Pageable toDomain(Pageable appPageable) {
        if (appPageable == null) {
            return null;
        }

        return PageRequest.of(
            appPageable.getPageNumber(),
            appPageable.getPageSize()
        );
    }

    /**
     * 将领域层的Pageable转换为应用层的Pageable
     *
     * @param domainPageable 领域层Pageable
     * @return 应用层Pageable
     */
    public Pageable toApp(Pageable domainPageable) {
        if (domainPageable == null) {
            return null;
        }

        return com.datascope.app.common.PageRequest.of(
            domainPageable.getPageNumber(),
            domainPageable.getPageSize()
        );
    }
}

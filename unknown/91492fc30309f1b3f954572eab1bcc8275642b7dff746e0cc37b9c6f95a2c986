# 代码规范

## 编码规范

### 基本原则

- 按照领域模型修订实现，注意采用mybatis、Lombok等第三方工具包简化代码量，但不允许使用JPA、Hibernate、mapstruct
- 类的属性必须添加中文注释
- 实体的id采用String类型，与数据库类型保持一致
- 枚举属于值对象
- 严禁使用System.out.println打印日志，而是采用log的方式
- 避免过长的方法和类，单个方法不应超过50行
- 保持代码的可读性和逻辑清晰性

### 命名规范

1. **包命名**
  - 全小写字母，使用有意义的包名
  - 例如：com.datascope.domain.datasource

2. **类命名**
  - 大驼峰命名法（PascalCase）
  - 类名应该是名词
  - 例如：DataSourceService

3. **方法命名**
  - 小驼峰命名法（camelCase），动词或动词短语
  - 例如：createDataSource()

4. **变量命名**
  - 小驼峰命名法，有意义的名称
  - 例如：dataSourceName

5. **常量命名**
  - 全大写，下划线分隔
  - 例如：MAX_CONNECTION_COUNT

6. **布尔变量命名**
  - 变量命名禁止使用"is"、"has"、"can"等前缀，但可以提供这种有含义的访问方法
  - 例如：isActive、hasPermission、canEdit

### 代码格式

1. **缩进**
  - 使用4个空格，不使用Tab

2. **行长度**
  - 最大120个字符
  - 超过时进行换行

3. **空行**
  - 方法之间加空行
  - 逻辑块之间加空行
  - 不要有连续多个空行

4. **括号**
  - 左括号不换行
  - 右括号独占一行

### 注释规范

1. **类注释**

```java
/**
 * 类的功能描述
 */
```

2. **方法注释**

```java
/**
 * 方法的功能描述
 *
 * @param param1 参数1的说明
 * @param param2 参数2的说明
 * @return 返回值说明
 * @throws Exception 异常说明
 */
```

3. **字段注释**

```java
/**
 * 字段说明
 */
private String field;
```

4. **TODO注释**

```java
// TODO: PROJ-123 需要实现的功能
```

### 枚举管理规范

1. 公共枚举应放在`com.datascope.domain.common.enums`包下
2. 模块特有枚举需添加模块前缀(如`DataSourceType`)
3. 新增枚举需经过架构评审
4. 禁止重复定义功能相似的枚举
5. 枚举值变更需考虑向后兼容性

### 异常处理

* **Controller 层：** 使用 `@RestControllerAdvice` 捕获全局异常，并返回统一的错误响应格式。
* **Service 层：** 捕获底层异常，并转换为业务异常，向上层抛出。
* **Repository 层：** 捕获数据库访问异常，并转换为数据访问异常，向上层抛出。

* **异常包装：** 将底层异常包装为领域特定的异常，保留原始异常作为原因，并添加上下文信息。
* **日志记录：** 记录系统错误的完整堆栈跟踪，记录业务错误的错误消息，包含所有日志条目的关联ID，屏蔽日志中的敏感数据。
* **事务管理：** 在系统错误时回滚事务，考虑批量操作的部分提交，记录事务状态。
* **重试机制：** 为瞬时故障实现重试，使用指数退避，设置最大重试次数。

### 常见代码问题与最佳实践

#### 缩进与代码结构

- 严格使用4个空格进行缩进，不允许使用Tab
- 保持代码块的括号匹配和正确缩进
- 避免过长的方法和类，单个方法不应超过50行
- 保持代码的可读性和逻辑清晰性

#### 注解使用规范

- `@Override`注解必须用于正确覆盖父类或接口方法
- 检查方法签名是否完全匹配接口或父类方法
- 如果方法未在接口或父类中定义，不要使用`@Override`注解

#### 类型转换与方法调用

- 进行类型转换时，必须确保目标类型存在相应的方法
- 避免使用未经验证的类型转换
- 对于复杂的类型转换，建议使用显式的类型检查和转换方法

### 避免重复创建类的策略

- 在创建新类前，先搜索项目中是否已存在相似功能的类
- 使用统一的命名约定，便于搜索和识别
- 定期进行代码重构，合并功能相似的类
- 维护类职责文档，记录每个包和主要类的职责
- 在代码审查中检查是否存在重复创建类的情况

## 代码审查清单

### 通用检查项

- 代码是否遵循本文档中的编码规范
- 方法和类的命名是否清晰、有意义
- 是否存在重复代码
- 异常处理是否得当
- 是否有不必要的注释或被注释掉的代码

### 特定检查项

- 检查`@Override`注解的正确使用
- 验证类型转换的安全性
- 审查方法的参数验证和空值处理
- 评估代码的可读性和可维护性

## 持续改进

本编码规范是一个动态文档，随着项目的发展和技术的演进会不断更新。团队成员应该：

- 定期review和讨论编码规范
- 及时更新文档，反映最佳实践的变化
- 在代码审查中相互学习和改进
- 保持开放的心态，接受新的编码技巧和方法

通过遵循这些规范和最佳实践，我们可以持续提高代码质量，增强系统的可维护性和可读性。

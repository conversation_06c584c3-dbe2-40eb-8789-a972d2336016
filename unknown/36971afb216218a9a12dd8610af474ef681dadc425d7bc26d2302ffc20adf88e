package com.datascope.app.controller;

import com.datascope.app.dto.integration.ExecuteIntegrationQueryRequest;
import com.datascope.app.dto.integration.IntegrationQueryResultDTO;
import com.datascope.app.service.IntegrationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 集成管理控制器测试
 */
public class IntegrationControllerTest {

    private MockMvc mockMvc;
    private MockMvc compatMockMvc;

    @Mock
    private IntegrationService integrationService;

    @InjectMocks
    private IntegrationController integrationController;

    @InjectMocks
    private IntegrationCompatController integrationCompatController;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(integrationController).build();
        compatMockMvc = MockMvcBuilders.standaloneSetup(integrationCompatController).build();
    }

    @Test
    public void testExecuteIntegrationQuery_NewPath() throws Exception {
        // 准备测试数据
        IntegrationQueryResultDTO resultDTO = new IntegrationQueryResultDTO();
        resultDTO.setColumns(new ArrayList<>());
        
        // Mock服务方法
        when(integrationService.executeIntegrationQuery(any(ExecuteIntegrationQueryRequest.class)))
                .thenReturn(resultDTO);
        
        // 测试新路径
        mockMvc.perform(post("/api/integrations/execute-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"integrationId\":\"test-id\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists());
    }

    @Test
    public void testExecuteIntegrationQuery_OldPath() throws Exception {
        // 准备测试数据
        IntegrationQueryResultDTO resultDTO = new IntegrationQueryResultDTO();
        resultDTO.setColumns(new ArrayList<>());
        
        // Mock服务方法
        when(integrationService.executeIntegrationQuery(any(ExecuteIntegrationQueryRequest.class)))
                .thenReturn(resultDTO);
        
        // 测试旧路径
        compatMockMvc.perform(post("/api/integration/execute-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"integrationId\":\"test-id\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists());
    }
} 
package com.datascope.app.common.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分页响应类
 *
 * @param <T> 数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {

    /**
     * 当前页码
     */
    private int page;

    /**
     * 每页大小
     */
    private int size;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int pages;

    @Builder.Default
    private Boolean success = true;

    /**
     * 数据列表
     */
    private List<T> items;

    /**
     * 是否有上一页
     */
    private boolean hasPrevious;

    /**
     * 是否有下一页
     */
    private boolean hasNext;

    /**
     * 是否为第一页
     */
    private boolean isFirst;

    /**
     * 是否为最后一页
     */
    private boolean isLast;

    /**
     * 创建分页响应
     *
     * @param content 数据列表
     * @param page    当前页码
     * @param size    每页大小
     * @param total   总记录数
     * @param <T>     数据类型
     * @return 分页响应
     */
    public static <T> PageResponse<T> of(List<T> content, int page, int size, long total) {
        int pages = size == 0 ? 1 : (int) Math.ceil((double) total / (double) size);
        boolean isFirst = page <= 1;
        boolean isLast = page >= pages;
        boolean hasPrevious = !isFirst;
        boolean hasNext = !isLast;

        return PageResponse.<T>builder()
            .items(content)
            .page(page)
            .size(size)
            .total(total)
            .pages(pages)
            .isFirst(isFirst)
            .isLast(isLast)
            .hasPrevious(hasPrevious)
            .hasNext(hasNext)
            .build();
    }

    /**
     * 创建空的分页响应
     *
     * @param <T> 数据类型
     * @return 空的分页响应
     */
    public static <T> PageResponse<T> empty() {
        return PageResponse.<T>builder()
            .page(1)
            .size(0)
            .total(0)
            .pages(1)
            .isFirst(true)
            .isLast(true)
            .hasPrevious(false)
            .hasNext(false)
            .build();
    }
}

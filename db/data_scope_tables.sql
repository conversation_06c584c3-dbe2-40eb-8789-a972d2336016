-- 数据库建表语句
-- 根据接口文档生成的数据库表结构

-- 查询管理相关表

-- 查询表
CREATE TABLE `queries` (
  `id` varchar(64) NOT NULL COMMENT '查询ID',
  `name` varchar(255) NOT NULL COMMENT '查询名称',
  `description` text COMMENT '查询描述',
  `folder_id` varchar(64) DEFAULT NULL COMMENT '文件夹ID',
  `status` varchar(20) NOT NULL COMMENT '状态：DRAFT-草稿，PUBLISHED-已发布，DEPRECATED-已废弃，ARCHIVED-已归档',
  `service_status` varchar(20) NOT NULL DEFAULT 'ENABLED' COMMENT '服务状态：ENABLED-启用，DISABLED-禁用',
  `data_source_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `query_type` varchar(20) NOT NULL COMMENT '查询类型：SQL-SQL查询，NATURAL_LANGUAGE-自然语言查询',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开',
  `result_count` int DEFAULT NULL COMMENT '结果数量',
  `execution_time` decimal(10,2) DEFAULT NULL COMMENT '执行时间(毫秒)',
  `execution_count` int NOT NULL DEFAULT '0' COMMENT '执行次数',
  `last_executed_at` datetime DEFAULT NULL COMMENT '最后执行时间',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_queries_data_source_id` (`data_source_id`),
  KEY `idx_queries_status` (`status`),
  KEY `idx_queries_created_by` (`created_by`),
  KEY `idx_queries_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询表';

-- 查询版本表
CREATE TABLE `query_versions` (
  `id` varchar(64) NOT NULL COMMENT '版本ID',
  `query_id` varchar(64) NOT NULL COMMENT '查询ID',
  `version_number` int NOT NULL COMMENT '版本号',
  `name` varchar(255) DEFAULT NULL COMMENT '版本名称',
  `description` text COMMENT '版本描述',
  `sql_content` text NOT NULL COMMENT 'SQL内容',
  `data_source_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `status` varchar(20) NOT NULL COMMENT '状态：DRAFT-草稿，PUBLISHED-已发布，DEPRECATED-已废弃',
  `is_latest` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否最新版本',
  `comment` text COMMENT '版本注释',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_query_version` (`query_id`,`version_number`),
  KEY `idx_query_versions_query_id` (`query_id`),
  KEY `idx_query_versions_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询版本表';

-- 查询执行历史表
CREATE TABLE `execution_history` (
  `id` varchar(64) NOT NULL COMMENT '执行历史ID',
  `query_id` varchar(64) NOT NULL COMMENT '查询ID',
  `version_id` varchar(64) NOT NULL COMMENT '版本ID',
  `executed_by` varchar(64) NOT NULL COMMENT '执行人',
  `executed_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
  `status` varchar(20) NOT NULL COMMENT '状态：success-成功，error-错误，running-运行中，cancelled-已取消',
  `duration` decimal(10,2) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `row_count` int DEFAULT NULL COMMENT '结果行数',
  `parameters` text COMMENT '执行参数(JSON格式)',
  `error` text COMMENT '错误信息',
  `result_id` varchar(64) DEFAULT NULL COMMENT '结果ID',
  PRIMARY KEY (`id`),
  KEY `idx_execution_history_query_id` (`query_id`),
  KEY `idx_execution_history_executed_by` (`executed_by`),
  KEY `idx_execution_history_executed_at` (`executed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询执行历史表';

-- 查询收藏表
CREATE TABLE `query_favorites` (
  `id` varchar(64) NOT NULL COMMENT '收藏ID',
  `query_id` varchar(64) NOT NULL COMMENT '查询ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_query_user` (`query_id`,`user_id`),
  KEY `idx_query_favorites_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询收藏表';

-- 查询参数表
CREATE TABLE `query_parameters` (
  `id` varchar(64) NOT NULL COMMENT '参数ID',
  `query_id` varchar(64) NOT NULL COMMENT '查询ID',
  `version_id` varchar(64) DEFAULT NULL COMMENT '版本ID',
  `name` varchar(100) NOT NULL COMMENT '参数名称',
  `type` varchar(20) NOT NULL COMMENT '参数类型：string-字符串，number-数字，boolean-布尔值，date-日期',
  `label` varchar(100) DEFAULT NULL COMMENT '参数标签',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填',
  `options` text COMMENT '选项(JSON格式)',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_query_parameters_query_id` (`query_id`),
  KEY `idx_query_parameters_version_id` (`version_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询参数表';

-- 数据源管理相关表

-- 数据源表
CREATE TABLE `datasources` (
  `id` varchar(64) NOT NULL COMMENT '数据源ID',
  `name` varchar(255) NOT NULL COMMENT '数据源名称',
  `description` text COMMENT '数据源描述',
  `type` varchar(20) NOT NULL COMMENT '数据源类型：mysql, postgresql, oracle, sqlserver, mongodb, elasticsearch',
  `host` varchar(255) NOT NULL COMMENT '主机地址',
  `port` int NOT NULL COMMENT '端口号',
  `database_name` varchar(255) NOT NULL COMMENT '数据库名称',
  `schema` varchar(255) DEFAULT NULL COMMENT '模式名称',
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码(加密存储)',
  `status` varchar(20) NOT NULL DEFAULT 'inactive' COMMENT '状态：active-活跃，inactive-不活跃，error-错误，syncing-同步中',
  `sync_frequency` varchar(20) NOT NULL DEFAULT 'manual' COMMENT '同步频率：manual-手动，hourly-每小时，daily-每天，weekly-每周，monthly-每月',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `error_message` text COMMENT '错误信息',
  `connection_params` text COMMENT '连接参数(JSON格式)',
  `encryption_type` varchar(20) NOT NULL DEFAULT 'none' COMMENT '加密类型：none-无，ssl-SSL，tls-TLS',
  `encryption_options` text COMMENT '加密选项(JSON格式)',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_datasources_name` (`name`),
  KEY `idx_datasources_type` (`type`),
  KEY `idx_datasources_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源表';

-- 数据源统计信息表
CREATE TABLE `datasource_stats` (
  `id` varchar(64) NOT NULL COMMENT '统计ID',
  `datasource_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `tables_count` int DEFAULT NULL COMMENT '表数量',
  `views_count` int DEFAULT NULL COMMENT '视图数量',
  `total_rows` bigint DEFAULT NULL COMMENT '总行数',
  `total_size` varchar(50) DEFAULT NULL COMMENT '总大小',
  `queries_count` int DEFAULT NULL COMMENT '查询数量',
  `connection_pool_size` int DEFAULT NULL COMMENT '连接池大小',
  `active_connections` int DEFAULT NULL COMMENT '活跃连接数',
  `avg_query_time` varchar(50) DEFAULT NULL COMMENT '平均查询时间',
  `avg_response_time` decimal(10,2) DEFAULT NULL COMMENT '平均响应时间(毫秒)',
  `peak_connections` int DEFAULT NULL COMMENT '峰值连接数',
  `last_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_datasource_stats_datasource_id` (`datasource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源统计信息表';

-- 元数据同步记录表
CREATE TABLE `metadata_sync` (
  `id` varchar(64) NOT NULL COMMENT '同步ID',
  `datasource_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` varchar(20) NOT NULL COMMENT '状态：running-运行中，completed-完成，failed-失败',
  `tables_count` int DEFAULT NULL COMMENT '表数量',
  `views_count` int DEFAULT NULL COMMENT '视图数量',
  `sync_duration` int DEFAULT NULL COMMENT '同步时长(毫秒)',
  `message` text COMMENT '消息',
  `errors` text COMMENT '错误信息(JSON格式)',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_metadata_sync_datasource_id` (`datasource_id`),
  KEY `idx_metadata_sync_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='元数据同步记录表';

-- 元数据管理相关表

-- 数据库模式表
CREATE TABLE `schemas` (
  `id` varchar(64) NOT NULL COMMENT '模式ID',
  `datasource_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `name` varchar(255) NOT NULL COMMENT '模式名称',
  `description` text COMMENT '模式描述',
  `tables_count` int DEFAULT '0' COMMENT '表数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_schemas_datasource_name` (`datasource_id`,`name`),
  KEY `idx_schemas_datasource_id` (`datasource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库模式表';

-- 数据表信息表
CREATE TABLE `tables` (
  `id` varchar(64) NOT NULL COMMENT '表ID',
  `schema_id` varchar(64) NOT NULL COMMENT '模式ID',
  `datasource_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `name` varchar(255) NOT NULL COMMENT '表名称',
  `type` varchar(20) NOT NULL COMMENT '类型：TABLE-表，VIEW-视图',
  `description` text COMMENT '表描述',
  `row_count` bigint DEFAULT NULL COMMENT '行数',
  `columns_count` int DEFAULT NULL COMMENT '列数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tables_schema_name` (`schema_id`,`name`),
  KEY `idx_tables_datasource_id` (`datasource_id`),
  KEY `idx_tables_schema_id` (`schema_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据表信息表';

-- 列信息表
CREATE TABLE `columns` (
  `id` varchar(64) NOT NULL COMMENT '列ID',
  `table_id` varchar(64) NOT NULL COMMENT '表ID',
  `name` varchar(255) NOT NULL COMMENT '列名称',
  `data_type` varchar(100) NOT NULL COMMENT '数据类型',
  `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
  `position` int NOT NULL COMMENT '位置',
  `is_nullable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可为空',
  `is_primary_key` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主键',
  `is_unique` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否唯一',
  `is_indexed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否索引',
  `default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `character_length` int DEFAULT NULL COMMENT '字符长度',
  `numeric_precision` int DEFAULT NULL COMMENT '数值精度',
  `numeric_scale` int DEFAULT NULL COMMENT '数值刻度',
  `description` text COMMENT '列描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_columns_table_name` (`table_id`,`name`),
  KEY `idx_columns_table_id` (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='列信息表';

-- 集成管理相关表

-- 集成表
CREATE TABLE `integrations` (
  `id` varchar(64) NOT NULL COMMENT '集成ID',
  `name` varchar(255) NOT NULL COMMENT '集成名称',
  `description` text COMMENT '集成描述',
  `type` varchar(20) NOT NULL COMMENT '集成类型：SIMPLE_TABLE-简单表格，TABLE-表格，CHART-图表',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态：ACTIVE-活跃，INACTIVE-不活跃，DRAFT-草稿',
  `query_id` varchar(64) NOT NULL COMMENT '查询ID',
  `datasource_id` varchar(64) NOT NULL COMMENT '数据源ID',
  `chart_config` text COMMENT '图表配置(JSON格式)',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_integrations_query_id` (`query_id`),
  KEY `idx_integrations_datasource_id` (`datasource_id`),
  KEY `idx_integrations_status` (`status`),
  KEY `idx_integrations_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集成表';

-- 集成点表
CREATE TABLE `integration_points` (
  `id` varchar(64) NOT NULL COMMENT '集成点ID',
  `integration_id` varchar(64) NOT NULL COMMENT '集成ID',
  `name` varchar(255) NOT NULL COMMENT '集成点名称',
  `type` varchar(20) NOT NULL COMMENT '集成点类型：URL-URL，API-API',
  `url_config` text COMMENT 'URL配置(JSON格式)',
  `api_config` text COMMENT 'API配置(JSON格式)',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_integration_points_integration_id` (`integration_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集成点表';

-- 用户表
CREATE TABLE `users` (
  `id` varchar(64) NOT NULL COMMENT '用户ID',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `email` varchar(255) NOT NULL COMMENT '电子邮件',
  `password` varchar(255) NOT NULL COMMENT '密码(加密存储)',
  `name` varchar(100) DEFAULT NULL COMMENT '姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃，inactive-不活跃，locked-锁定',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_username` (`username`),
  UNIQUE KEY `uk_users_email` (`email`),
  KEY `idx_users_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 标签表
CREATE TABLE `tags` (
  `id` varchar(64) NOT NULL COMMENT '标签ID',
  `name` varchar(100) NOT NULL COMMENT '标签名称',
  `type` varchar(20) DEFAULT NULL COMMENT '标签类型',
  `color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `nonce` int NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tags_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 对象标签关联表
CREATE TABLE `object_tags` (
  `id` varchar(64) NOT NULL COMMENT '关联ID',
  `object_id` varchar(64) NOT NULL COMMENT '对象ID',
  `object_type` varchar(50) NOT NULL COMMENT '对象类型',
  `tag_id` varchar(64) NOT NULL COMMENT '标签ID',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_object_tags` (`object_id`,`object_type`,`tag_id`),
  KEY `idx_object_tags_tag_id` (`tag_id`),
  KEY `idx_object_tags_object_type` (`object_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对象标签关联表';
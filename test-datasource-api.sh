#!/bin/bash

# 测试不同的API路径
echo "测试不同的API路径..."

# 基础URL
BASE_URLS=(
  "http://localhost:8080"
  "http://localhost:8080/data-scope"
)

# API路径
API_PATHS=(
  "/api/v1/datasources"
  "/v1/datasources"
  "/api/datasources"
  "/datasources"
)

# 测试所有组合
for base in "${BASE_URLS[@]}"; do
  for path in "${API_PATHS[@]}"; do
    url="${base}${path}"
    echo "测试: $url"
    response=$(curl -s -X GET "$url" -H "Content-Type: application/json")
    echo "响应: $response"
    echo "-----------------------------------"
  done
done

echo "测试完成"
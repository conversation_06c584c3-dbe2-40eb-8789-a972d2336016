# DataScope功能清单

## 1. 数据源管理

> 详细的数据源管理模块功能清单参见 [datasource-functions.mdc](datasource-functions.mdc)

### 1.1 数据源基本操作
- 新增数据源（MySQL/DB2）
- 编辑数据源信息
- 删除数据源
- 查看数据源列表
- 查看数据源详情
- 密码AES-GCM加密存储

### 1.2 数据源连接管理
- 测试数据源连接
- 监控数据源健康状态
- 管理连接池
- 处理连接超时

### 1.3 元数据同步
- 手动触发元数据同步
- 自动定时同步（24小时周期）
- 增量元数据更新
- 查看同步历史和日志
- 同步状态监控

## 2. 元数据管理

### 2.1 元数据浏览
- 查看架构(Schema)列表
- 查看表(Table)列表
- 查看列(Column)信息
- 查看索引信息
- 查看视图(View)信息

### 2.2 元数据搜索
- 基本关键词搜索
- 高级搜索（多条件）
- 区分大小写搜索
- 正则表达式搜索
- 按实体类型搜索（表/列/视图）
- 查看搜索结果
- 导出搜索结果为CSV

### 2.3 元数据缓存
- 缓存查询结果
- 缓存失效管理
- 缓存命中率统计

## 3. 数据查询

### 3.1 SQL查询
- 编写SQL查询
- 执行SQL查询
- SQL语法高亮
- SQL语法验证
- 查询超时控制（默认30秒）
- 查询频率限制

### 3.2 自然语言查询
- 输入自然语言描述
- 自然语言转SQL（通过OpenRouter LLM）
- 调整和优化生成的SQL
- 多轮交互优化查询
- 支持中文自然语言输入

### 3.3 查询结果管理
- 分页显示结果
- 导出结果为CSV（最多50000条）
- 结果列排序
- 结果列过滤
- 数据格式化展示

### 3.4 查询历史管理
- 保存查询历史
- 查看历史查询
- 重新执行历史查询
- 收藏常用查询
- 编辑保存的查询

## 4. 表关系管理

### 4.1 表关系定义
- 手动定义表关系
- 编辑已定义关系
- 删除表关系
- 查看表关系列表
- 表关系可视化

### 4.2 表关系推断
- 基于命名约定自动推断关系
- 基于查询模式学习关系
- 自动识别外键关系
- 通过数据分析推断关联
- 关系推断任务状态监控
- 查看推断关系的置信度

## 5. 数据预览

### 5.1 表数据预览
- 分页查看表数据
- 自定义每页显示条数
- 刷新数据
- 导出表数据为CSV

### 5.2 数据筛选和排序
- 按列排序（升序/降序）
- 列数据过滤
- 多条件组合过滤
- 清除单列过滤
- 清除所有过滤

### 5.3 数据统计
- 查看列统计信息（唯一值、空值）
- 数值列统计（最小值、最大值）
- 日期列统计

## 6. 低代码平台集成

### 6.1 页面配置管理
- 创建页面配置
- 编辑页面配置
- 删除页面配置
- 查看页面配置列表
- 查看页面配置详情
- 版本管理（回滚、比较）

### 6.2 查询条件配置
- 配置必填查询条件
- 配置可选查询条件
- 设置默认值
- 配置参数依赖规则
- 自动隐藏不常用条件
- 设置字段校验规则

### 6.3 结果展示配置
- 配置显示列
- 设置列顺序
- 列格式化配置
- 敏感数据掩码配置
- 配置操作列

### 6.4 集成API管理
- 生成API端点
- API版本控制
- API参数映射
- API文档生成
- API使用统计

### 6.5 数据类型映射
- 自动映射数据类型到UI组件
- 定制数据类型映射规则
- 数据类型预览

## 7. 系统管理

### 7.1 性能监控
- 查询执行时间统计
- 资源利用率监控
- 用户活动监控
- 性能瓶颈识别

### 7.2 系统日志
- 查看系统日志
- 日志过滤和搜索
- 错误日志分析
- 审计日志查看

### 7.3 系统配置
- 全局查询超时设置
- 查询频率限制配置
- 元数据同步设置
- OpenRouter LLM配置管理

## 8. 用户界面功能

### 8.1 界面导航
- 主导航菜单
- 面包屑导航
- 返回上一级
- 快捷操作按钮

### 8.2 通知和消息
- 操作成功/失败提示
- 长时间操作进度指示
- 系统通知
- 错误详情展示

### 8.3 视图切换
- 列表/详情视图切换
- 表格/图表视图切换
- 展开/折叠详情
- 调整视图大小

### 8.4 用户体验优化
- 响应式布局
- 加载状态指示
- 空状态处理
- 错误状态处理
- 用户偏好记忆

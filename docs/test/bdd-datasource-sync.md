Feature: 数据源元数据同步

## Scenario 1: 成功手动触发全量同步

Given: 用户已登录系统
And: 系统中已存在一个名为 "MySQL Prod" 的已配置且可连接的数据源
And: 该数据源包含表 "users" 和 "orders"
And: 用户在数据源管理页面
When: 用户点击 "MySQL Prod" 数据源对应的 "同步元数据" 按钮（或选择全量同步选项）
Then: 系统后台启动对 "MySQL Prod" 的全量元数据同步任务
And: 系统界面显示同步任务正在进行中
And: 同步任务完成后，系统提示 "元数据同步成功"
And: 系统内部存储的 "MySQL Prod" 元数据包含 "users" 和 "orders" 表及其列信息

Acceptance Criteria:

- [ ] 用户能够为指定数据源手动触发全量元数据同步。
- [ ] 系统应能成功连接数据源并拉取所有表和列的元数据。
- [ ] 同步完成后，系统应存储最新的元数据信息。
- [ ] 界面应反馈同步任务的状态（进行中、成功、失败）。

## Scenario 2: 成功手动触发增量同步（检测到新增表）

Given: 用户已登录系统
And: 系统中已存在数据源 "MySQL Prod"，且已成功进行过一次全量同步，包含表 "users"
And: 之后，在 "MySQL Prod" 的实际数据库中新增了一个表 "products"
And: 用户在数据源管理页面
When: 用户点击 "MySQL Prod" 数据源对应的 "同步元数据" 按钮（并选择增量同步，如果系统支持）
Then: 系统后台启动对 "MySQL Prod" 的增量元数据同步任务
And: 系统界面显示同步任务正在进行中
And: 同步任务完成后，系统提示 "元数据同步成功"
And: 系统内部存储的 "MySQL Prod" 元数据现在包含 "users" 和 "products" 表

Acceptance Criteria:

- [ ] 用户能够触发增量同步（如果功能支持）。
- [ ] 增量同步应能检测到源数据库的模式变更（如新增表）。
- [ ] 同步完成后，系统元数据应准确反映这些变更。

## Scenario 3: 同步过程中数据源连接失败

Given: 用户已登录系统
And: 系统中已存在数据源 "Unstable DB"
And: 用户在数据源管理页面
When: 用户触发对 "Unstable DB" 的元数据同步
And: 在同步过程中，与 "Unstable DB" 的连接中断
Then: 系统同步任务失败
And: 系统提示 "元数据同步失败"
And: 系统提供具体的错误信息，例如 "与数据源的连接丢失"
And: 系统内部存储的 "Unstable DB" 元数据保持在同步开始前的状态（或标记为不一致）

Acceptance Criteria:

- [ ] 同步过程中如遇连接问题，任务应优雅地失败。
- [ ] 系统应报告失败状态和具体的连接错误。
- [ ] 失败的同步不应破坏已有的元数据一致性。

## Scenario 4: 尝试同步一个配置错误的数据源

Given: 用户已登录系统
And: 系统中存在一个数据源 "Misconfigured DB"，其连接信息不正确（如错误的端口）
And: 用户在数据源管理页面
When: 用户触发对 "Misconfigured DB" 的元数据同步
Then: 系统尝试连接数据源失败
And: 系统同步任务失败
And: 系统提示 "元数据同步失败"
And: 系统提供具体的错误信息，例如 "无法连接到数据库服务器"

Acceptance Criteria:

- [ ] 对配置错误、无法连接的数据源触发同步，任务应直接失败。
- [ ] 系统应报告失败状态和连接错误信息。

## Scenario 5: 尝试同步不存在的数据源

Given: 用户已登录系统
When: 用户尝试通过某种方式（如 API 调用）为 ID 为 "nonexistent-ds" 的数据源触发同步
Then: 系统返回错误，指示数据源未找到
And: 没有同步任务被创建或执行

Acceptance Criteria:

- [ ] 不能为不存在的数据源启动同步任务。
- [ ] 系统应返回明确的 "未找到" 错误。

Feature: 数据源连接测试

## Scenario 1: 测试有效的 MySQL 数据源连接

Given: 用户已登录系统
And: 系统中已存在一个配置正确的 MySQL 数据源 "Valid MySQL"
And: 用户在数据源管理页面
When: 用户点击 "Valid MySQL" 数据源对应的 "测试连接" 按钮
Then: 系统显示 "连接成功" 的提示信息

Acceptance Criteria:

- [ ] 对于配置正确且可达的数据源，测试连接操作应返回成功状态。

## Scenario 2: 测试无效的 MySQL 数据源连接（密码错误）

Given: 用户已登录系统
And: 系统中已存在一个 MySQL 数据源 "Invalid Pass MySQL"，但其密码配置错误
And: 用户在数据源管理页面
When: 用户点击 "Invalid Pass MySQL" 数据源对应的 "测试连接" 按钮
Then: 系统显示 "连接失败" 的提示信息
And: 系统应提供具体的错误原因，例如 "用户名或密码错误"

Acceptance Criteria:

- [ ] 对于配置错误（如密码不正确）的数据源，测试连接应失败。
- [ ] 失败时应返回明确、有用的错误信息。

## Scenario 3: 测试无效的 MySQL 数据源连接（主机不可达）

Given: 用户已登录系统
And: 系统中已存在一个 MySQL 数据源 "Unreachable MySQL"，其主机地址当前无法访问
And: 用户在数据源管理页面
When: 用户点击 "Unreachable MySQL" 数据源对应的 "测试连接" 按钮
Then: 系统显示 "连接失败" 的提示信息
And: 系统应提供具体的错误原因，例如 "无法连接到数据库服务器" 或 "连接超时"

Acceptance Criteria:

- [ ] 对于网络不可达（如主机错误、端口不通、防火墙阻止）的数据源，测试连接应失败。
- [ ] 失败时应返回明确、有用的网络相关错误信息。

## Scenario 4: 在创建数据源表单中测试连接（成功）

Given: 用户已登录系统
And: 用户正在创建或编辑一个新的数据源
When: 用户在表单中填写了所有必需且有效的连接信息
And: 用户点击表单内的 "测试连接" 按钮
Then: 系统在表单附近显示 "连接成功" 的提示信息

Acceptance Criteria:

- [ ] 在数据源创建或编辑界面，用户可以直接测试当前输入的配置。
- [ ] 测试成功时，应在界面上给出清晰的成功反馈。

## Scenario 5: 在创建数据源表单中测试连接（失败）

Given: 用户已登录系统
And: 用户正在创建或编辑一个新的数据源
When: 用户在表单中填写了无效的连接信息（例如，错误的端口号）
And: 用户点击表单内的 "测试连接" 按钮
Then: 系统在表单附近显示 "连接失败" 的提示信息
And: 系统应提供具体的错误原因

Acceptance Criteria:

- [ ] 在数据源创建或编辑界面测试无效配置时，应失败。
- [ ] 测试失败时，应在界面上给出清晰的失败反馈及原因。

Feature: 数据源管理 CRUD 操作

## Scenario 1: 成功创建 MySQL 数据源

Given: 用户已登录系统
And: 用户在数据源管理页面
When: 用户填写 MySQL 数据源信息（名称: "Test MySQL", 类型: "MySQL", 主机: "localhost", 端口: "3306", 用户名: "testuser",
密码: "password123", 数据库: "testdb"）
And: 用户点击 "保存" 按钮
Then: 系统提示 "数据源创建成功"
And: 数据源列表显示新增的 "Test MySQL" 数据源
And: 数据库中存储的 "Test MySQL" 数据源密码是加密的

Acceptance Criteria:

- [ ] 能够成功创建一个新的 MySQL 数据源。
- [ ] 创建成功后，列表中能看到新创建的数据源。
- [ ] 数据源的密码在数据库中应以加密形式存储。

## Scenario 2: 创建数据源时缺少必填项（名称）

Given: 用户已登录系统
And: 用户在数据源管理页面
When: 用户填写 MySQL 数据源信息，但未填写名称（类型: "MySQL", 主机: "localhost", 端口: "3306", 用户名: "testuser", 密码: "
password123", 数据库: "testdb"）
And: 用户点击 "保存" 按钮
Then: 系统提示 "名称不能为空"
And: 数据源未被创建

Acceptance Criteria:

- [ ] 当缺少必填字段（如名称）时，系统应阻止创建并给出明确提示。

## Scenario 3: 创建数据源时名称重复

Given: 用户已登录系统
And: 系统中已存在一个名为 "Existing MySQL" 的数据源
And: 用户在数据源管理页面
When: 用户填写 MySQL 数据源信息，名称为 "Existing MySQL"（类型: "MySQL", 主机: "*************", 端口: "3306", 用户名: "
dupuser", 密码: "password456", 数据库: "dupdb"）
And: 用户点击 "保存" 按钮
Then: 系统提示 "数据源名称已存在"
And: 数据源未被创建

Acceptance Criteria:

- [ ] 当尝试使用已存在的名称创建数据源时，系统应阻止创建并给出明确提示。

## Scenario 4: 查看数据源列表

Given: 用户已登录系统
And: 系统中已存在多个数据源 ("MySQL Prod", "PostgreSQL Dev")
When: 用户访问数据源管理页面
Then: 页面显示包含 "MySQL Prod" 和 "PostgreSQL Dev" 的数据源列表
And: 列表中不显示数据源的明文密码

Acceptance Criteria:

- [ ] 用户可以查看所有已配置的数据源列表。
- [ ] 列表中不应显示敏感信息，如明文密码。

## Scenario 5: 查看单个数据源详情（编辑模式）

Given: 用户已登录系统
And: 系统中已存在一个名为 "MySQL Prod" 的数据源
When: 用户在数据源列表中点击 "MySQL Prod" 的 "编辑" 按钮
Then: 页面显示 "MySQL Prod" 的详细信息（名称、类型、主机、端口、用户名、数据库），密码字段通常显示为掩码或空（待重新输入）
And: 密码字段不显示明文密码

Acceptance Criteria:

- [ ] 用户可以查看特定数据源的详细配置信息以进行编辑。
- [ ] 编辑表单中不应显示明文密码。

## Scenario 6: 成功更新数据源信息

Given: 用户已登录系统
And: 系统中已存在一个名为 "MySQL Staging" 的数据源
And: 用户在 "MySQL Staging" 数据源的编辑页面
When: 用户将主机地址修改为 "staging.db.internal"
And: 用户点击 "保存" 按钮
Then: 系统提示 "数据源更新成功"
And: 数据源列表显示 "MySQL Staging" 的主机为 "staging.db.internal"
And: 如果更新了密码，数据库中存储的新密码是加密的

Acceptance Criteria:

- [ ] 用户可以成功修改现有数据源的配置信息。
- [ ] 更新后的信息应正确反映在系统和数据库中。
- [ ] 更新密码时，新密码应加密存储。

## Scenario 7: 更新不存在的数据源

Given: 用户已登录系统
When: 用户尝试通过 API 或其他非法途径更新一个 ID 不存在的数据源
Then: 系统返回错误信息，例如 "数据源未找到"
And: 系统状态未发生改变

Acceptance Criteria:

- [ ] 尝试更新不存在的数据源时，系统应返回明确的错误信息。

## Scenario 8: 成功删除数据源

Given: 用户已登录系统
And: 系统中已存在一个名为 "Obsolete Oracle" 的数据源
And: 用户在数据源管理页面
When: 用户在 "Obsolete Oracle" 数据源行点击 "删除" 按钮
And: 用户在确认弹窗中点击 "确认"
Then: 系统提示 "数据源删除成功"
And: 数据源列表不再显示 "Obsolete Oracle" 数据源

Acceptance Criteria:

- [ ] 用户可以成功删除不再需要的数据源。
- [ ] 删除后，数据源应从列表中移除。

## Scenario 9: 删除不存在的数据源

Given: 用户已登录系统
When: 用户尝试通过 API 或其他非法途径删除一个 ID 不存在的数据源
Then: 系统返回错误信息，例如 "数据源未找到"
And: 系统状态未发生改变

Acceptance Criteria:

- [ ] 尝试删除不存在的数据源时，系统应返回明确的错误信息。

Feature: 数据源元数据浏览

## Scenario 1: 成功浏览已同步数据源的表列表

Given: 用户已登录系统
And: 系统中已存在数据源 "Synced MySQL"，且已成功同步元数据
And: "Synced MySQL" 包含表 "customers" 和 "products"
When: 用户在元数据浏览界面选择 "Synced MySQL" 数据源
Then: 界面显示 "Synced MySQL" 的表列表，包含 "customers" 和 "products"

Acceptance Criteria:

- [ ] 用户可以选择一个已同步的数据源进行浏览。
- [ ] 系统应正确显示该数据源下所有已同步的表。

## Scenario 2: 成功浏览指定表的列信息

Given: 用户已登录系统
And: 用户正在浏览 "Synced MySQL" 数据源的元数据
And: "Synced MySQL" 的 "customers" 表包含列 "id" (INT), "name" (VARCHAR), "email" (VARCHAR)
When: 用户在表列表中选择 "customers" 表
Then: 界面显示 "customers" 表的列列表，包含 "id", "name", "email" 及其对应的数据类型

Acceptance Criteria:

- [ ] 用户可以选择表列表中的一个表查看其详细列信息。
- [ ] 系统应正确显示该表的所有列及其元数据（如数据类型）。

## Scenario 3: 尝试浏览尚未同步的数据源

Given: 用户已登录系统
And: 系统中存在数据源 "Unsynced PG"，但其元数据尚未同步
When: 用户在元数据浏览界面选择 "Unsynced PG" 数据源
Then: 界面显示提示信息，例如 "请先同步该数据源的元数据"
And: 不显示任何表或列信息

Acceptance Criteria:

- [ ] 对于尚未同步元数据的数据源，系统应提示用户先进行同步。
- [ ] 不应显示不完整或不存在的元数据。

## Scenario 4: 尝试浏览不存在的数据源

Given: 用户已登录系统
And: 用户在元数据浏览界面
When: 用户尝试通过某种方式（如直接访问 URL）浏览一个 ID 为 "ghost-ds" 的不存在的数据源
Then: 系统显示错误信息，例如 "数据源未找到"
And: 不显示任何元数据信息

Acceptance Criteria:

- [ ] 尝试访问不存在的数据源进行浏览时，系统应返回明确的错误。

## Scenario 5: 尝试浏览数据源中不存在的表

Given: 用户已登录系统
And: 用户正在浏览 "Synced MySQL" 数据源的元数据，该数据源包含 "customers" 表
When: 用户尝试通过某种方式（如直接访问 URL 或过时的链接）浏览 "Synced MySQL" 中一个名为 "non_existent_table" 的不存在的表
Then: 系统显示错误信息，例如 "在数据源 'Synced MySQL' 中未找到表 'non_existent_table'"
And: 不显示任何列信息

Acceptance Criteria:

- [ ] 尝试访问数据源中不存在的表时，系统应返回明确的错误。

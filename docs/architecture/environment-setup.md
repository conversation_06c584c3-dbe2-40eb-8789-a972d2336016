# DataScope项目环境设置指南

*版本: 1.0*
*日期: 2025-04-06*

## 目录

1. [开发环境配置](#开发环境配置)
2. [容器化配置](#容器化配置)
3. [监控和可观测性配置](#监控和可观测性配置)
4. [CI/CD流水线配置](#cicd流水线配置)
5. [环境变量配置](#环境变量配置)
6. [多环境支持](#多环境支持)

## 开发环境配置

### 基础环境要求

* Java 17
* Maven 3.9+
* Node.js 18+ 
* MySQL 8.0+
* Redis 7.2+
* IDE推荐: IntelliJ IDEA (后端), VS Code (前端)

### 后端开发环境设置

```bash
# 克隆仓库
git clone https://github.com/your-org/data-scope.git
cd data-scope

# 构建项目
mvn clean install -DskipTests

# 运行开发环境
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 前端开发环境设置

```bash
# 进入前端目录
cd boss-data-scope2

# 安装依赖
npm install

# 开发模式启动(使用Mock数据)
npm run dev

# 开发模式启动(连接实际后端API)
npm run dev:api
```

### 本地开发数据库设置

1. 使用Docker启动本地数据库:

```bash
docker run -d \
  --name datascope-mysql \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=secret \
  -e MYSQL_DATABASE=datascope \
  -v mysql-data:/var/lib/mysql \
  mysql:8.2
```

2. 使用Docker启动本地Redis:

```bash
docker run -d \
  --name datascope-redis \
  -p 6379:6379 \
  redis:7.2
```

## 容器化配置

### Docker配置文件

项目包含了完整的Docker配置：

**后端 Dockerfile**:
```dockerfile
# Build stage
FROM maven:3.9.5-eclipse-temurin-17-focal AS build
WORKDIR /app
COPY . .
RUN mvn clean package -DskipTests

# Run stage
FROM eclipse-temurin:17-jre-focal
WORKDIR /app

# Add Tini for proper signal handling
ENV TINI_VERSION v0.19.0
ADD https://github.com/krallin/tini/releases/download/${TINI_VERSION}/tini /tini
RUN chmod +x /tini
ENTRYPOINT ["/tini", "--"]

# Create non-root user
RUN groupadd -r datascope && useradd -r -g datascope datascope

# Copy application files
COPY --from=build /app/data-scope-app/target/data-scope-app-*.jar app.jar

# Set proper permissions
RUN chown -R datascope:datascope /app

# Switch to non-root user
USER datascope

# Set environment variables
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# Expose ports
EXPOSE 8080

# Start application
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

**前端 Dockerfile**:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY . .

# 构建应用
# RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "run", "dev"]
```

### Docker Compose配置

使用`docker-compose.yml`来配置完整的应用环境:

```yaml
version: "3.8"

services:
  app:
    build: .
    container_name: datascope-app
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=secret
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - datascope-network

  mysql:
    image: mysql:8.2
    container_name: datascope-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=secret
      - MYSQL_DATABASE=datascope
    volumes:
      - mysql-data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - datascope-network

  redis:
    image: redis:7.2
    container_name: datascope-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - datascope-network
      
  web:
     build: ./boss-data-scope2
    container_name: datascope-web
    ports:
      - "8082:3000"
    environment:
      - VITE_API_BASE_URL=http://app:8080/data-scope
      - VITE_USE_MOCK_API=false
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - datascope-network

volumes:
  mysql-data:
  redis-data:

networks:
  datascope-network:
    driver: bridge
```

### 测试环境Docker配置

为测试环境配置的`docker-compose-test.yml`:

```yaml
version: "3.8"

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: test
      MYSQL_DATABASE: test_db
    ports:
      - "3306:3306"
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      interval: 5s
      timeout: 10s
      retries: 5

  db2:
    image: ibmcom/db2:11.5
    environment:
      DB2INST1_PASSWORD: test
      DBNAME: test_db
      LICENSE: accept
    ports:
      - "50000:50000"
    healthcheck:
      test: [ "/bin/sh", "-c", "su - db2inst1 -c 'db2 connect to test_db'" ]
      interval: 5s
      timeout: 10s
      retries: 5

  redis:
    image: redis:6.0
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "redis-cli", "ping" ]
      interval: 5s
      timeout: 10s
      retries: 5
```

## 监控和可观测性配置

DataScope项目集成了完整的监控和可观测性工具:

### Prometheus和Grafana配置

添加以下服务到`docker-compose.yml`:

```yaml
prometheus:
  image: prom/prometheus:v2.45.0
  container_name: datascope-prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
    - prometheus-data:/prometheus
  command:
    - "--config.file=/etc/prometheus/prometheus.yml"
    - "--storage.tsdb.path=/prometheus"
    - "--web.console.libraries=/usr/share/prometheus/console_libraries"
    - "--web.console.templates=/usr/share/prometheus/consoles"
  healthcheck:
    test: ["CMD", "wget", "-q", "--spider", "http://localhost:9090/-/healthy"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s
  restart: unless-stopped
  networks:
    - datascope-network

grafana:
  image: grafana/grafana:10.2.0
  container_name: datascope-grafana
  ports:
    - "3000:3000"
  environment:
    - GF_SECURITY_ADMIN_USER=admin
    - GF_SECURITY_ADMIN_PASSWORD=secret
    - GF_USERS_ALLOW_SIGN_UP=false
  volumes:
    - grafana-data:/var/lib/grafana
    - ./grafana/provisioning:/etc/grafana/provisioning
  depends_on:
    - prometheus
  healthcheck:
    test: ["CMD", "wget", "-q", "--spider", "http://localhost:3000/api/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s
  restart: unless-stopped
  networks:
    - datascope-network
```

### Prometheus配置文件

创建`prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ["localhost:9093"]

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "datascope-app"
    metrics_path: /actuator/prometheus
    static_configs:
      - targets: ["app:8080"]
```

### Spring Boot Actuator配置

在`application.yml`中添加:

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

## CI/CD流水线配置

### GitHub Actions配置

创建`.github/workflows/ci.yml`:

```yaml
name: DataScope CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: maven
    
    - name: Build with Maven
      run: mvn -B package --file pom.xml
    
    - name: Run tests
      run: mvn test
      
    - name: Build and analyze
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Needed to get PR information
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      run: mvn -B verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dsonar.projectKey=datascope
    
    - name: Build and push Docker image
      if: github.event_name != 'pull_request'
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: datascope/app:latest
```

### 前端CI/CD配置

创建`.github/workflows/frontend.yml`:

```yaml
name: Frontend CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
       - 'boss-data-scope2/**'
  pull_request:
    branches: [ main, develop ]
    paths:
       - 'boss-data-scope2/**'

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'boss-data-scope2/package-lock.json'
    
    - name: Install dependencies
      run: cd boss-data-scope2 && npm ci
    
    - name: Run tests
      run: cd boss-data-scope2 && npm test
    
    - name: Build
      run: cd boss-data-scope2 && npm run build
      
    - name: Build and push Docker image
      if: github.event_name != 'pull_request'
      uses: docker/build-push-action@v4
      with:
         context: ./boss-data-scope2
        push: true
        tags: datascope/web:latest
```

## 环境变量配置

### 核心环境变量列表

**后端环境变量**:

| 环境变量 | 描述 | 默认值 |
|----------|------|--------|
| SPRING_PROFILES_ACTIVE | 激活的Spring配置文件 | dev |
| SPRING_DATASOURCE_URL | 数据库连接URL | ************************************* |
| SPRING_DATASOURCE_USERNAME | 数据库用户名 | root |
| SPRING_DATASOURCE_PASSWORD | 数据库密码 | - |
| SPRING_REDIS_HOST | Redis主机名 | localhost |
| SPRING_REDIS_PORT | Redis端口 | 6379 |
| JAVA_OPTS | JVM参数 | -Xms512m -Xmx2g |
| AES_KEY | 用于密码加密的AES密钥 | - |
| SALT | 加密盐值 | - |
| MAX_CONNECTIONS | 最大数据库连接数 | 50 |
| QUERY_TIMEOUT | 查询超时时间(秒) | 30 |
| MAX_DOWNLOAD | 最大下载记录数 | 50000 |
| OPENROUTER_API_KEY | OpenRouter API密钥 | - |
| OPENROUTER_MODEL | 使用的AI模型 | mistralai/mistral-7b-instruct |
| MAX_TOKENS | 最大生成令牌数 | 1000 |
| MAX_REQUESTS | 每秒最大请求数 | 10 |

**前端环境变量**:

| 环境变量 | 描述 | 默认值 |
|----------|------|--------|
| VITE_API_BASE_URL | API基础URL | http://localhost:8080/data-scope |
| VITE_USE_MOCK_API | 是否使用Mock API | false |
| VITE_ENV | 环境名称 | development |
| VITE_APP_VERSION | 应用版本号 | 1.0.0 |
| VITE_BUILD_TIMESTAMP | 构建时间戳 | - |

### 环境变量管理

开发环境:
- 后端: 使用`.env`文件或IDE配置
- 前端: 使用`.env.development`文件

生产环境:
- 使用Docker环境变量
- 使用Kubernetes ConfigMaps和Secrets
- 避免硬编码敏感配置

## 多环境支持

### 支持的环境配置

DataScope支持以下环境配置:

1. **开发环境** (`dev`): 
   - 配置文件: `application-dev.yml`
   - 特点: 内存数据库, 详细日志, API文档启用

2. **测试环境** (`test`): 
   - 配置文件: `application-test.yml`
   - 特点: 测试数据库, 中等日志级别

3. **生产环境** (`prod`): 
   - 配置文件: `application-prod.yml`
   - 特点: 生产数据库, 最小日志级别, API文档禁用

### 配置切换方法

通过以下方式切换环境:

1. 命令行参数:
```
java -jar app.jar --spring.profiles.active=prod
```

2. 环境变量:
```
export SPRING_PROFILES_ACTIVE=prod
```

3. Docker环境变量:
```
docker run -e SPRING_PROFILES_ACTIVE=prod datascope/app
```

4. Kubernetes ConfigMap:
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: datascope-config
data:
  application.yml: |
    spring:
      profiles:
        active: prod
```

## 结论

本文档提供了DataScope项目环境设置的全面指南，包括开发环境配置、容器化配置、监控和可观测性配置、CI/CD流水线配置以及环境变量管理。按照本指南设置环境，可以确保项目在不同环境中正确运行，并实现持续集成和部署。

随着项目的发展，此文档将不断更新以反映最新的环境配置要求和最佳实践。

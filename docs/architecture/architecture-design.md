# DataScope系统架构设计

## 概述

DataScope是一个专为公司内部员工设计的智能化数据发现与查询系统。系统允许用户将不同的数据库系统（如MySQL、DB2）无缝集成为数据源，自动提取和管理元数据，并通过SQL或自然语言进行数据查询。系统特别强调了自然语言转SQL（NL2SQL）的能力，利用OpenRouter的LLM服务降低数据访问门槛。此外，系统支持表关系智能推断与手动维护，以及与低代码平台的深度集成，通过生成标准化的JSON配置和API接口，实现快速的应用开发和数据展示。

## 架构原则

DataScope的架构设计遵循以下原则：

1. **领域驱动设计(DDD)** - 使用DDD方法组织代码，将业务逻辑与技术实现分离
2. **关注点分离** - 清晰地分离不同的系统职责
3. **模块化** - 通过定义明确的模块边界实现高内聚低耦合
4. **可扩展性** - 设计灵活的架构以支持新的数据源类型和功能
5. **安全优先** - 在设计的各个层面考虑安全性
6. **可测试性** - 架构支持各个层次的自动化测试

## 1. 核心业务模型和数据实体分析

### 1.1 核心业务模型

DataScope系统围绕以下核心业务流程展开：

1. **数据源注册与管理**：用户注册外部数据库系统（MySQL、DB2）作为数据源，配置连接信息和同步策略。
2. **元数据提取与维护**：系统自动从已注册的数据源中提取元数据信息并持久化存储，支持增量更新。
3. **数据查询**：用户通过SQL或自然语言描述来查询数据源中的数据。
4. **表关系管理**：系统自动推断或允许用户手动定义表之间的关联关系。
5. **页面配置生成**：为低代码平台生成符合规范的页面配置JSON和API接口。

### 1.2 核心数据实体

#### 数据源实体 (DataSource)

- ID、名称、描述
- 类型（MySQL/DB2）
- 连接信息（URL、端口、用户名）
- 密码（AES-GCM加密存储）
- 状态（活跃/非活跃）
- 同步配置（频率、上次同步时间）
- 创建时间、更新时间

#### 元数据实体

- **Schema**: ID、数据源ID、名称、描述
- **Table**: ID、Schema ID、名称、类型、描述、行数估计、上次更新时间
- **Column**: ID、Table ID、名称、数据类型、长度、精度、可空性、默认值、描述、位置、是否敏感数据标记
- **Index**: ID、Table ID、名称、类型（主键/唯一/普通）、包含的列、创建时间
- **MetadataSyncJob**: ID、数据源ID、开始时间、结束时间、状态、增量/全量标记、日志

#### 表关系实体 (TableRelation)

- ID、源表ID、目标表ID
- 关系类型（一对一、一对多、多对多）
- 源列ID数组、目标列ID数组
- 推断方式（自动/手动）
- 可信度得分（0-100）
- 使用频率、上次使用时间
- 创建时间、更新时间

#### 查询实体 (Query)

- ID、用户ID、数据源ID
- 查询类型（SQL/自然语言）
- SQL文本、自然语言描述
- 执行状态、执行结果
- 是否收藏、收藏时间
- 执行时间、影响行数
- 创建时间、最后执行时间
- 超时设置（默认30秒）

#### 页面配置实体 (PageConfig)

- ID、名称、描述
- 关联查询ID
- 版本号、状态（草稿/发布）
- 查询条件配置（JSON）
- 结果展示配置（JSON）
- 操作列配置（JSON）
- 敏感数据掩码规则
- 自动隐藏不常用条件设置
- 创建时间、更新时间

#### 用户实体 (User)

- ID、用户名、邮箱
- 查询配额设置
- 配额使用统计
- 上次活动时间
- 创建时间、更新时间

## 2. 系统整体架构图

```mermaid
graph TB
    subgraph "前端层"
        UI[用户界面]
        subgraph "前端组件"
            DSM[数据源管理界面]
            QM[查询管理界面]
            PC[页面配置工具]
            RM[关系管理界面]
        end
    end

    subgraph "API网关层"
        AG[API网关]
    end

    subgraph "应用服务层"
        DSS[数据源服务]
        QS[查询服务]
        MDS[元数据服务]
        RS[关系服务]
        PCS[页面配置服务]
        NLPS[自然语言处理服务]
    end

    subgraph "领域层"
        DSDM[数据源领域模型]
        QDM[查询领域模型]
        MDM[元数据领域模型]
        RDM[关系领域模型]
        PCDM[页面配置领域模型]
    end

    subgraph "基础设施层"
        DS[(数据源适配器)]
        MR[(元数据仓库)]
        QR[(查询仓库)]
        RR[(关系仓库)]
        PCR[(页面配置仓库)]
        RC[(Redis缓存)]
        LLM[OpenRouter LLM]
    end

    subgraph "外部系统"
        EDB1[(MySQL数据源)]
        EDB2[(DB2数据源)]
        LCDP[低代码平台]
    end

    UI --> DSM & QM & PC & RM
    DSM & QM & PC & RM --> AG

    AG --> DSS & QS & MDS & RS & PCS & NLPS

    DSS --> DSDM
    QS --> QDM
    MDS --> MDM
    RS --> RDM
    PCS --> PCDM
    NLPS --> QDM

    DSDM --> DS & MR
    QDM --> QR & RC
    MDM --> MR & RC
    RDM --> RR & RC
    PCDM --> PCR

    NLPS --> LLM
    DS --> EDB1 & EDB2
    PCS --> LCDP
    QS --> EDB1 & EDB2
```

## 3. 主要模块划分及职责

### 3.1 数据源管理模块

**职责**：

- 管理数据源的注册、编辑、删除和状态监控
- 安全存储数据源连接信息（使用AES-GCM加密密码）
- 提供数据源连接测试功能
- 管理数据源同步策略和状态
- 处理最多100个数据源的同步

**核心组件**：

- `DataSourceController`: 提供数据源管理REST API
- `DataSourceService`: 处理数据源业务逻辑
- `DataSourceRepository`: 数据源持久化
- `EncryptionService`: 密码加密解密服务
- `ConnectionTestService`: 连接测试服务

### 3.2 元数据提取和同步模块

**职责**：

- 从数据源提取元数据信息，包括Schema/Table/Column/Index
- 支持全量和增量同步策略（24小时同步周期）
- 存储和维护元数据
- 提供元数据查询接口
- 管理同步任务和日志

**核心组件**：

- `MetadataController`: 提供元数据查询REST API
- `MetadataService`: 处理元数据业务逻辑
- `MetadataExtractor`: 负责从不同数据源提取元数据
- `MySQLMetadataExtractor`: MySQL元数据提取实现
- `DB2MetadataExtractor`: DB2元数据提取实现
- `SyncTaskScheduler`: 同步任务调度器
- `IncrementalSyncService`: 增量同步服务

### 3.3 查询管理模块

**职责**：

- 管理用户SQL和自然语言查询
- 执行SQL查询并返回结果
- 验证和优化SQL
- 管理查询历史和收藏
- 控制查询频率和超时（默认30秒）
- 支持查询结果CSV导出（最多50000条）

**核心组件**：

- `QueryController`: 提供查询相关REST API
- `QueryService`: 处理查询业务逻辑
- `QueryExecutor`: 执行SQL查询
- `QueryValidator`: 验证SQL语法和安全性
- `QueryOptimizer`: 优化SQL语句
- `RateLimiter`: 查询频率限制
- `TimeoutManager`: 查询超时控制
- `CSVExportService`: 查询结果导出服务

### 3.4 自然语言处理模块

**职责**：

- 将自然语言查询转换为SQL语句
- 利用元数据和表关系信息提高转换准确性
- 与OpenRouter LLM服务集成
- 支持多轮对话和上下文管理
- 提供SQL微调建议

**核心组件**：

- `NLQueryController`: 提供自然语言查询REST API
- `NLQueryService`: 处理自然语言查询业务逻辑
- `NLToSQLConverter`: 自然语言到SQL转换器
- `PromptGenerator`: 生成LLM提示
- `LLMIntegrationService`: 与OpenRouter集成
- `ContextManager`: 管理对话上下文
- `SQLFeedbackService`: 处理SQL微调反馈
- `LLMConfigManager`: 管理LLM接口的请求地址、API密钥和模型参数

### 3.5 表关系推断模块

**职责**：

- 通过查询历史和数据分析推断表间关系
- 管理手动定义的表关系
- 提供关系可视化
- 支持关系查询和使用
- 自动识别外键或通过数据分析推断关联

**核心组件**：

- `RelationController`: 提供关系管理REST API
- `RelationService`: 处理关系业务逻辑
- `RelationInferenceEngine`: 关系推断引擎
- `QueryHistoryAnalyzer`: 分析查询历史推断关系
- `DataContentAnalyzer`: 分析数据内容推断关系
- `RelationRepository`: 关系持久化
- `RelationVisualizer`: 关系可视化组件

### 3.6 页面配置模块

**职责**：

- 生成和管理查询相关的页面配置
- 支持查询条件和结果展示配置
- 转换配置为特定格式的JSON
- 管理配置版本
- 提供AI辅助配置功能
- 支持敏感数据掩码配置
- 自动隐藏不常用查询条件

**核心组件**：

- `PageConfigController`: 提供页面配置REST API
- `PageConfigService`: 处理页面配置业务逻辑
- `ConfigGenerator`: 生成页面配置
- `AIConfigAssistant`: AI辅助配置
- `DataTypeUIMapper`: 数据类型到UI组件映射
- `ConfigRepository`: 配置持久化
- `VersionManager`: 配置版本管理
- `MaskingRuleManager`: 敏感数据掩码规则管理
- `DynamicConditionManager`: 动态查询条件管理

### 3.7 系统集成模块

**职责**：

- 提供低代码平台集成API
- 管理API版本
- 处理数据查询请求
- 转换和格式化查询结果
- 支持多种数据呈现形式（查询表单、查看页面、图表展示）

**核心组件**：

- `IntegrationController`: 提供集成REST API
- `IntegrationService`: 处理集成业务逻辑
- `APIVersionManager`: API版本管理
- `ResultFormatter`: 结果格式化
- `CSVExporter`: CSV导出功能
- `LowCodeConfigManager`: 低代码配置管理
- `DisplayTemplateManager`: 展示模板管理

## 4. 数据流向图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Gateway as API网关
    participant NLP as 自然语言处理服务
    participant LLM as OpenRouter LLM
    participant MetaSvc as 元数据服务
    participant RelSvc as 关系服务
    participant QuerySvc as 查询服务
    participant DataSourceAdapter as 数据源适配器
    participant ExternalDB as 外部数据源

    User->>Frontend: 输入自然语言查询
    Frontend->>Gateway: 发送查询请求
    Gateway->>NLP: 转发到NLP服务

    NLP->>MetaSvc: 获取相关元数据
    MetaSvc-->>NLP: 返回元数据

    NLP->>RelSvc: 获取表关系
    RelSvc-->>NLP: 返回表关系

    NLP->>LLM: 调用LLM生成SQL
    LLM-->>NLP: 返回生成的SQL

    NLP->>NLP: 验证和优化SQL
    NLP-->>Gateway: 返回SQL和微调建议
    Gateway-->>Frontend: 显示SQL和微调建议

    User->>Frontend: 确认或微调SQL
    Frontend->>Gateway: 发送执行请求
    Gateway->>QuerySvc: 转发到查询服务

    QuerySvc->>DataSourceAdapter: 执行SQL查询
    DataSourceAdapter->>ExternalDB: 在外部数据源执行
    ExternalDB-->>DataSourceAdapter: 返回查询结果
    DataSourceAdapter-->>QuerySvc: 返回处理后的结果

    QuerySvc->>QuerySvc: 格式化和分页处理
    QuerySvc-->>Gateway: 返回最终结果
    Gateway-->>Frontend: 显示查询结果

    User->>Frontend: 请求下载CSV
    Frontend->>Gateway: 发送导出请求
    Gateway->>QuerySvc: 请求CSV导出
    QuerySvc-->>Gateway: 返回CSV数据
    Gateway-->>Frontend: 提供CSV下载
```

## 5. 主要API接口规范

### 5.1 API设计原则

1. RESTful设计风格
2. 统一的JSON响应格式
3. 明确的版本化策略（URL路径版本：/api/v1/...）
4. 规范的错误处理（HTTP状态码 + 错误消息）
5. 支持分页、排序和过滤
6. 适当的缓存策略
7. 严格的接口文档（使用Swagger/OpenAPI）

### 5.2 核心API接口

#### 数据源管理API

```
# 获取数据源列表
GET /api/v1/datasources
# 创建数据源
POST /api/v1/datasources
# 获取数据源详情
GET /api/v1/datasources/{id}
# 更新数据源
PUT /api/v1/datasources/{id}
# 删除数据源
DELETE /api/v1/datasources/{id}
# 测试数据源连接
POST /api/v1/datasources/{id}/test-connection
# 触发数据源元数据同步
POST /api/v1/datasources/{id}/sync
# 获取数据源同步状态
GET /api/v1/datasources/{id}/sync-status
```

#### 元数据查询API

```
# 获取数据源的schema列表
GET /api/v1/datasources/{id}/schemas
# 获取schema的表列表
GET /api/v1/schemas/{id}/tables
# 获取表的列列表
GET /api/v1/tables/{id}/columns
# 获取表的索引列表
GET /api/v1/tables/{id}/indexes
# 搜索元数据（支持关键词搜索）
GET /api/v1/metadata/search?keyword={keyword}
```

#### 查询管理API

```
# 执行SQL查询
POST /api/v1/queries/sql
# 自然语言转SQL
POST /api/v1/queries/nl-to-sql
# 执行自然语言查询
POST /api/v1/queries/nl
# 获取查询历史
GET /api/v1/queries/history
# 收藏查询
POST /api/v1/queries/{id}/favorite
# 取消收藏
DELETE /api/v1/queries/{id}/favorite
# 获取收藏的查询
GET /api/v1/queries/favorites
# 导出查询结果为CSV
GET /api/v1/queries/{id}/export-csv
# 设置查询超时时间
PUT /api/v1/queries/{id}/timeout
```

#### 表关系管理API

```
# 获取表的关系列表
GET /api/v1/tables/{id}/relations
# 创建表关系
POST /api/v1/relations
# 更新表关系
PUT /api/v1/relations/{id}
# 删除表关系
DELETE /api/v1/relations/{id}
# 自动推断表关系
POST /api/v1/tables/{id}/infer-relations
# 获取关系推断任务状态
GET /api/v1/relations/inference-tasks/{taskId}
# 获取表关系可视化数据
GET /api/v1/tables/{id}/relations-visualization
```

#### 页面配置API

```
# 获取页面配置列表
GET /api/v1/page-configs
# 创建页面配置
POST /api/v1/page-configs
# 获取页面配置详情
GET /api/v1/page-configs/{id}
# 更新页面配置
PUT /api/v1/page-configs/{id}
# 删除页面配置
DELETE /api/v1/page-configs/{id}
# 发布页面配置
POST /api/v1/page-configs/{id}/publish
# AI辅助生成页面配置
POST /api/v1/page-configs/ai-generate
# 获取页面配置历史版本
GET /api/v1/page-configs/{id}/versions
# 回滚到指定版本
POST /api/v1/page-configs/{id}/rollback/{versionId}
# 下载页面配置JSON
GET /api/v1/page-configs/{id}/download
# 配置敏感数据掩码规则
POST /api/v1/page-configs/{id}/masking-rules
# 配置自动隐藏条件规则
POST /api/v1/page-configs/{id}/auto-hide-rules
```

#### 低代码平台集成API

```
# 按ID获取页面配置JSON
GET /api/v1/integration/page-configs/{id}
# 按版本获取页面配置JSON
GET /api/v1/integration/page-configs/{id}/versions/{versionId}
# 执行页面配置对应的查询
POST /api/v1/integration/execute-query
# 获取数据类型到UI组件的映射规则
GET /api/v1/integration/datatype-ui-mappings
# 获取可用的数据展示模板
GET /api/v1/integration/display-templates
```

## 6. 项目目录结构建议

### 6.1 后端项目结构

```
data-scope/
├── data-scope-app/                  # 应用层 - 处理应用服务和用例
│   ├── src/main/java/com/datascope/app/
│   │   ├── exception/               # 异常处理
│   │   ├── datasource/              # 数据源应用服务
│   │   │  ├── controller/           # REST控制器
│   │   │  ├── dto/                  # 数据传输对象
│   │   │  │  ├── request/
│   │   │  │  └── response/
│   │   ├── metadata/                # 元数据应用服务
│   │   ├── query/                   # 查询应用服务
│   │   ├── nlp/                     # 自然语言处理应用服务
│   │   ├── relation/                # 关系应用服务
│   │   └── pageconfig/              # 页面配置应用服务
│   ├── src/test/
│   └── pom.xml
├── data-scope-domain/               # 领域层 - 包含核心业务逻辑和领域模型
│   ├── src/main/java/com/datascope/domain/
│   │   ├── datasource/              # 数据源领域模型
│   │   │   ├── model/               # 实体和值对象
│   │   │   ├── repository/          # 仓储接口
│   │   │   ├── service/             # 领域服务
│   │   │   ├── event/               # 领域事件
│   │   │   └── exception/           # 领域异常
│   │   ├── metadata/                # 元数据领域模型
│   │   ├── query/                   # 查询领域模型
│   │   ├── relation/                # 关系领域模型
│   │   └── pageconfig/              # 页面配置领域模型
│   ├── src/test/
│   └── pom.xml
├── data-scope-infrastructure/       # 基础设施层 - 实现技术细节
│   ├── src/main/java/com/datascope/infrastructure/
│   │   ├── config/                  # 配置类
│   │   ├── persistence/             # 持久化实现
│   │   │   ├── mybatis/             # MyBatis相关配置和Mapper
│   │   │   └── repository/          # 仓储实现
│   │   ├── datasource/              # 数据源适配器
│   │   ├── cache/                   # Redis缓存实现
│   │   ├── nlp/                     # LLM集成实现
│   │   ├── cache/                   # 缓存实现
│   │   ├── security/                # 安全相关实现
│   │   └── util/                    # 工具类
│   ├── src/main/resources/
│   │   └── mybatis/                 # MyBatis XML映射文件
│   ├── src/test/
│   └── pom.xml
├── data-scope-main/                 # 应用入口和Web层
│   ├── src/main/java/com/datascope/
│   │   ├── security/                # 安全配置
│   │   ├── config/                  # 配置
│   │   └── DataScopeApplication.java # 主应用类
│   ├── src/main/resources/
│   │   ├── application.yml          # 应用配置
│   │   ├── application-dev.yml      # 开发环境配置
│   │   ├── application-prod.yml     # 生产环境配置
│   │   └── db/migration/            # Flyway迁移脚本
│   ├── src/test/
│   └── pom.xml
├── docs/                            # 项目文档
│   ├── api/                         # API文档
│   ├── architecture/                # 架构文档
│   └── guides/                      # 用户指南
├── docker/                          # Docker相关文件
│   ├── Dockerfile
│   └── docker-compose.yml
└── pom.xml                          # 父POM文件
```

### 6.2 前端项目结构

```
boss-data-scope2/
├── public/                         # 静态资源
│   ├── favicon.ico
│   ├── index.html
│   └── assets/                     # 图片等静态资源
│       └── images/
│           └── logo.png
├── src/
│   ├── App.vue                     # 根组件
│   ├── main.ts                     # 入口文件 (TypeScript)
│   ├── style.css                   # 全局样式
│   ├── vite-env.d.ts               # Vite 环境变量类型定义
│   ├── assets/                     # 资源文件
│   │   ├── vue.svg                 # Vue Logo
│   │   └── images/                 # 图片文件 (重复, 可考虑合并)
│   │       └── logo.png
│   ├── components/                 # 公共组件
│   │   ├── HelloWorld.vue          # 示例组件
│   │   ├── common/                 # 通用UI组件
│   │   │   ├── BaseForm.vue
│   │   │   ├── ConfirmModal.vue
│   │   │   ├── DataTable.vue
│   │   │   ├── LoadingSpinner.vue
│   │   │   ├── MessageAlert.vue
│   │   │   └── MessageNotification.vue
│   │   ├── datasource/             # 数据源相关组件
│   │   │   ├── AdvancedSearch.vue
│   │   │   ├── DataSourceDetail.vue
│   │   │   ├── DataSourceForm.vue
│   │   │   ├── DataSourceList.vue
│   │   │   ├── SearchResultsView.vue
│   │   │   └── TableDataPreview.vue
│   │   ├── home/                   # 首页相关组件
│   │   │   ├── CTASection.vue
│   │   │   ├── FeatureCard.vue
│   │   │   ├── FeaturesSection.vue
│   │   │   └── HeroSection.vue
│   │   ├── integration/            # 低代码集成相关组件
│   │   │   ├── DataSourceSelector.vue
│   │   │   ├── FormConfigEditor.vue
│   │   │   ├── IntegrationDebug.vue
│   │   │   ├── IntegrationPointEditor.vue
│   │   │   ├── QueryParamsConfig.vue
│   │   │   ├── QueryPreview.vue
│   │   │   ├── QuerySelector.vue
│   │   │   ├── QuerySelectorEnhanced.vue
│   │   │   ├── TableConfigEditor.vue
│   │   │   ├── chartmode/
│   │   │   │   └── ChartPreview.vue
│   │   │   ├── preview/
│   │   │   │   ├── ChartView.vue
│   │   │   │   ├── QueryForm.vue
│   │   │   │   └── TableView.vue
│   │   │   └── tablemode/
│   │   │       ├── FormConfigTable.vue
│   │   │       ├── IntegrationPointTable.vue
│   │   │       ├── TableConfigTable.vue
│   │   │       └── TableModeToggle.vue
│   │   ├── layout/                 # 布局组件
│   │   │   ├── TheFooter.vue
│   │   │   └── TheNavigation.vue
│   │   └── query/                  # 查询相关组件
│   │       ├── ChartConfig.vue
│   │       ├── ChartView.vue
│   │       ├── MetadataExplorer.vue
│   │       ├── NaturalLanguageQuery.vue
│   │       ├── QueryAnalysis.vue
│   │       ├── QueryBuilder.vue
│   │       ├── QueryEditor.vue
│   │       ├── QueryExecutionPlan.vue
│   │       ├── QueryHistory.vue
│   │       ├── QueryManager.vue
│   │       ├── QueryResults.vue
│   │       ├── QueryResultTable.vue
│   │       ├── QuerySuggestions.vue
│   │       ├── QueryVisualization.vue
│   │       ├── SaveQueryModal.vue
│   │       ├── SqlEditor.vue
│   │       ├── builder/              # 查询构建器子组件
│   │       │   ├── ConditionBuilder.vue
│   │       │   ├── ConditionGroup.vue
│   │       │   ├── ExpressionEditor.vue
│   │       │   ├── FieldSelector.vue
│   │       │   └── TableSelector.vue
│   │       └── detail/               # 查询详情子组件
│   │           ├── QueryDetailHeader.vue
│   │           └── QueryDetailTabs.vue
│   ├── mocks/                      # Mock API 数据
│   │   ├── datasource.ts
│   │   └── query.ts
│   ├── plugins/                    # Vue 插件
│   │   ├── antd-locale.ts
│   │   ├── dayjs.ts
│   │   └── echarts.ts
│   ├── router/                     # 路由配置
│   │   └── index.ts                # 路由配置 (TypeScript)
│   ├── services/                   # API 服务层
│   │   ├── api.ts
│   │   ├── datasource.ts
│   │   ├── integrationService.ts
│   │   ├── loading.ts
│   │   ├── message.ts
│   │   ├── mock-query.ts
│   │   ├── mockData.ts
│   │   ├── modal.ts
│   │   ├── query.ts
│   │   └── queryTemplates.ts
│   ├── stores/                     # Pinia 状态管理
│   │   ├── datasource.ts
│   │   ├── integration.ts
│   │   ├── message.ts
│   │   ├── query.ts
│   │   ├── system.ts
│   │   └── user.ts
│   ├── styles/                     # 全局样式
│   │   └── index.css
│   ├── types/                      # TypeScript 类型定义
│   │   ├── datasource.ts
│   │   ├── form.ts
│   │   ├── integration.ts
│   │   ├── message.ts
│   │   ├── metadata.ts
│   │   ├── modal.ts
│   │   ├── query.ts
│   │   ├── table.ts
│   │   ├── user.ts
│   │   ├── builder/
│   │   │   └── index.ts
│   │   └── integration/
│   │       └── index.ts
│   ├── utils/                      # 工具函数
│   │   ├── apiTransformer.ts
│   │   ├── configConverter.ts
│   │   ├── formatter.ts
│   │   ├── request.ts
│   │   └── typeMapping.ts
│   └── views/                      # 页面视图
│       ├── HomeView.vue
│       ├── datasource/
│       │   └── DataSourceView.vue
│       ├── examples/               # 示例页面
│       │   ├── ExamplesIndex.vue
│       │   ├── FormExample.vue
│       │   ├── LoadingExample.vue
│       │   ├── MessageExample.vue
│       │   ├── ModalExample.vue
│       │   ├── TableExample.vue
│       │   └── TestView.vue
│       ├── integration/            # 低代码集成页面
│       │   ├── FullIntegrationEdit.vue
│       │   ├── IntegrationEdit.vue
│       │   ├── IntegrationList.vue
│       │   ├── IntegrationPreview.vue
│       │   ├── IntegrationPreview.vue.original
│       │   ├── MinimalPage.vue
│       │   ├── SimpleIntegrationEdit.vue
│       │   └── SimplifiedIntegrationEdit.vue
│       ├── query/                  # 查询相关页面
│       │   ├── QueryAnalytics.vue
│       │   ├── QueryDetail.vue
│       │   ├── QueryEditor.vue
│       │   ├── QueryHistory.vue
│       │   ├── QueryList.vue
│       │   └── QueryView.vue
│       └── settings/               # 设置页面
│           └── SettingsView.vue
├── .eslintrc.cjs                   # ESLint配置
├── .gitignore                      # Git忽略文件
├── tsconfig.json                   # TypeScript配置
├── package.json                    # 依赖配置
├── tailwind.config.cjs             # Tailwind CSS配置
└── vite.config.ts                  # Vite 配置 (替代 vue.config.ts)
```

## 7. 技术选型理由及版本建议

### 7.1 后端技术栈

| 技术              | 推荐版本   | 选择理由                            |
|-----------------|--------|---------------------------------|
| Java            | 17 LTS | 长期支持版本，提供了许多现代Java特性，性能和安全性更好   |
| Spring Boot     | 3.2.x  | 简化Spring应用开发，自动配置，内嵌服务器，支持响应式编程 |
| MyBatis         | 3.5.x  | 灵活的SQL映射，与Spring Boot集成良好，控制力强  |
| MySQL           | 8.0.x  | 稳定可靠，广泛使用，支持JSON和窗口函数等现代特性      |
| Redis           | 7.0.x  | 高性能缓存，支持多种数据结构，适合缓存元数据和查询结果     |
| Maven           | 3.9.x  | 依赖管理工具，构建管理，模块化支持               |
| Swagger/OpenAPI | 3.0.x  | REST API文档自动生成，简化API开发和测试       |
| Jackson         | 2.15.x | JSON序列化/反序列化，与Spring Boot默认集成   |

### 7.2 前端技术栈

| 技术            | 推荐版本   | 选择理由                                  |
|---------------|--------|---------------------------------------|
| TypeScript    | 5.3.x  | 强类型语言，提高代码健壮性和可维护性，与Vue 3生态良好集成       |
| Vue           | 3.3.x  | 响应式数据绑定，组件化开发，对TypeScript支持良好         |
| Vue Router    | 4.2.x  | Vue官方路由，与Vue 3和TypeScript完全集成         |
| Pinia         | 2.1.x  | Vue 3官方推荐的状态管理库，提供优秀的TypeScript支持     |
| Axios         | 1.6.x  | 基于Promise的HTTP客户端，提供类型定义文件            |
| Tailwind CSS  | 3.4.x  | 实用优先的CSS框架，高度可定制，开发效率高                |
| Font Awesome  | 6.5.x  | 丰富的图标库，易于使用和定制                        |
| Vue Query     | 5.0.x  | 数据获取和缓存库，提供良好的TypeScript支持，提升用户体验     |
| Monaco Editor | 0.45.x | VS Code的编辑器核心，适合SQL编辑器实现，TypeScript编写 |
| ECharts       | 5.4.x  | 功能强大的图表库，适合关系可视化和数据展示，提供类型定义          |
| vue-i18n      | 9.2.x  | 国际化支持，方便未来扩展多语言，支持TypeScript          |

### 7.3 DevOps工具

| 技术             | 推荐版本    | 选择理由                 |
|----------------|---------|----------------------|
| Docker         | 24.x    | 容器化部署，环境一致性，隔离性好     |
| Docker Compose | 2.23.x  | 多容器应用编排，简化开发和部署流程    |
| Jenkins        | 2.414.x | 自动化构建、测试和部署，CI/CD支持  |
| Prometheus     | 2.48.x  | 监控指标收集，适合监控查询性能和资源使用 |
| Grafana        | 10.2.x  | 可视化监控数据，直观展示系统状态     |

## 8. 可能的性能瓶颈及解决方案

### 8.1 数据源元数据同步性能

**潜在问题**：

- 大型数据库的元数据提取可能耗时，尤其是全量同步
- 增量同步策略可能不精确，导致不必要的更新
- 多个数据源同时同步可能导致系统资源竞争
- 每个数据源最多有100个表，每个数据源有不超过100个表，但表的数据量可能达到几亿级别

**解决方案**：

1. **采用分布式任务调度**：使用Spring Batch或Quartz配合Redis实现分布式调度
2. **增量同步优化**：基于时间戳和校验和的高效增量同步算法
3. **优先级同步队列**：根据数据源优先级和使用频率安排同步顺序
4. **异步处理**：使用Spring的@Async注解或CompletableFuture异步处理同步任务
5. **资源限制**：设置最大并发同步任务数，避免资源耗尽
6. **定向同步**：支持选择性同步特定Schema或Table，而非整个数据源
7. **智能调度**：在系统负载较低时执行同步任务，默认24小时同步周期

### 8.2 查询执行性能

**潜在问题**：

- 复杂SQL查询可能导致长时间执行
- 大结果集处理内存消耗大
- 外部数据源响应慢或不稳定
- 多用户并发查询导致系统负载高

**解决方案**：

1. **查询超时控制**：实现可配置的查询超时机制（默认30秒）
2. **结果分页处理**：强制分页返回结果，避免一次性加载大量数据
3. **查询优化建议**：集成SQL优化器，提供执行计划分析和优化建议
4. **连接池管理**：使用HikariCP等高性能连接池，合理配置连接池大小
5. **查询缓存**：对频繁执行的相同查询进行结果缓存
6. **慢查询日志**：记录并分析慢查询，帮助优化
7. **用户查询配额**：实现基于Redis的速率限制器，控制单用户查询频率
8. **CSV导出限制**：限制单次导出数据量不超过50000条

### 8.3 自然语言到SQL转换性能

**潜在问题**：

- LLM API调用延迟高
- 复杂的自然语言理解可能需要多次API调用
- 大量元数据和关系信息可能导致提示过长，超出LLM上下文窗口

**解决方案**：

1. **提示优化**：设计高效紧凑的提示模板，减少LLM输入长度
2. **元数据筛选**：基于自然语言内容智能筛选相关的表和列，只传递必要信息
3. **缓存常见查询**：缓存常见自然语言查询的SQL转换结果
4. **分层转换**：先确定查询意图和涉及的表，再分步构建复杂SQL
5. **本地预处理**：使用本地NLP组件预处理自然语言，减轻LLM负担
6. **请求批处理**：在高负载时实现请求队列和批处理机制
7. **备用LLM**：配置多个LLM提供商作为备选，实现自动故障转移
8. **LLM参数配置**：允许在配置文件中维护和调整LLM接口参数

### 8.4 关系推断性能

**潜在问题**：

- 基于数据内容分析的关系推断计算密集且耗时
- 对大表进行数据分析可能导致数据源负载增加
- 关系推断算法准确性与性能的平衡难以把握

**解决方案**：

1. **限制分析范围**：只分析表的样本数据，而非全表数据
2. **调度优化**：将关系推断任务安排在数据源低负载时段执行
3. **Progressive分析**：先使用轻量级算法快速推断，必要时再使用复杂算法深入分析
4. **缓存中间结果**：存储和复用分析的中间结果
5. **基于历史查询的优先推断**：优先分析被频繁查询的表之间的关系
6. **异步批处理**：将推断任务设计为异步长时间运行的批处理任务
7. **关系信息持久化**：将推断出的表关系持久化存储，避免重复计算

## 9. 系统安全考量

### 9.1 数据源密码保护

- 使用AES-GCM模式加密存储数据源密码
- 密钥管理与轮转策略
- 在内存中安全处理密码，避免日志泄露
- 密码解密仅在需要连接数据源时执行

### 9.2 SQL注入防护

- 对所有用户直接输入的SQL进行严格验证
- 使用参数化查询而非字符串拼接
- 实现SQL白名单过滤机制
- 限制允许的SQL操作类型（仅允许SELECT）
- 对LLM生成的SQL也进行安全校验

### 9.3 自然语言安全风险

- 防止通过精心构造的自然语言生成恶意SQL
- 对LLM生成的SQL强制检查，禁止执行非查询操作
- 提示安全原则，避免在提示中包含敏感数据
- 实现提示注入防护机制

### 9.4 API安全

- 实现适当的认证和授权机制
- API访问速率限制和防DoS措施
- 敏感API的访问日志审计
- 适当的CORS配置
- 请求参数验证和响应数据过滤

### 9.5 敏感数据保护

- 支持查询结果中的敏感数据掩码
- 敏感列自动识别和标记
- 日志中敏感信息的脱敏处理
- 实现细粒度的数据访问控制
- 对特定类型的数据（如个人信息）实现自动识别和保护
- 提供"查看"按钮功能，允许授权用户查看原始敏感数据

### 9.6 通信安全

- API和前端通信使用HTTPS
- 与外部LLM提供商的通信加密
- 与数据源的连接使用TLS/SSL（如可能）
- WebSocket连接的安全处理

### 9.7 容器和部署安全

- 容器镜像安全扫描
- 最小权限原则的容器配置
- 定期更新基础镜像和依赖
- 运行时环境的安全加固
- 机密信息使用Docker Secrets或环境变量管理

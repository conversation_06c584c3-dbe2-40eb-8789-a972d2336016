# DataScope

DataScope是一个全面的数据管理和查询系统，支持多数据源集成、智能数据发现和低代码平台集成。

## 功能特性

### 数据源管理

- 支持MySQL、DB2等多种数据库系统
- 自动化元数据提取和同步
- 数据源健康监控
- 密码加密存储

### 智能数据发现

- 直观的数据浏览界面
- 自然语言查询支持
- 智能表关系推断
- SQL查询优化建议

### 低代码集成

- 标准化集成协议
- 灵活的界面配置
- AI辅助配置生成
- 多种展示形式支持
  - 表格集成：支持高级过滤、排序、分页等功能
  - 简单表格：轻量级数据展示，无需复杂配置
  - 可视化图表：增强的图表绘制支持
    - 柱状图、折线图、饼图、散点图、面积图等多种图表类型
    - 灵活的数据映射和图表属性配置
    - 智能的字段推荐与实时预览功能
    - 严格的数据校验，保证图表渲染安全
    - 全新的交互设计，根据图表类型智能适配配置选项
    - 多主题支持，适配不同应用场景
- 集成预览系统
  - 组件化架构，提高代码复用与维护性
  - 强类型设计，确保代码质量与稳定性
  - 查询表单组件支持多种输入类型与验证
  - 表格视图组件提供排序、分页与导出功能
  - 图表视图组件基于ECharts实现丰富的可视化效果
  - 全新的用户界面设计，采用Ant Design Vue组件库
  - 完善的状态管理，优雅处理加载、错误与空数据状态
  - 高质量UI组件，提供专业的数据展示与交互体验
  - 统一设计语言，所有预览组件(表单、表格、图表)保持一致风格
  - UI框架升级：
    - 从原生HTML/CSS升级到Ant Design Vue组件库
    - 表格组件(TableView)重构，使用Ant Design Table实现高级功能
    - 表单组件(QueryForm)升级，采用Ant Design Form系统提供更好的验证体验
    - 图表容器(ChartView)美化，使用Ant Design Card组件提供统一的样式
    - 保留ECharts作为图表渲染引擎，确保高质量的数据可视化能力
    - 统一的加载状态处理，使用Ant Design Spin组件提供友好的加载反馈
    - 完善的空状态处理，使用Ant Design Empty组件展示无数据状态
    - 添加可折叠的调试信息面板，便于开发调试
    - 优化响应式布局，提供在各种设备上的良好体验
    - 日期组件优化，解决跨浏览器兼容性问题
- 三大模块配置体系
  - 数据条件配置：
    - 标准化的查询参数接口定义
    - 支持多种数据类型和表单类型
    - 高级配置选项包括数字范围限制、日期格式、验证规则等
    - 参数默认值与选项列表管理
  - 表格配置：
    - 完整的列定义、操作按钮、分页控制
    - 支持多种显示类型，适配不同数据场景
    - 高级表格功能如导出、批量操作、聚合计算
    - 自定义过滤条件与保存管理
  - 图表配置：
    - 标准化图表类型与主题定义
    - 完善的数据映射体系，灵活连接数据与图表元素
    - 丰富的样式选项与交互控制
    - 支持自定义颜色、背景、字体等视觉元素

### 元数据管理

DataScope提供高性能的元数据管理功能，支持：

- 多数据源支持
- MySQL 8.0+
- DB2 11.5+

- 核心特性
- 高性能元数据提取
- 智能缓存管理
- 批量处理优化
- 实时监控

- 性能指标
- 单表提取 < 5s
- 查询延迟 < 50ms
- 缓存命中率 > 90%

### 数据展示增强

- 上下文感知的集成编辑体验
  - 根据集成类型智能显示相关配置项
  - 必填字段标记与验证提示
  - 直观的配置卡片组织方式
- 改进的数据条件配置
  - 从数据源快速加载查询条件
  - 灵活的参数配置与验证
  - 实时数据预览声知

## 技术栈

### 后端技术

- Java 8+
- Spring Boot 2.7+
- MyBatis 3.5+
- Redis 6.0+

### 前端技术

- HTML5
- Tailwind CSS
- FontAwesome
- 第三方组件库

### 数据存储

- MySQL 8.0+
- Redis缓存

## High-Level System Architecture

### 业务模块架构

DataScope 系统采用前后端分离的架构，主要分为以下几个模块：

* 数据源管理模块：负责管理各种数据源的连接信息、元数据提取和同步。
* 查询引擎模块：负责解析用户查询，并从相应的数据源中获取数据。
* 低代码平台集成模块：负责与低代码平台进行集成，提供数据查询和展示功能。
* 系统管理模块：负责用户权限管理、系统配置等功能。

### Key Components

* 数据源连接器：负责连接各种数据源，并提供统一的数据访问接口。
* 查询解析器：负责解析用户输入的自然语言或 SQL 查询。
* 查询优化器：负责优化查询执行计划，提高查询效率. 
* 结果展示组件：负责将查询结果以表格或图表的形式展示给用户。

### 代码模块划分

项目代码结构如下：

* data-scope-app: 应用层，包含主要的业务逻辑。
* data-scope-domain: 领域层，包含领域模型和业务规则。
* data-scope-facade: 门面层，提供外部访问接口。
* data-scope-infrastructure: 基础设施层，提供数据库访问、缓存等基础设施。
* data-scope-main: 启动模块。
## 快速开始

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/yourusername/data-scope.git
cd data-scope
```

2. 配置数据库

```sql
create database data_scope;
```

3. 修改配置
```bash
cp src/main/resources/application.example.yml src/main/resources/application.yml
# 编辑application.yml配置数据库连接信息
```

4. 编译打包
```bash
mvn clean package
```

5. 运行应用
```bash
java -jar data-scope-main/target/data-scope.jar
```

### 访问系统

- 访问地址：http://localhost:8080
- 默认用户名：admin
- 默认密码：admin123

### 使用示例

```java
// 提取元数据
MetadataExtractor extractor = MetadataExtractor.builder()
    .dataSource(dataSource)
    .batchSize(1000)
    .build();

List<TableMetadata> metadata = extractor.extract();
```

## 项目结构

```
data-scope/
├── data-scope-app/          # 应用层
├── data-scope-domain/       # 领域层
├── data-scope-facade/       # 门面层
├── data-scope-infrastructure/# 基础设施层
├── data-scope-main/         # 启动模块
├── docs/                    # 项目文档
└── README.md               # 项目说明
```

## 文档说明

- [用户故事](docs/user_stories.md)
- [数据库设计](docs/database_design.md)
- [系统架构](docs/architecture/architecture-design.md)
- [API设计](docs/api_design.md)
- [低代码集成](docs/lowcode_integration.md)

## 开发指南

### 代码规范

- 遵循DDD架构设计
- 采用Java代码规范
- 使用统一的命名规则
- 编写完整的注释

### 提交规范

- feat: 新功能
- fix: 修复问题
- docs: 文档变更
- style: 代码格式
- refactor: 代码重构
- test: 测试相关
- chore: 其他修改

### 分支管理

- main: 主分支
- develop: 开发分支
- feature/*: 特性分支
- hotfix/*: 紧急修复分支

## 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify
```

## 部署

### Docker部署
```bash
docker build -t data-scope .
docker run -p 8080:8080 data-scope
```

### 配置说明

- application.yml: 应用配置

#### 元数据管理配置

```yaml
metadata:
  cache:
    enabled: true
    type: redis
    ttl: 3600
  extract:
    batch-size: 1000
    timeout: 30s
    retry:
      max-attempts: 3
      delay: 1s
```
- logback-spring.xml: 日志配置
- docker-compose.yml: 容器编排

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交修改
4. 推送到分支
5. 创建Pull Request

## 许可证

[MIT License](LICENSE)

## 联系方式

- 项目负责人：Your Name
- 邮箱：<EMAIL>
- 问题反馈：GitHub Issues

## 前端项目

项目现在包含两个前端实现：

### 正式环境
- 运行在端口8080
- 启动方式：
```bash
cd boss-data-scope2
npm install
npm run dev
```

### 测试环境
- 运行在端口8082
- 基于Vue 3.4.21 + TypeScript 5.2.2 + Ant Design Vue 4.0.3开发
- 已实现全新的Mock系统，支持更好的API模拟
- 启动方式：

```bash
cd boss-data-scope2
npm install
npm run dev          # 使用Mock数据启动（默认API地址：http://localhost:5000/api）
npm run dev:api      # 使用真实后端API启动（非Mock模式）
```

两个前端项目可以同时运行，互不干扰，以便进行平滑过渡。

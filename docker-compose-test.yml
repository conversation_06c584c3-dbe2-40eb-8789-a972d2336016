version: "3.8"

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: test
      MYSQL_DATABASE: test_db
    ports:
      - "3306:3306"
    healthcheck:
      test: [ "C<PERSON>", "mysqladmin", "ping", "-h", "localhost" ]
      interval: 5s
      timeout: 10s
      retries: 5

  db2:
    image: ibmcom/db2:11.5
    environment:
      DB2INST1_PASSWORD: test
      DBNAME: test_db
      LICENSE: accept
    ports:
      - "50000:50000"
    healthcheck:
      test: [ "/bin/sh", "-c", "su - db2inst1 -c 'db2 connect to test_db'" ]
      interval: 5s
      timeout: 10s
      retries: 5

  redis:
    image: redis:6.0
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "redis-cli", "ping" ]
      interval: 5s
      timeout: 10s
      retries: 5

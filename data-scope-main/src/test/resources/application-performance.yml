# 性能测试专用配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: metadata-service
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: [ 0.5, 0.75, 0.95, 0.99 ]

# 数据库连接池监控
datasource:
  hikari:
    metrics-name: metadata.datasource
    register-mbeans: true

# Redis监控
redis:
  metrics:
    enabled: true

# JVM监控
jvm:
  memory:
    max: 512MB

# 日志配置
logging:
  level:
    root: INFO
    org.springframework.web: INFO
    com.datascope: DEBUG
  file:
    name: logs/performance-test.log

# 性能测试参数
performance:
  test:
    batch-size: 1000
    concurrent-users: 50
    warmup-iterations: 3
    measurement-iterations: 10

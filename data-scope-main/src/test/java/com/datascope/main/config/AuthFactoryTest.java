package com.datascope.main.config;

import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.factory.AbstractAuthCenter;
import com.datascope.app.factory.AuthFactory;
import com.datascope.app.model.AuthResourceBO;
import com.datascope.app.service.AuthService;
import com.datascope.main.DataScopeApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = DataScopeApplication.class)
public class AuthFactoryTest {

    @Autowired
    private AuthFactory authFactory;

    @Autowired
    private AuthService authService;

    @Test
    public void authenticate() {
        AbstractAuthCenter auth = authFactory.getAuth(AuthTypeEnum.COLUMN.getCode());
        auth.process(new AuthDTO().setAuthRequired(true).setId("eed66d80f1ae495ba152458a4436c214"));
    }

    @Test
    public void getResource() {
        List<AuthResourceBO> resources = authService
            .getResources("tl:tl:TBL_RRS_REQ:AGENCY_NO", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJsb2dpbl90eXBlIjoiQUNDT1VOVCIsIm1vYmlsZSI6IjE4MTAxMzAzNzkwIiwibWlncmF0ZV91c2VyX2lkIjoiMTg1ODBhMzE5M2ZkNDc0YWExZTM2NzI3NTAzM2VlMjciLCJ4LWlwIjoiMTcyLjI1LjI1LjEwIiwicHJpbmNpcGFsX2lkIjoiNjUwNzIiLCJ0b2tlbiI6IjllYjZlYjNlLTBmZTctNDk5NS1iNTFmLTJmYWI2Nzg1NmYwNCIsImxvZ2luX25hbWUiOiJqaWFuZ3Rhby5zdSIsInR3b19mYWN0b3JfdmFsaWQiOmZhbHNlLCJsb2dpbl90aW1lIjoiMjAyNS0wNS0yMiAxMDozMDo1OSIsInNjb3BlIjoiIiwiY2FsbGJhY2siOiJodHRwczovL3FhdWlhc2VydmljZS55ZWVwYXkuY29tL3VpYS11aSMvcmVzb3VyY2UiLCJzc290aWNrZXQiOiI0ZTM2NzFjZS0zNjFkLTQyNzYtODgyOC01OGQzNzYyMDhmYjgiLCJleHAiOjE3NDc5Njc0NTksImlhdCI6MTc0Nzg3OTI1OSwiZW1haWwiOiJqaWFuZ3Rhby5zdUB5ZWVwYXkuY29tIiwidXNlcm5hbWUiOiLoi4_msZ_mtpsifQ.dSIHkSTMzMZUCpZSpcOQL9oLnSrhhehNZashJHKaKM8CckKmhbP9zqTJpBjWXXecPFMxL7vB4V6nvLiiPh7c-A");
        System.out.println(resources);
    }
}

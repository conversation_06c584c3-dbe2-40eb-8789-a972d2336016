package com.datascope.main.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * MyBatis-Plus 配置测试
 */
@SpringBootTest
public class MybatisPlusConfigTest {

    @Autowired
    private MybatisPlusInterceptor mybatisPlusInterceptor;

    @Test
    public void testInterceptorExists() {
        assertNotNull(mybatisPlusInterceptor, "MybatisPlusInterceptor should not be null");
    }
    
    @Test
    public void testWrapperClassesAvailable() {
        // 测试 QueryWrapper 是否可用
        QueryWrapper<?> queryWrapper = new QueryWrapper<>();
        assertNotNull(queryWrapper, "QueryWrapper should not be null");
        
        // 测试 LambdaQueryWrapper 是否可用
        LambdaQueryWrapper<?> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        assertNotNull(lambdaQueryWrapper, "LambdaQueryWrapper should not be null");
    }
} 
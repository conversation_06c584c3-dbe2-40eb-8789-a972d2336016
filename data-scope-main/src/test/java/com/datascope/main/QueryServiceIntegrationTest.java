package com.datascope.main;

import com.datascope.app.dto.query.QueryDTO;
import com.datascope.app.dto.query.SaveQueryParams;
import com.datascope.app.entity.Query;
import com.datascope.app.entity.QueryVersion;
import com.datascope.app.mapper.QueryMapper;
import com.datascope.app.mapper.QueryVersionMapper;
import com.datascope.app.service.QueryService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 查询服务集成测试类
 * 该测试将使用实际的数据库连接
 */
@SpringBootTest(classes = com.datascope.main.DataScopeApplication.class)
@ActiveProfiles("test") // 使用测试配置文件
@Transactional // 测试完成后自动回滚事务
public class QueryServiceIntegrationTest {

    @Autowired
    private QueryService queryService;

    @Autowired
    private QueryMapper queryMapper;

    @Autowired
    private QueryVersionMapper queryVersionMapper;

    private String testQueryId;
    private SaveQueryParams createQueryParams;

    @BeforeEach
    void setUp() {
        testQueryId = UUID.randomUUID().toString().replace("-", "");

        // 创建测试查询参数
        createQueryParams = SaveQueryParams.builder()
                .name("集成测试查询")
                .dataSourceId("ds-test-001")
                .sql("SELECT * FROM test_users")
                .description("集成测试用查询")
                .queryType("SQL")
                .status("DRAFT")
                .tags(Arrays.asList("test", "integration"))
                .build();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        try {
            Query query = queryMapper.selectById(testQueryId);
            if (query != null) {
                queryMapper.deleteById(testQueryId);
            }
        } catch (Exception e) {
            // 忽略清理过程中的异常
        }
    }

    @Test
    void testCreateQueryIntegration() {
        // 调用被测方法
        QueryDTO result = queryService.createQuery(createQueryParams);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(createQueryParams.getName(), result.getName());
        assertEquals(createQueryParams.getDataSourceId(), result.getDataSourceId());
        assertEquals(createQueryParams.getDescription(), result.getDescription());

        // 从数据库中查询确认
        Query savedQuery = queryMapper.selectById(result.getId());
        assertNotNull(savedQuery);
        assertEquals(createQueryParams.getName(), savedQuery.getName());
        assertEquals(createQueryParams.getDataSourceId(), savedQuery.getDataSourceId());

        // 验证是否创建了版本
        QueryVersion version = queryVersionMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<QueryVersion>()
                        .eq(QueryVersion::getQueryId, result.getId())
                        .eq(QueryVersion::getIsLatest, true)
        );

        assertNotNull(version);
        assertEquals(createQueryParams.getSql(), version.getSqlContent());
        assertEquals(1, version.getVersionNumber());
        assertEquals(true, version.getIsLatest());
    }
}

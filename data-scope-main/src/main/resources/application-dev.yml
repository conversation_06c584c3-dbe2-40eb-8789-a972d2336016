spring:
  # 数据库配置
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:data_scope;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
  # JPA configuration removed to resolve conflict with excluded HibernateJpaAutoConfiguration
  h2:
    console:
      enabled: true
      path: /h2-console

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 10000
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0

  # Flyway配置
  #flyway:
   # enabled: true

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  packages-to-scan: com.datascope.app

logging:
  level:
    root: DEBUG
    com.datascope: DEBUG
    org.springframework: INFO
    org.mybatis: DEBUG
  file:
    name: logs/data-scope.log
    path: logs
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"
  endpoint:
    health:
      show-details: "always"

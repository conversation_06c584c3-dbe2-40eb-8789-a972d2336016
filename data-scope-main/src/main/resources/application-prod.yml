spring:
  datasource:
    url: jdbc:mysql://***********:3306/datascope?useUnicode=true&characterEncoding=utf-8&useSSL=true&verifyServerCertificate=false&serverTimezone=GMT%2B8&useMonitor=false
    username: datascope
    password: datascopeABC123！
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}

logging:
  level:
    root: WARN
    com.datascope: INFO
    org.springframework: WARN
    org.mybatis: WARN
  file:
    name: logs/data-scope.log
    path: logs
    max-size: 100MB
    max-history: 30

springdoc:
  swagger-ui:
    enabled: false

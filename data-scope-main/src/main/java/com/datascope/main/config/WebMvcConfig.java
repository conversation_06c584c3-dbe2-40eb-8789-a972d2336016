package com.datascope.main.config;

import com.yeepay.g3.core.yuia.yuiacommons.patron.interceptors.*;
import com.yeepay.g3.core.yuia.yuiacommons.patronclient.DefaultPatronConfiguration;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.*;

/**
 * Web MVC Configuration
 * Configures Spring MVC components
 *
 * <AUTHOR>
 */
@EnableWebMvc
@Configuration
@RequiredArgsConstructor
@Import(DefaultPatronConfiguration.class)
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${insight.cors.allowed-origins:*}")
    private String allowedOrigins;

    @Value("${insight.cors.allowed-methods:*}")
    private String allowedMethods;

    @Value("${insight.cors.allowed-headers:*}")
    private String allowedHeaders;

    @Value("${insight.cors.allow-credentials:true}")
    private boolean allowCredentials;

    @Value("${insight.cors.max-age:3600}")
    private long maxAge;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
            .allowedOriginPatterns(allowedOrigins)
            .allowedMethods(allowedMethods)
            .allowedHeaders(allowedHeaders)
            .allowCredentials(allowCredentials)
            .maxAge(maxAge);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 静态资源映射
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
        // 只映射特定的静态资源路径，而不是使用/**
        registry.addResourceHandler("/css/**", "/js/**", "/images/**", "/fonts/**", "/favicon.ico")
               .addResourceLocations("classpath:/static/");

        // Swagger UI资源映射
        registry.addResourceHandler("/swagger-ui/**")
            .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
            .resourceChain(false);
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.setUseTrailingSlashMatch(true);
    }

    @Autowired
    private PatronContextInterceptor patronContextInterceptor;

    @Autowired
    private DefaultResourceInterceptor defaultResourceInterceptor;

    @Autowired
    private DefaultAuthenticationInterceptor defaultAuthenticationInterceptor;

    @Autowired
    private DefaultAuthorizationInterceptor defaultAuthorizationInterceptor;

    @Autowired
    private WebRequestLogInterceptor webRequestLogInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(patronContextInterceptor);
        registry.addInterceptor(defaultResourceInterceptor);
        registry.addInterceptor(defaultAuthenticationInterceptor);
        registry.addInterceptor(defaultAuthorizationInterceptor);
        registry.addInterceptor(webRequestLogInterceptor);
    }

}

package com.datascope.main.config;

import com.baomidou.dynamic.datasource.creator.DataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean;
import com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSource;
import com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

import java.util.List;

import static com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration.DRUID_ORDER;
import static com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration.JNDI_ORDER;

/**
 * MyBatis-Plus 配置类
 */
@Configuration
@MapperScan("com.datascope.app.mapper")
@Slf4j
public class MybatisPlusConfig {

    /**
     * 配置 mybatis dynamic datasource
     */
    @Bean
    @Order((JNDI_ORDER + DRUID_ORDER) / 2)
    public DataSourceCreator localDruidDataSource() {
        return new DataSourceCreator() {
            @Override
            public DataSource createDataSource(DataSourceProperty dataSourceProperty) {
                try {
                    final DataSourceFactoryBean factoryBean = new DataSourceFactoryBean();
                    factoryBean.setName(dataSourceProperty.getPoolName());
                    factoryBean.setPooledDataSourceFactory(new DruidPooledDataSourceFactory());
                    return (DataSource) factoryBean.getObject();
                } catch (Exception e) {
                    log.warn("fail to createDataSource, props: {}", dataSourceProperty, e);
                }
                return null;
            }

            @Override
            public boolean support(DataSourceProperty dataSourceProperty) {
                return dataSourceProperty.getType() == DruidPooledDataSource.class;
            }
        };
    }

    /**
     * 配置 MybatisPlusInterceptor 插件（分页等）
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 添加乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // 添加分页插件
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(1000L);
        interceptor.addInnerInterceptor(paginationInnerInterceptor);

        return interceptor;
    }

    @Bean
    public MybatisSqlSessionFactoryBean getSqlSessionFactoryBean(DataSource dataSource, List<Interceptor> plugins) throws Exception {
        final MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(pathMatchingResourcePatternResolver.getResources("classpath*:/mapper/**/*Mapper.xml"));
        sqlSessionFactoryBean.setFailFast(true);
        if (CollectionUtils.isNotEmpty(plugins)) {
            sqlSessionFactoryBean.setPlugins(plugins.toArray(new Interceptor[plugins.size()]));
        }
        return sqlSessionFactoryBean;
    }

    @Bean
    public JdbcTemplate myJdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
